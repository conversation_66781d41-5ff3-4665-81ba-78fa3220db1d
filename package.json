{"name": "vue-antd-pro", "version": "0.0.80", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "npm run release && vue-cli-service build && npm run generate:version", "build-dev": "vue-cli-service build --mode dev", "build-test": "vue-cli-service build --mode test", "postinstall": "patch-package", "generate:version": "generate-version-file dist public", "release": "standard-version --tag-force"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@antv/data-set": "^0.11.7", "@antv/g2": "^4.1.0", "@dimakorotkov/tinymce-mathjax": "^1.0.6", "@gausszhou/vue-drag-resize-rotate": "^2.0.15", "@microsoft/fetch-event-source": "^2.0.1", "@tinymce/tinymce-vue": "^3.2.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@wangeditor/plugin-formula": "^1.0.11", "animate.css": "^4.1.0", "ant-design-vue": "^1.6.0", "axios": "^0.20.0", "benz-amr-recorder": "^1.1.5", "cache-loader": "^4.1.0", "cli-plugin-eslint": "^0.0.1", "core-js": "^2.6.5", "dayjs": "^1.11.10", "easy-typer-js": "^2.1.0", "echarts": "^4.7.0", "echarts-liquidfill": "^2.0.6", "echarts-stat": "^1.2.0", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.15.0", "enquire.js": "^2.1.6", "esdk-obs-browserjs": "^3.24.3", "exceljs": "^4.4.0", "fabric": "^5.3.0", "fabric-with-erasing": "^1.0.1", "file-saver": "^2.0.5", "highlight.js": "^11.11.1", "html-to-image": "^1.9.0", "html2canvas": "^1.1.4", "htmlparser2": "^6.1.0", "jquery": "^3.7.1", "js-audio-recorder": "^1.0.6", "js-base64": "^3.6.0", "js-cookie": "^2.2.0", "jspdf": "^2.3.1", "katex": "^0.13.24", "lamejs": "^1.2.1", "lib-flexible": "^0.3.2", "lodash": "^4.17.21", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "lucky-canvas": "latest", "mammoth": "^1.9.0", "markdown-it": "^12.1.0", "markmap-common": "0.15.3", "markmap-lib": "0.15.4", "markmap-view": "0.15.4", "mathjax": "^3.2.0", "md5": "^2.2.1", "mermaid": "^8.14.0", "moment": "^2.24.0", "node-uuid": "^1.4.8", "nprogress": "^0.2.0", "postcss-import": "^12.0.1", "postcss-px2rem": "^0.3.0", "postcss-url": "^8.0.0", "pptxjs": "^0.0.0", "px2rem-loader": "^0.1.9", "save": "^2.4.0", "screenfull": "^5.0.2", "splitpanes": "^2.4.1", "swiper": "^6.1.2", "tinymce": "^5.5.1", "version-rocket": "^1.7.1", "vue": "^2.5.22", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.2.1", "vue-cropper": "^0.5.5", "vue-directive-image-previewer": "^2.2.2", "vue-drag-resize": "^1.5.4", "vue-easytable": "^2.24.0", "vue-fabric": "^0.1.40", "vue-json-excel": "^0.2.98", "vue-katex": "^0.5.0", "vue-katex-auto-render": "^0.1.3", "vue-ls": "^3.2.0", "vue-mathjax": "0.0.11", "vue-pdf": "^4.3.0", "vue-photo-preview": "^1.1.3", "vue-resource": "^1.5.1", "vue-router": "3", "vue-svg-component-runtime": "^1.0.1", "vue-video-player": "^5.0.2", "vuex": "^3.1.0", "wangeditor": "^3.1.1", "ws": "^8.18.0", "xlsx": "^0.18.5", "xmldom": "^0.3.0"}, "devDependencies": {"@ant-design/colors": "^3.1.0", "@babel/core": "^7.11.6", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.11.5", "@vue/cli-plugin-babel": "3.7.0", "@vue/cli-plugin-unit-jest": "^3.7.0", "@vue/cli-service": "3.7.0", "@vue/eslint-config-standard": "4.0.0", "@vue/test-utils": "^1.0.0-beta.20", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^23.6.0", "babel-loader": "^8.1.0", "babel-plugin-import": "^1.11.0", "babel-preset-env": "^1.7.0", "eslint": "^7.0.0", "eslint-plugin-html": "5.0.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.8.1", "less-loader": "^5.0.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "patch-package": "^6.5.1", "sass-loader": "^9.0.3", "speed-measure-webpack-plugin": "^1.5.0", "standard-version": "^9.5.0", "ts-loader": "~8.2.0", "typescript": "~4.5.0", "vconsole": "^3.15.1", "vue-happy-scroll": "^2.1.1", "vue-loader": "^15.9.7", "vue-style-loader": "^4.1.3", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.5.22", "vuedraggable": "^2.24.3", "webpack": "^4.44.1", "webpack-bundle-analyzer": "^3.6.0", "webpack-theme-color-replacer": "^1.2.15"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"eslint-disable-next-line": 0, "generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0, "no-tabs": 0, "quotes": [0, "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "semi": [0, "never", {"beforeStatementContinuationChars": "never"}], "no-delete-var": 2, "prefer-const": [2, {"ignoreReadBeforeAssign": false}], "indent": [0], "space-before-function-paren": [0]}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "collective": {"type": "opencollective", "url": "https://opencollective.com/ant-design-pro-vue"}, "volta": {"node": "14.19.1", "yarn": "1.22.4"}}