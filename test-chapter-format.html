<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节数据格式转换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        button {
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #66b1ff;
        }
        .success {
            background: #67c23a;
        }
        .success:hover {
            background: #85ce61;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>章节数据格式转换测试</h2>
        
        <div class="section">
            <h3>原始数据格式 (chapterList)</h3>
            <pre id="originalData"></pre>
        </div>
        
        <div class="section">
            <h3>转换后数据格式 (ChapterInfo)</h3>
            <pre id="formattedData"></pre>
        </div>
        
        <div class="section">
            <button onclick="testConversion()">测试转换</button>
            <button onclick="addTestData()" class="success">添加测试数据</button>
            <button onclick="clearData()">清空数据</button>
        </div>
    </div>

    <script>
        // 模拟原始章节数据
        let chapterList = [
            {
                id: 1,
                name: '第一章 基础知识',
                editing: false,
                expand: true,
                children: [
                    {
                        id: 2,
                        name: '1.1 概念介绍',
                        editing: false,
                        expand: true,
                        children: [
                            {
                                id: 3,
                                name: '1.1.1 基本概念',
                                editing: false,
                                expand: false,
                                children: []
                            },
                            {
                                id: 4,
                                name: '1.1.2 重要定理',
                                editing: false,
                                expand: false,
                                children: []
                            }
                        ]
                    },
                    {
                        id: 5,
                        name: '1.2 实践应用',
                        editing: false,
                        expand: false,
                        children: [
                            {
                                id: 6,
                                name: '1.2.1 案例分析',
                                editing: false,
                                expand: false,
                                children: []
                            }
                        ]
                    }
                ]
            },
            {
                id: 7,
                name: '第二章 进阶内容',
                editing: false,
                expand: false,
                children: [
                    {
                        id: 8,
                        name: '2.1 高级概念',
                        editing: false,
                        expand: false,
                        children: []
                    }
                ]
            }
        ];

        // 递归转换章节数据格式的函数
        function formatChapterData(chapters) {
            return chapters.map((chapter, index) => {
                const formattedChapter = {
                    ChapterName: chapter.name,
                    chapter: chapter.children && chapter.children.length > 0 ? formatChapterData(chapter.children) : [],
                    Orderid: index + 1
                }
                return formattedChapter
            })
        }

        function displayData() {
            document.getElementById('originalData').textContent = JSON.stringify(chapterList, null, 2);
            
            const formattedData = formatChapterData(chapterList);
            document.getElementById('formattedData').textContent = JSON.stringify(formattedData, null, 2);
        }

        function testConversion() {
            console.log('原始数据:', chapterList);
            
            const formattedData = formatChapterData(chapterList);
            console.log('转换后数据:', formattedData);
            
            // 验证数据结构
            function validateChapter(chapter, level = 1) {
                const hasRequiredFields = 
                    typeof chapter.ChapterName === 'string' &&
                    Array.isArray(chapter.chapter) &&
                    typeof chapter.Orderid === 'number';
                
                if (!hasRequiredFields) {
                    console.error(`第${level}级章节数据格式错误:`, chapter);
                    return false;
                }
                
                // 递归验证子章节
                for (let i = 0; i < chapter.chapter.length; i++) {
                    if (!validateChapter(chapter.chapter[i], level + 1)) {
                        return false;
                    }
                }
                
                return true;
            }
            
            let isValid = true;
            for (let i = 0; i < formattedData.length; i++) {
                if (!validateChapter(formattedData[i])) {
                    isValid = false;
                    break;
                }
            }
            
            if (isValid) {
                alert('✅ 数据格式转换成功！所有章节都包含 ChapterName、chapter、Orderid 字段');
            } else {
                alert('❌ 数据格式转换失败！请检查控制台错误信息');
            }
            
            displayData();
        }

        function addTestData() {
            chapterList = [
                {
                    id: 1,
                    name: '第一章 基础知识',
                    editing: false,
                    expand: true,
                    children: [
                        {
                            id: 2,
                            name: '1.1 概念介绍',
                            editing: false,
                            expand: true,
                            children: [
                                {
                                    id: 3,
                                    name: '1.1.1 基本概念',
                                    editing: false,
                                    expand: false,
                                    children: []
                                },
                                {
                                    id: 4,
                                    name: '1.1.2 重要定理',
                                    editing: false,
                                    expand: false,
                                    children: []
                                }
                            ]
                        },
                        {
                            id: 5,
                            name: '1.2 实践应用',
                            editing: false,
                            expand: false,
                            children: [
                                {
                                    id: 6,
                                    name: '1.2.1 案例分析',
                                    editing: false,
                                    expand: false,
                                    children: []
                                }
                            ]
                        }
                    ]
                },
                {
                    id: 7,
                    name: '第二章 进阶内容',
                    editing: false,
                    expand: false,
                    children: [
                        {
                            id: 8,
                            name: '2.1 高级概念',
                            editing: false,
                            expand: false,
                            children: []
                        }
                    ]
                },
                {
                    id: 9,
                    name: '第三章 高级应用',
                    editing: false,
                    expand: false,
                    children: []
                }
            ];
            
            displayData();
            alert('已添加测试数据');
        }

        function clearData() {
            chapterList = [];
            displayData();
            alert('已清空数据');
        }

        // 初始化显示
        displayData();
    </script>
</body>
</html>
