<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识点Excel导入测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #66b1ff;
        }
        .success {
            background: #67c23a;
        }
        .success:hover {
            background: #85ce61;
        }
        .warning {
            background: #e6a23c;
        }
        .warning:hover {
            background: #ebb563;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
            max-height: 300px;
            overflow-y: auto;
        }
        .knowledge-tree {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .knowledge-node {
            margin-bottom: 5px;
        }
        .node-content {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
            background: white;
            border: 1px solid #e0e0e0;
            cursor: pointer;
        }
        .node-content:hover {
            background: #f0f9ff;
            border-color: #409eff;
        }
        .expand-icon {
            margin-right: 8px;
            width: 16px;
            color: #666;
            cursor: pointer;
            user-select: none;
        }
        .node-text {
            flex: 1;
            font-weight: 500;
            color: #333;
        }
        .node-code {
            font-size: 12px;
            color: #999;
            margin-left: 8px;
        }
        .children {
            margin-left: 24px;
            margin-top: 5px;
        }
        .level-1 .node-content {
            background: #e8f4fd;
            border-color: #409eff;
        }
        .level-1 .node-text {
            color: #409eff;
            font-weight: 600;
        }
        .level-2 .node-content {
            background: #f0f9ff;
            border-color: #67c23a;
        }
        .level-2 .node-text {
            color: #67c23a;
            font-weight: 500;
        }
        .level-3 .node-content {
            background: #f6ffed;
            border-color: #e6a23c;
        }
        .level-3 .node-text {
            color: #e6a23c;
        }
        .level-4 .node-content {
            background: #fff7e6;
            border-color: #f56c6c;
        }
        .level-4 .node-text {
            color: #f56c6c;
        }
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .example-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>知识点Excel导入功能测试</h2>
        
        <div class="section">
            <h3>Excel文件格式说明</h3>
            <p>Excel文件应按以下格式组织，每列代表一个层级：</p>
            <table class="example-table">
                <thead>
                    <tr>
                        <th>第一列（第一级）</th>
                        <th>第二列（第二级）</th>
                        <th>第三列（第三级）</th>
                        <th>第四列（第四级）</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>知识点1</td>
                        <td>知识点1.1</td>
                        <td>知识点1.1.1</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td>知识点1.1.2</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>知识点1.2</td>
                        <td>知识点1.2.1</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>知识点2</td>
                        <td>知识点2.1</td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
            <p><strong>编码规则：</strong>第一级为 1, 2, 3...，第二级为 1.1, 1.2, 2.1...，第三级为 1.1.1, 1.1.2, 1.2.1...</p>
        </div>
        
        <div class="section">
            <h3>文件导入测试</h3>
            <input type="file" id="fileInput" accept=".xlsx,.xls" />
            <br>
            <button onclick="createSampleExcel()" class="success">生成示例Excel文件</button>
            <button onclick="clearData()" class="warning">清空数据</button>
        </div>
        
        <div class="section">
            <h3>原始Excel数据</h3>
            <pre id="rawData">请选择Excel文件...</pre>
        </div>
        
        <div class="section">
            <h3>解析后的知识点树</h3>
            <div id="knowledgeTree" class="knowledge-tree">请先导入Excel文件...</div>
        </div>
        
        <div class="section">
            <h3>最终数据结构（form.Subset格式）</h3>
            <pre id="finalData">等待数据解析...</pre>
        </div>
    </div>

    <script>
        let subset = []

        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0]
            if (!file) return
            
            const fileName = file.name.toLowerCase()
            if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                alert('请选择Excel文件（.xlsx或.xls格式）')
                return
            }
            
            parseExcelFile(file)
        })

        function parseExcelFile(file) {
            const reader = new FileReader()
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result)
                    const workbook = XLSX.read(data, { type: 'array' })
                    
                    const firstSheetName = workbook.SheetNames[0]
                    const worksheet = workbook.Sheets[firstSheetName]
                    
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                        header: 1,
                        defval: ''
                    })
                    
                    console.log('Excel原始数据:', jsonData)
                    document.getElementById('rawData').textContent = JSON.stringify(jsonData, null, 2)
                    
                    buildKnowledgeTree(jsonData)
                    
                } catch (error) {
                    console.error('解析Excel文件失败:', error)
                    alert('解析Excel文件失败，请检查文件格式')
                }
            }
            
            reader.onerror = () => {
                alert('读取文件失败')
            }
            
            reader.readAsArrayBuffer(file)
        }

        function buildKnowledgeTree(excelData) {
            if (!excelData || excelData.length === 0) {
                alert('Excel文件为空')
                return
            }
            
            const validRows = excelData.filter(row => 
                row && row.some(cell => cell && cell.toString().trim() !== '')
            )
            
            if (validRows.length === 0) {
                alert('Excel文件中没有有效数据')
                return
            }
            
            console.log('有效数据行:', validRows)
            
            const newSubset = []
            let sortCounter = 1
            const levelParents = {}
            const levelCodes = {}
            
            validRows.forEach((row, rowIndex) => {
                for (let colIndex = 0; colIndex < row.length; colIndex++) {
                    const cellValue = row[colIndex]
                    if (!cellValue || cellValue.toString().trim() === '') {
                        continue
                    }
                    
                    const knowledgeName = cellValue.toString().trim()
                    const level = colIndex + 1
                    
                    // 生成编码
                    let codeId = ''
                    if (level === 1) {
                        if (!levelCodes[1]) {
                            levelCodes[1] = 0
                        }
                        levelCodes[1]++
                        codeId = levelCodes[1].toString()
                        
                        // 重置下级编码
                        for (let i = 2; i <= 10; i++) {
                            levelCodes[i] = 0
                        }
                    } else {
                        const parentLevel = level - 1
                        const parent = levelParents[parentLevel]
                        
                        if (!parent) {
                            console.warn(`第${rowIndex + 1}行第${colIndex + 1}列: 找不到第${parentLevel}级父节点，跳过"${knowledgeName}"`)
                            continue
                        }
                        
                        if (!levelCodes[level]) {
                            levelCodes[level] = 0
                        }
                        levelCodes[level]++
                        
                        // 重置下级编码
                        for (let i = level + 1; i <= 10; i++) {
                            levelCodes[i] = 0
                        }
                        
                        codeId = parent.CodeId + '.' + levelCodes[level]
                    }
                    
                    const knowledge = {
                        CodeContent: knowledgeName,
                        Sort: sortCounter++,
                        CodeId: codeId,
                        Level: level,
                        Subset: []
                    }
                    
                    if (level === 1) {
                        const existingKnowledge = newSubset.find(k => k.CodeContent === knowledgeName)
                        if (!existingKnowledge) {
                            newSubset.push(knowledge)
                            levelParents[1] = knowledge
                        } else {
                            levelParents[1] = existingKnowledge
                        }
                    } else {
                        const parentLevel = level - 1
                        const parent = levelParents[parentLevel]
                        
                        if (parent) {
                            const existingChild = parent.Subset.find(k => k.CodeContent === knowledgeName)
                            if (!existingChild) {
                                parent.Subset.push(knowledge)
                                levelParents[level] = knowledge
                            } else {
                                levelParents[level] = existingChild
                            }
                        }
                    }
                }
            })
            
            subset = newSubset
            
            console.log('构建的知识点树:', subset)
            
            displayKnowledgeTree()
            document.getElementById('finalData').textContent = JSON.stringify(subset, null, 2)
            
            alert(`成功导入 ${validRows.length} 行数据，共生成 ${countTotalKnowledge(newSubset)} 个知识点`)
        }

        function displayKnowledgeTree() {
            const container = document.getElementById('knowledgeTree')
            container.innerHTML = ''
            
            function renderKnowledge(knowledge, level = 1) {
                const nodeDiv = document.createElement('div')
                nodeDiv.className = `knowledge-node level-${level}`
                
                const contentDiv = document.createElement('div')
                contentDiv.className = 'node-content'
                
                const expandIcon = document.createElement('span')
                expandIcon.className = 'expand-icon'
                if (knowledge.Subset && knowledge.Subset.length > 0) {
                    expandIcon.textContent = '▼'
                    expandIcon.onclick = () => toggleExpand(nodeDiv, expandIcon)
                } else {
                    expandIcon.innerHTML = '&nbsp;'
                }
                
                const textSpan = document.createElement('span')
                textSpan.className = 'node-text'
                textSpan.textContent = knowledge.CodeContent
                
                const codeSpan = document.createElement('span')
                codeSpan.className = 'node-code'
                codeSpan.textContent = `(${knowledge.CodeId})`
                
                contentDiv.appendChild(expandIcon)
                contentDiv.appendChild(textSpan)
                contentDiv.appendChild(codeSpan)
                nodeDiv.appendChild(contentDiv)
                
                if (knowledge.Subset && knowledge.Subset.length > 0) {
                    const childrenDiv = document.createElement('div')
                    childrenDiv.className = 'children'
                    
                    knowledge.Subset.forEach(child => {
                        childrenDiv.appendChild(renderKnowledge(child, level + 1))
                    })
                    
                    nodeDiv.appendChild(childrenDiv)
                }
                
                return nodeDiv
            }
            
            subset.forEach(knowledge => {
                container.appendChild(renderKnowledge(knowledge))
            })
        }

        function toggleExpand(nodeDiv, expandIcon) {
            const childrenDiv = nodeDiv.querySelector('.children')
            if (childrenDiv) {
                if (childrenDiv.style.display === 'none') {
                    childrenDiv.style.display = 'block'
                    expandIcon.textContent = '▼'
                } else {
                    childrenDiv.style.display = 'none'
                    expandIcon.textContent = '▶'
                }
            }
        }

        function countTotalKnowledge(knowledgeList) {
            let count = 0
            knowledgeList.forEach(knowledge => {
                count += 1
                if (knowledge.Subset && knowledge.Subset.length > 0) {
                    count += countTotalKnowledge(knowledge.Subset)
                }
            })
            return count
        }

        function createSampleExcel() {
            const sampleData = [
                ['知识点1', '知识点1.1', '知识点1.1.1', ''],
                ['', '', '知识点1.1.2', ''],
                ['', '知识点1.2', '知识点1.2.1', ''],
                ['', '', '知识点1.2.2', ''],
                ['知识点2', '知识点2.1', '', ''],
                ['', '知识点2.2', '知识点2.2.1', ''],
                ['', '', '知识点2.2.2', ''],
                ['知识点3', '知识点3.1', '知识点3.1.1', ''],
                ['', '', '知识点3.1.2', ''],
                ['', '', '知识点3.1.3', '']
            ]
            
            const ws = XLSX.utils.aoa_to_sheet(sampleData)
            const wb = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(wb, ws, "知识点结构")
            
            XLSX.writeFile(wb, "知识点导入示例.xlsx")
            alert('示例Excel文件已生成并下载')
        }

        function clearData() {
            subset = []
            document.getElementById('rawData').textContent = '请选择Excel文件...'
            document.getElementById('knowledgeTree').textContent = '请先导入Excel文件...'
            document.getElementById('finalData').textContent = '等待数据解析...'
            document.getElementById('fileInput').value = ''
        }
    </script>
</body>
</html>
