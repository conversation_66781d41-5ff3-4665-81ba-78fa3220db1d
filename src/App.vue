<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <router-view />
    </a-config-provider>
    <!--  帮助地址  -->
    <div id="drag1" v-drag class="bangzhu" v-if="$store.state.LoginSource == 2">
      <div @click="linkClick">帮助</div>
      <!-- <el-link href="https://uwoo.obs.cn-east-2.myhuaweicloud.com/teacher-handbook.pdf" :underline="false" target="_blank">帮助</el-link> -->
    </div>
  </div>
</template>

<script>
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { AppDeviceEnquire } from '@/utils/mixin'
import TokenCache from '@/utils/cache/TokenCache'
import { checkVersion } from 'version-rocket'
import { version } from '../package.json'
const TY_path = [
  { type_v: 'baosan', path: '/Base_Manage/Home/WxSubmitDSLogin' },
  { type_v: 'xuhui', path: '/Base_Manage/Home/XuHuiSubmitDSLogin' },
  { type_v: 'shanghai', path: '/Base_Manage/Home/SubmitDSLogin' },
]

export default {
  mixins: [AppDeviceEnquire],
  data() {
    return {
      locale: zhCN,
      Token: '',
      name: '',
    }
  },
  directives: {
    drag (el) {
      const oDiv = el
      // 拖拽时间标识
      let firstTime = ''
      let lastTime = ''
      // *************这里不要开启会影响填空题同义答案系统键盘输入问题
      // document.onselectstart = function () {
      //   return false
      // }
      let odiv = el;   //获取当前元素
      el.onmousedown = (e) => {
        document.getElementById('drag1').setAttribute('drag-flag', false)
        firstTime = new Date().getTime()
        //算出鼠标相对元素的位置
        let disX = e.clientX - odiv.offsetLeft;
        let disY = e.clientY - odiv.offsetTop;
        let left = '';
        let top = '';
        document.onmousemove = (e)=>{
            //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
             left = e.clientX - disX;
            top = e.clientY - disY;
            //绑定元素位置到positionX和positionY上面
            //移动当前元素
            odiv.style.left = left + 'px';
            odiv.style.top = top + 'px';

          // 判断下当前时间与初始时间差，大于200毫秒则判断状态为拖拽
          lastTime = new Date().getTime()
          if (lastTime - firstTime > 200) {
            document.getElementById('drag1').setAttribute('drag-flag', true)
          }
        };
        document.onmouseup = (e) => {
            document.onmousemove = null;
            document.onmouseup = null;
        };
      };
    }
  },
  created() {
    let V_S_F_A_L_O = localStorage.getItem('V_S_F_A_L_O')
    let versionForAutoLoginOut = this.$store.state.versionForAutoLoginOut
    if (!V_S_F_A_L_O) localStorage.setItem('V_S_F_A_L_O', versionForAutoLoginOut)
    this.getUrlParams()
    // checkVersion({
    //   localPackageVersion: version,
    //   originVersionFileUrl: `${location.origin}/version.json`,
    //   pollingTime:1000 * 30
    // },{
    //   buttonText:'立即升级',
    // })
  },
  watch: {
    $route() {
      const Home = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Home/Index?' + Home) {
        this.getUrlParams()
      }
    },
  },
  mounted() {
    var reg = /^(192.168.110.|192.168.1.|**************)/
    if (window.location.hostname !== 'localhost' && !reg.test(window.location.hostname)) {
      // 判断非本地server时 http强制转换成https
      var targetProtocol = 'https:'
      if (window.location.protocol != targetProtocol)
        window.location.href = targetProtocol + window.location.href.substring(window.location.protocol.length)
    }
    if (document.location.href === 'https://www.eduwon.cn/') {
      // if (document.location.href === 'http://***************:5001/') {
      window.addEventListener('resize', () => {})

      if (this._isMobile()) {
        this.$router.push('/MobileHome/Mobindex')
      } else {
        this.$router.push('/Index/index')
      }
    }
    this.GetHelpVideos()
    // window.addEventListener('resize', () => {})
    // if (this._isMobile()) {
    //   this.$router.push('/MobileHome/Mobindex')

    // } else {
    //   this.$router.push('/Index/index')

    // }
  },
  methods: {
    //获取指导视频路径（有不调用，没有才调）
    async GetHelpVideos() {
      if (localStorage.getItem('UW_HELPVIDEOS') == 'undefined' || !localStorage.getItem('UW_HELPVIDEOS')) {
        const res = await this.$uwonhttp.get('/guide/guide/GetGuideHelpVideoList')
        localStorage.setItem('UW_HELPVIDEOS', JSON.stringify(res.data.Data))
      }
    },
    //获取教师多学科
    async GetSubjectList(subject) {
      const res = await this.$uwonhttp.post('/Period_Subject/Period_Subject/GetSubjectListByUser', {
        UserId: localStorage.getItem('UserId'),
        subject,
      })
      if (!localStorage.getItem('UW_SUBJECT')) {
        const Data = res.data.Data
        localStorage.setItem('UW_SUBJECT', Data.find((item) => item.JX == subject).Id)
      } else {
        // 根据数字基座返回参数获取默认学科
        localStorage.setItem('UW_SUBJECT', res.data.Data.Id)
      }
    },
    getUrlParams() {
      const Token = this.$route.query.token ?? '' // 平台token
      const name = this.$route.query.n ?? ''
      const type = this.$route.query.type ?? ''
      const subject = this.$route.query.subject ?? ''
      const stage = this.$route.query.stage ?? ''
      const sourceToken = this.$route.query.sourceToken ?? '' // 三个助手公认token
      if(type =="huangpu"){
        if (Token && name && type && subject) {
          var array = Token.split(' ')
          var token = array.join('+')
          localStorage.setItem('PL_newP', '1')
          TokenCache.setToken(token)
          localStorage.removeItem('UW_SUBJECT')
          localStorage.removeItem('UW_CODE')
          this.GetUserInfoByUserName(name, subject)
          this.$store.commit('user/SET_USER_ROLE',true)
        }
      }else if(type == "xuhui"){
        // 徐汇数字基座
        if (Token && type) {
          var array = Token.split(' ')
          var token = array.join('+')
          localStorage.setItem('PL_newP', '1')
          localStorage.setItem('isXH', 1),
          TokenCache.setToken(token)
          localStorage.removeItem('UW_SUBJECT')
          localStorage.removeItem('UW_CODE')
          this.GetUserInfoByUserName(null)
          this.$store.commit('user/SET_USER_ROLE',true)
        }
      }else{
        if (Token && name && type && subject && sourceToken) {
        //处理新token
        var array = Token.split(' ')
        // var token = array[0] 原来
        var token = array.join('+')
        if (type == 'shanghai') {
          localStorage.setItem('PL_newP', '2')
        } else {
          localStorage.setItem('PL_newP', '1')
        }
        // 根据三个助手传过来URL存储学段值: P5小学，J4初中，S3高中
        localStorage.setItem('STAGE', stage)
        sessionStorage.setItem('sourceToken', sourceToken)
        sessionStorage.removeItem('Level')
        this.SubmitDSLogin(token, name, type, subject,stage)
      }
      }
      // if (Token && name && type && subject && sourceToken) {
      //   //处理新token
      //   var array = Token.split(' ')
      //   // var token = array[0] 原来
      //   var token = array.join('+')
      //   if (type == 'shanghai') {
      //     localStorage.setItem('PL_newP', '2')
      //   } else {
      //     localStorage.setItem('PL_newP', '1')
      //   }
      //   // 根据三个助手传过来URL存储学段值: P5小学，J4初中，S3高中
      //   localStorage.setItem('STAGE', stage)
      //   sessionStorage.setItem('sourceToken', sourceToken)
      //   this.SubmitDSLogin(token, name, type, subject)
      // } else {
      // }
    },
    //统一用户登录
    async SubmitDSLogin(Token, name, type, subject,stage) {
      let params = {
        tokend: Token,
        stage
      }
      let path = '/Base_Manage/Home/SubmitDSLogin'
      let find_path = TY_path.find((item) => item.type_v == type)
      if (find_path !== undefined) path = find_path.path
      await this.$http.post(path, params).then((res) => {
        if (res.Success) {
          TokenCache.setToken(res.Data)
          localStorage.removeItem('UW_SUBJECT')
          localStorage.removeItem('UW_CODE')
          this.GetUserInfoByUserName(name, subject)
          let GetOut_url = Token
          localStorage.setItem('GetOut_url', GetOut_url)
        } else {
          this.$message.error(res.Msg)
        }
      })
    },

    async GetUserInfoByUserName(name, subject) {
      const res = await this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: name })
      if (res.Success) {
        if (res.Data.Roles != null) {
          this.$store.commit('set_Level', sessionStorage.getItem('Level') ? sessionStorage.getItem('Level') : res.Data.Roles[0].Level)
          localStorage.setItem('role', res.Data.Roles[0].Id)
          localStorage.setItem('SchoolId', res.Data.User.SchoolId)
          if (
            res.Data.Roles.some((item) => {
              return item.RoleName === '校领导'
            })
          ) {
            this.isSchoolHeader = true
            localStorage.setItem('isSchoolHeader', this.isSchoolHeader)
            localStorage.removeItem('isTeacher')
            localStorage.removeItem('isTeachingStaff')
            localStorage.setItem('role', res.Data.Roles[0].Id)
          }
        }
        if (!res.Data.Roles) {
          this.isStudent = '1'
          localStorage.setItem('isStudent', this.isStudent)
          localStorage.removeItem('isTeacher')
        } else {
          this.isTeacher = res.Data.Roles.some((item) => {
            return item.RoleName === '教师'
          })
          localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
          localStorage.setItem('isTeacher', this.isTeacher)
          localStorage.removeItem('isStudent')
          localStorage.setItem('role', res.Data.Roles[0].Id)
        }
        this.id = res.Data.UserType
        localStorage.setItem('Id', res.Data.UserType)
        localStorage.setItem('UserId', res.Data.User.Id)
        localStorage.setItem('IsVip', res.Data.IsVip)
        localStorage.setItem('IsDZBQX', res.Data.IsDZBQX)
        localStorage.setItem('LoginSource', res.Data.LoginSource) // 1:专科专练登录,2:三个助手登录
        // localStorage.setItem('LoginSource', res.Data.LoginSource) // 1:专科专练登录,2:三个助手登录
        this.$store.commit('set_LoginSource', res.Data.LoginSource)
        this.GetSubjectList(subject)
        if(res.Data.LoginSource == 1){
          this.$router.push({
            path: '/Home/Introduce',
            query: {
              id: this.id,
            },
          })
        } else {
          this.$router.push({
            path: '/threeAssistants/newHome/index',
          })
        }
      }
    },

    toQRLogin() {
      this.$router.push('/Compage/QRLogin')
    },
    _isMobile() {
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      )
      return flag
    },
    linkClick(){
      // 点击事件触发时，判断当前状态是拖拽还是点击，若是拖拽，直接返回不继续执行
      const isDrag = document.getElementById('drag1').getAttribute('drag-flag')
      if (isDrag === 'true') {
        return
      }
      window.open('https://uwoo.obs.cn-east-2.myhuaweicloud.com/teacher-handbook.pdf', '_blank');
    }
  },
}
</script>
<style lang="less">
// @import url('//at.alicdn.com/t/c/font_4160151_fa406i0dt67.css');
@import url('//at.alicdn.com/t/c/font_4160151_corzdrlf0sf.css');

// @import "~@/assets/font/iconfont.css";
#app {
  height: 100%;
}
.ant-btn-primary {
  background-color: #68bb97;
  border-color: #68bb97;
}
.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #68bb97;
  border-color: #68bb97;
}
.ant-btn:hover,
.ant-btn:focus {
  color: #fff;
  background-color: #ddd;
  border-color: #ddd;
}
/deep/.ant-radio-checked .ant-radio-inner::after {
  background-color: #68bb97;
}

._scrollbar {
  & > .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: scroll;
    & > .el-scrollbar__view {
      // min-height: 100%;
      margin: 0 10px 10px;
    }
  }
  & > .el-scrollbar__bar.is-vertical {
    width: 4px;
    right: 0;
    z-index: 4;
    .el-scrollbar__thumb {
      background: rgba(0, 0, 0, 0.2);
    }
    .el-scrollbar__thumb:hover {
      background: rgba(0, 0, 0, 0.2);
    }
  }
}
// 帮助样式
.bangzhu{
  width: 70px;
  height: 70px;
  border-radius: 50%;
  text-align: center;
  line-height: 70px;
  background: rgb(86,133,237);
  position: fixed;
  bottom: 50px;
  left: 50px;
  color: #FFFFFF;
  font-size: 20px;
  z-index: 100;
  .el-link{
    font-size: 20px;
    .el-link--inner{
      color: #FFFFFF;
    }
  }
}
</style>
<style>
/* .el-message-box{
    width:3.6rem !important;
  } */
</style>