<script>
export default {
  name: 'index',
  data() {
    return {
      visible: false,
      total: 0,
      QuestionTypeInfos: [],
      QuestionInfos: [],
    }
  },
  created() {
    this.GetQuestionItemBasketInfo()
  },
  methods:{
    // 生成作业
    generateAJob(){
      const IdList = this.QuestionInfos.map(item => item.QuestionId)
      IdList.length  > 0 ? this.$emit('generateAJob',IdList) : this.$message.error('请选择题目')
    },
    // 保存题目到题目栏
    async SaveQuestionItemBasket(QuestionId){
      const { data:res } = await this.$uwonhttp.post('/Question/Question/SaveQuestionItemBasket',{ UserId: localStorage.getItem('UserId'), QuestionId })
      console.log(res)
      if(res.Success){
        this.GetQuestionItemBasketInfo()
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取题目栏数据
    async GetQuestionItemBasketInfo(){
      const { data:res } = await this.$uwonhttp.get('/Question/Question/GetQuestionItemBasketInfo',{ UserId: localStorage.getItem('UserId'), subjectId: localStorage.getItem('UW_SUBJECT') })
      // console.log(res)
      if(res.Success){
        const { QuestionTypeInfos, QuestionInfos , Total } = res.Data
        this.total = Total
        this.QuestionTypeInfos = QuestionTypeInfos !== null ? QuestionTypeInfos : []
        this.QuestionInfos = QuestionInfos !== null ? QuestionInfos : []
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 删除题目栏
    async DelQuestionItemBasket({ type, Id }){
      let data
      if(type === 'lan'){
        data = {
          QuestionTypeId: Id,
        }
      }else if(type === 'item'){
        data = {
          QuestionId: Id,
        }
      }
      const { data:res } = await this.$uwonhttp.post('/Question/Question/DelQuestionItemBasket', { ...data, SubjectId: localStorage.getItem('UW_SUBJECT'), UserId: localStorage.getItem('UserId') })
      // console.log(res)
      if(res.Success){
        this.GetQuestionItemBasketInfo()
        if(['lan','all'].includes(type)){
          this.$emit('delSuccess')
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
  }
}
</script>

<template>
<!--  :disabled="QuestionTypeInfos.length === 0"-->
  <el-popover
    placement="left"
    width="300"
    trigger="hover"
    v-model="visible">
    <div class="contentPopover">
      <ul>
        <li v-for="(item, index) in QuestionTypeInfos" :key="index">
          <p>{{ item.TypeName }}</p>
          <p>{{ item.Total }}</p>
          <p><i style="cursor: pointer;" class="el-icon-delete" @click="DelQuestionItemBasket({ type: 'lan', Id: item.TypeId})"></i></p>
        </li>
      </ul>
      <div class="generateAJob">
        <el-button @click="generateAJob">生成作业</el-button>
      </div>
      <div class="preview">
        <el-button type="text" icon="el-icon-news" @click="$emit('preview')">作业预览</el-button>
        <el-button type="text" icon="el-icon-delete-solid" @click="DelQuestionItemBasket({ type: 'all', Id: '' })">删除全部</el-button>
      </div>
    </div>
    <div class="elPopover" slot="reference">
      <p class="spanRed" v-show="total > 0">{{ total }}</p>
      <i class="iconfont icon-gouwulan" style="color: #FFFFFF;"></i>
      题目篮
    </div>
  </el-popover>
</template>

<style scoped lang="less">
// 题目栏
.contentPopover{
  ul{
    padding: 14px 0;
    border-bottom: 1px solid gainsboro;
    margin-bottom: 14px;
    li{
      display: flex;
      align-items: center;
      p{
        width: 33%;
        font-size: 16px;
      }
      p:nth-child(2){
        text-align: center;
      }
      p:nth-child(3){
        text-align: right;
      }
    }
  }
  .generateAJob{
    .el-button{
      width: 100%;
      background: #7fb99a;
      color: #ffffff;
    }
  }
  .preview{
    display: flex;
    justify-content: space-around;
    .el-button{
      font-size: 16px;
      color: #30303099;
    }
  }
}
</style>