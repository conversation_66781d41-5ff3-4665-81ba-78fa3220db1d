<template>
  <div class="h-100 newJobResources">
    <div class="flex flex_align_start h-100">
      <div
        class="margin_r_5 back_white h-100 flex_item_shrink "
        style="transition: all ease 0.3s"
        :class="{ openInDialogBorder: openInDialog }"
        :style="{ width: leftWidth + '%' }"
      >
        <left-screening :pageId="$route.path" ref="leftScreening" :left-width="leftWidth" :openInDialog="openInDialog" @changeWidth="leftWidth = leftWidth == 18 ? 2.3 : 18" @getAllList="GetQuestions"></left-screening>
      </div>
      <div class="flex_item_grow h-100 padBotm" :style="{width: ZKZl ? '80%' : 'auto'}">
        <div v-if="!openInDialog&&$store.state.LoginSource != 1" class="tabs-view">
          <div class="tab-item active">题目</div>
          <div class="tab-item" @click="changeTab">课件</div>
        </div>
        <div
          class="back_white border_radius_4 commonPadding"
          :class="{ openInDialogBorder: openInDialog }"
          :style="styleHeight"
        >
          <div class="flex flex_align_center flex_space_between">
            <div v-if="!openInDialog">
              <el-button class="font_size_20" v-if="topLevel == $store.state.Level"
              @click="goNewItem"
              type="primary"
              icon="el-icon-plus"
              size="small">
                新增题目
              </el-button>
              <el-button
                v-if="topLevel == $store.state.Level"
                @click="openBaseInfoInput('Importing')"
                plain
                type="success"
                size="small"
                icon="el-icon-document-add"
                class="font_size_20"
                >AI导题</el-button
              >
              <el-button class="font_size_20"
              v-if="topLevel == $store.state.Level && !checkBoxShow"
              plain
              type="success"
              size="small"
              icon="el-icon-share"
              @click="() => checkBoxShow = true">
                同校共享
              </el-button>
              <el-button class="font_size_20"
              v-if="topLevel == $store.state.Level && checkBoxShow"
              plain
              type="success"
              size="small"
              @click="SchoolShare"
              >
                确认共享 （{{checkedIds.length}}）
              </el-button>
              <el-button type="text" v-show="topLevel == $store.state.Level && checkBoxShow" @click="() => checkBoxShow = false">取消</el-button>
            </div>
            <div v-else></div>
            <div class="flex flex_space_between flex_align_center margin_tb_10">
              <div class="divTotal">共<span style="color: #7FB99A;">{{questionsTotal}}</span>条资源</div>
              <select-com v-show="['5'].includes($store.state.Level)" placeholder="请选择区" :options="areaList" size="small" @selectValue="areaBtn"></select-com>
              <select-com v-show="['5'].includes($store.state.Level)" placeholder="请选择学校" :options="schoolList" size="small" @selectValue="schoolBtn"></select-com>
              <div class="flex flex_align_center">
                <el-input class="font_size_20" v-model="QueryKey" placeholder="请输入题干" >
                  <el-button class="font_size_20" slot="append" icon="el-icon-search" @click="GetQuestions"></el-button>
                </el-input>
                &nbsp;&nbsp;&nbsp;
              </div>
              <div class="more-filter-view" @click="handleMoreFilter">
                <span style="font-size:16px;color:#8ac3ad;">更多筛选</span>
                <i class="el-icon-arrow-down" style="font-size:16px;color:#8ac3ad;margin-left:2px;"></i>
              </div>
            </div>
          </div>
          <top-screening v-show="moreSelect" ref="topScreening" :showTop="false" :showBottom="true" @screeningEmit="screeningEmit" @updateLeftChapters="updateLeftChapters" @updateHeight="(e)=>boxHeight = e" ></top-screening>
          <div class="sort-view" v-if="!openInDialog">
            <div class="sortDiv">
              <div class="txt" :style="{color: [1,4].includes(SortKey) ? '#7fb99a' : '#999'}" @click="sortBtn(1,4)">更新时间</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: SortKey == 4 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: SortKey == 1 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="sortDiv" v-show="(State == 1 && topLevel == 2) || [3,4,5].includes(topLevel)">
              <div class="txt" :style="{color: [2,5].includes(SortKey) ? '#7fb99a' : '#999'}"  @click="sortBtn(2,5)">组卷次数</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: SortKey == 5 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: SortKey == 2 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="sortDiv" v-show="(State == 1 && topLevel == 2) || [3,4,5].includes(topLevel)">
              <div class="txt" :style="{color: [3,6].includes(SortKey) ? '#7fb99a' : '#999'}"  @click="sortBtn(3,6)">已做学生数</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: SortKey == 6 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: SortKey == 3 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
          </div>
          <div style="height: 92%" v-show="QuestionList.length > 0">
            <el-scrollbar style="height: 100%" ref="scrollbar" v-loading="isLoading">
              <div class="list-container" :class="openInDialog ? 'mb100':'' ">
                <StemCard
                  v-for="(Q, index) in QuestionList"
                  :key="index + 'Q'"
                  :index="index"
                  :stem="Q"
                  :State="State"
                  :openInDialog="openInDialog"
                  :ItemScore="false"
                  :checkBoxShow="checkBoxShow"
                  :genjin="genjin"
                  @updateList="GetQuestions"
                  @handleCheckChange="handleCheckChange"
                  @addItem="(Id) => $refs.itemList.SaveQuestionItemBasket(Id)"
                  @delItem="(Id) => $refs.itemList.DelQuestionItemBasket({ type: 'item', Id })"
                  @NewEldio="$emit('NewEldio')"
                  @comfirmAdd="$emit('comfirmAdd')"
                ></StemCard>
              </div>
            </el-scrollbar>
          </div>
          <div v-show="!QuestionList.length" class="width_full text_align_center empty">没有查询到题目信息哦~</div>
        </div>
      </div>
    </div>
    <new-base-info-input ref="newBaseInfoInput" @goXinZeng="goXinZeng"></new-base-info-input>
    <!--  生成作业  -->
    <add-a-job-popup ref="addAJobPopup" :QuestionList="QuestionList"></add-a-job-popup>
    <el-dialog class="font_size_20" title="同校共享" :visible.sync="shareDialog" center width="27%" :before-close="cancelShare">
      <div class="text_align_center">
        <div class="margin_tb_20 font_size_22">确认将这些作业资源共享至校本？</div>
        <el-radio v-model="radio" :label="2">愿意全区共享</el-radio>
        <el-radio v-model="radio" :label="3">愿意全市共享</el-radio>
      </div>
      <div slot="footer">
        <el-button class="font_size_20" @click="cancelShare">取消</el-button>
        <el-button class="font_size_20" type="primary" @click="confirmShare">确认</el-button>
      </div>
    </el-dialog>
    <item-list ref="itemList"
     v-show="pathShow"
     @delSuccess="GetQuestions"
     @preview="() => {$refs.previewPaper.GetQuestionItemBasketPreviewInfo()}"
     @generateAJob="generatingJobs"></item-list>
    <!--  预览  -->
    <preview-paper ref="previewPaper" @removePreviewItem="removePreviewItem" :btn-show="true"></preview-paper>
  </div>
</template>

<script>
import BaseInfoInput from './BaseInfoInput.vue'
import StemCard from './StemCard.vue'
import AddAJobPopup from '@/views/threeAssistants/components/JobDesign/AddAJobPopup'
import SelectCom from '@/views/threeAssistants/newJobResources/components/select'
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import topScreening from '@/views/threeAssistants/components/topScreening'
import newBaseInfoInput from '@/views/threeAssistants/newJobResources/newBaseInfoInput'
import itemList from '@/views/threeAssistants/newJobResources/components/itemList/index.vue'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper.vue'

export default {
  name: 'NewJobResources',
  components: {
    previewPaper,
    BaseInfoInput,
    // ChapterMenus,
    // SearchBars,
    StemCard,
    AddAJobPopup,
    SelectCom,
    leftScreening,
    topScreening,
    newBaseInfoInput,
    itemList
  },
  props: {
    // 是否弹窗打开
    openInDialog: {
      type: Boolean,
      default: false,
    },
    genjin: {
      type: Boolean,
      default: false,
    },
    ZKZl:{
      type: Boolean,
      default: false
    },
    // 针对试题章节树添加数量
    activeName:{
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moreSelect: false, // 是否显示更多筛选
      teachingMaterial: [],
      dataFromBaseInfoInput: {}, //从基本信息弹窗获取的参数,ChapterId获取的两级Id，根据接口取用
      leftWidth: 18, //动态控制布局
      QuestionList: [], //题目列表
      urlType: '', // 跳转路由区分
      boxHeight: 0, // 盒子高度
      isInit: true, // 是否首次加载
      isLoading: true,
      PageNo: 1, // 页码
      questionsTotal: 0, // 题目总数
      scrollHeight: 0, // 盒子可滚动高度
      timer: null,
      shareDialog: false,
      radio: 1,
      // chapterIds: [], //单元和章节参数
      AreaId: '',
      areaList: [],
      SchoolId: '',
      schoolList: [],
      // sortValue: 1,//排序记录值
      // sortDesValue: 1,
      // 左侧组件，头部组件缓存值
      QueryKey: '',
      leftSearch: {},
      SortKey: 1,
      State: 1, // 已发布未发布状态做时间权限
      topLevel: 2,
      checkBoxShow: false,
      submitType: '', // 生成作业类型
    }
  },
// /Paper/TeacherPaper/GetTeacherPaperListPractice
  async created() {
    // 获取区数据
    this.GetAreaList()
    // await this.GetShowMenu()
    // if (this.$route.path == '/threeAssistants/newJobResources/index') this.GetQuestions() //目的是防止作业设计新增弹窗为打开时调用接口，无意义
  },
  // activated() {
  //   this.GetQuestions()
  // },
  computed: {
    styleHeight() {
      return this.openInDialog ? { height: `calc( 100% - ${this.moreSelect ? 150 : 50}px)` } : { height: `calc( 100% - ${this.boxHeight}px)` }
    },
    // 题目篮显示和隐藏
    pathShow () {
      return ['/threeAssistants/newJobResources/index', '/Paper/Exam_MicroLesson/QuestionBankResourceCenter'].includes(this.$route.path)
    },
    checkedIds() {
      return this.QuestionList.filter((q) => q.checked).map((item) => item.Id)
    },
  },
  watch: {
    $route: {
      handler(nv, ov) {
        if (nv.path == '/threeAssistants/newJobResources/index' || nv.path == '/Paper/Exam_MicroLesson/QuestionBankResourceCenter') {
          this.GetQuestions()
          this.GetQuestionItemBasketInfo()
        }
      },
    },
  },
  methods: {
    handleMoreFilter() {
      this.moreSelect = !this.moreSelect
    },
    // 更新试题栏数据
    GetQuestionItemBasketInfo() {
      this.$refs.itemList.GetQuestionItemBasketInfo()
    },
    // 左侧组件方法
    getLeftScreening() {
      // this.leftSearch = obj
      // console.log('执行左侧组件方法')
      this.GetQuestions()
    },
    // 右上侧筛查组件
    screeningEmit(obj) {
      // 字符串转数字
      obj.Level = Number(obj.Level)
      this.topLevel = obj.Level
      this.State = obj.State
      this.GetQuestions()
    },
    // 只更新左侧章节树
    updateLeftChapters(obj) {
      // 字符串转数字
      obj.Level = Number(obj.Level)
      // 切换市区校个人资源时执行左侧章节树方法
      this.$refs.leftScreening.setLevel(obj.Level)
    },
    // ********作业设计弹窗组件执行逻辑***********
    popoverComponentRequest() {
      // console.log('逻辑中转站')
      this.$refs.leftScreening.getYear()
    },
    // 区查询
    areaBtn(val) {
      this.AreaId = val
      this.GetSchoolByArea(val)
      this.GetQuestions()
    },
    // 校查询
    schoolBtn(val) {
      this.SchoolId = val
      this.GetQuestions()
    },
    sortBtn(e, r) {
      if(this.SortKey == e){
        this.SortKey = r
      } else {
        this.SortKey = e
      }
      this.GetQuestions()
    },
    // 预览页删除题目栏数据
    removePreviewItem({btnText, itemId}) {
      this.$refs.itemList.DelQuestionItemBasket({type: 'item', Id: itemId})
      this.GetQuestions()
      if(btnText === '题目栏预览'){
        const time = setTimeout(() => {
          this.$refs.previewPaper.GetQuestionItemBasketPreviewInfo()
          clearTimeout(time)
        },500)
      }
    },
    // clearTreeCheckChange(nodes) {
    //   this.$refs.leftScreening.setTreeChecked(nodes)
    // },
    //绑定跟进练习时只能选中一道  未来会放开 放开后此方法可取消
    handleCheckChange(val) {
      if (!this.genjin) return
      this.QuestionList.forEach((item) => {
        if (item.Id == val) item.checked = true
        else item.checked = false
      })
    },
    // 同校共享弹窗调起
    SchoolShare() {
      if (this.checkedIds.length == 0) return this.$message.error('请先选择题目')
      this.shareDialog = true
    },
    // 同校共享和是否同区或者同市共享
    async confirmShare() {
      const { data } = await this.$uwonhttp.post('/Question/Question/SetQuestionShare', {
        QuestionIds: this.checkedIds,
        ShareAreaCity: this.radio,
      })
      if (!data.Success) return this.$message.error(data.Msg)
      else this.$message.success('共享成功！')
      this.GetQuestions()
      this.shareDialog = false
      this.checkBoxShow = false
      this.cancelShare()
    },
    cancelShare() {
      this.shareDialog = false
      this.radio = 1
      this.QuestionList.forEach((item) => {
        item.checked = false
      })
    },
    // 排序变化
    // changeSortKey(item) {
    //   if (item.value === this.sortValue) {
    //     this.commonData.SortKey.value = this.commonData.SortKey.value > 3 ? item.value : item.value + 3
    //   } else {
    //     this.commonData.SortKey.value = item.value
    //   }
    //   this.sortDesValue = this.commonData.SortKey.value
    //   this.sortValue = item.value
    //   this.GetQuestions()
    // },
    // 生成作业开弹窗
    generatingJobs(checkedIds) {
      // const { Year, Semester, GradeId } = this.commonData
      // const [ unit, ChapterId ] = this.chapterIds
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      const [ unit, ChapterId ] = Ids.length == 1 ? [ParentId[0], Ids[0]] : ['','']
      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: {
          Year: Year,
          Semester: Term,
          GradeId: GradeId,
          unit,
          ChapterId,
          TextbookId: -1,
          testQuestionBankIds: checkedIds ? checkedIds.join(',') : '',
        },
      });
      // const passingParameters = {
      //   Year: Year,
      //   Semester: Term,
      //   GradeId: GradeId,
      //   unit ,
      //   ChapterId,
      //   TextbookId: -1,
      // }
      // 题库ID数组
      // this.$refs.addAJobPopup.setBankIds(this.checkedIds)
      // this.$refs.addAJobPopup.setBankIds(checkedIds)
      // this.$refs.addAJobPopup.init(passingParameters, '试题栏')
    },
    //searchBars变化
    // changeSearchData(search) {
    //   const { item, key } = search
    //   this.commonData[key].value = item.value
    //   // this.GetQuestions()
    // },
    //更换章节
    // changeChapterId(ChapterId) {
    //   if (this.commonData.ChapterId.value == ChapterId) return
    //   this.commonData.ChapterId.value = ChapterId
    //   // this.GetQuestions()
    // },
    // 获取单元和章节
    // TossingValuesToTheParent(val) {
    //   if (val.length === 1) {
    //     const { ParentId, ChapterId } = val[0]
    //     this.chapterIds = [ParentId, ChapterId]
    //   } else {
    //     this.chapterIds = []
    //   }
    // },
    /**
     * @description: 滚动触底加载更多
     * @return {*}
     */
    handleScroll() {
      this.$nextTick(() => {
        let scrollbarEl = this.$refs.scrollbar.wrap
        scrollbarEl.scrollTop = 1
        const dropNum = 150
        scrollbarEl.onscroll = () => {
          this.scrollHeight = scrollbarEl.scrollHeight
          const scrollTop = Math.ceil(scrollbarEl.scrollTop)
          const totalScrollHeight = scrollTop + scrollbarEl.clientHeight + dropNum
          if (totalScrollHeight > this.scrollHeight && !this.isLoading) {
            this.isLoading = true
            this.PageNo++
            const pageTotal = Math.ceil(this.questionsTotal / 20)
            if (this.PageNo > pageTotal) return (this.isLoading = false)
            this.pushGetQuestions()
          }
        }
      })
    },
    //打开弹窗
    openBaseInfoInput(typ) {
      this.urlType = typ
      // this.$refs.BaseInfoInput.openDialog()
      this.$refs.newBaseInfoInput.init()
    },
    //新增
    goXinZeng(data) {
      // this.dataFromBaseInfoInput = data
      const { ChapterId, GradeId, Semester, TextbookId, Year, Unit } = data
      this.$router.push({
        path: '/threeAssistants/BulkImportEditing/Importing',
        query: {
          unitId: Unit,
          ChapterId,
          GradeId,
          Semester,
          TextbookId,
          Year,
        },
      })
    },
    goNewItem() {
      // 新增题目
      const { Year, Term, GradeId } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      this.$router.push({
        path: '/threeAssistants/AddANewTopic/index',
        query: {
          GradeId,
          Semester: Term,
          Year
        }
      })
    },
    // 这里还未开始
    async pushGetQuestions() {
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      const newJob = this.$route.path === '/threeAssistants/JobDesign/NewJob'
      // 左侧章节树组件值
      const { yearActive, semesterActive, gradeActive, chapterList } = this.$refs.leftScreening.throwObj
      // vuex固定值
      const { Level } = this.$refs.topScreening.throwObj
      const { DifficultyLevel, ShareType, State, QuestionType } = this.$refs.topScreening.throwObj
      const chiled = newJob ? chapterList.map(item => item.ChapterId).join(',') :Ids.join(',')
      const itemIds = this.$store.state.chapterStorage.itemIds
      const newParams = {
        SortKey: this.SortKey,
        Year: newJob ? yearActive : Year,
        Semester: newJob ? semesterActive : Term,
        GradeId: newJob ? gradeActive : GradeId,
        ChapterId: chiled,
        PageNo: this.PageNo,
        PageSize: 20,
        AreaId: this.AreaId,
        SchoolId: this.SchoolId,
        RoleId: localStorage.getItem('role'),
        TextbookId: -1,
        Level,
        DifficultyLevel,
        ShareType,
        QuestionType,
        State: Level == 3 || Level == 5 ? -1 : State
        // ...this.$refs.topScreening.throwObj // 右侧头部筛选组件数据
      }
      // const { data } = await this.$uwonhttp.post('/Question/Question/GetQuestions', params)
      const { data } = await this.$uwonhttp.post('/Question/Question/GetQuestions', newParams)
      if (!data.Success) return this.$message.error(data.Msg)
      let questionsList = []
      questionsList = data.Data.Questions.map((item) => {
        // item.checked = false
        // 加入试题篮需求判断
        item.checked = itemIds.includes(item.Id)
        return item
      })
      this.QuestionList = this.QuestionList.concat(questionsList)
      this.questionsTotal = data.Data.Total
      this.$nextTick(() => {
        let scrollbarEl = this.$refs.scrollbar.wrap
        this.scrollHeight = scrollbarEl.scrollHeight
        this.isLoading = false
      })
    },
    // 获取试题列表
    async GetQuestions() {
      this.isLoading = true
      this.PageNo = 1
      this.topLevel = this.$refs.topScreening.throwObj.Level
      const newJob = this.$route.path === '/threeAssistants/JobDesign/NewJob'
      const { Level } = this.$refs.topScreening.throwObj
      const { DifficultyLevel, ShareType, State, QuestionType } = this.$refs.topScreening.throwObj
      // 左侧章节树组件值
      const { yearActive, semesterActive, gradeActive, chapterList } = this.$refs.leftScreening.throwObj
      // vuex固定值
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      const chiled = newJob ? chapterList.map(item => item.ChapterId).join(',') :Ids.join(',')
      const itemIds = this.$store.state.chapterStorage.itemIds
      // console.log(itemIds)
      const newParams = {
        SortKey: this.SortKey,
        Year: newJob ? yearActive : Year,
        Semester: newJob ? semesterActive : Term,
        GradeId: newJob ? gradeActive : GradeId,
        RoleId: localStorage.getItem('role'),
        ChapterId: chiled,
        QueryKey: this.QueryKey,
        PageNo: 1,
        PageSize: 20,
        AreaId: this.AreaId,
        SchoolId: this.SchoolId,
        TextbookId: -1,
        Level,
        DifficultyLevel,
        ShareType,
        QuestionType,
        State: Level == 3 || Level == 5 ? -1 : State
        // ...this.$refs.topScreening.throwObj // 右侧头部筛选组件数据
      }
      this.State = newParams.State
      // const { data } = await this.$uwonhttp.post('/Question/Question/GetQuestions', params)
      const { data } = await this.$uwonhttp.post('/Question/Question/GetQuestions', newParams)
      if (!data.Success) return this.$message.error(data.Msg)
      if (data.Data.Questions.length > 0) {
        data.Data.Questions.forEach((item) => {
          // 加入试题篮需求判断
            item.checked = itemIds.includes(item.Id)
        })
      }
      this.QuestionList = data.Data.Questions
      this.questionsTotal = data.Data.Total
      this.isInit && data.Data.Questions.length && this.handleScroll()
      if (data.Data.Questions.length) this.isInit = false
      this.isLoading = false
      this.$nextTick(() => {
        if (this.$refs.scrollbar) {
          this.$refs.scrollbar.wrap.scrollTop = 0
        }
      })
    },
    //查询参数处理
    // handleQueryParams() {
    //   let params = {}
    //   Object.keys(this.commonData).forEach((key) => {
    //     let Val = this.commonData[key]
    //     if (!!Val && typeof Val === 'object' && !Val.notQuery) params[key] = Val.value
    //     else if (typeof Val !== 'object') params[key] = Val
    //   })
    //   return params
    // },

    //根据角色获取试题类型 市 区 我的 ...
    // async GetShowMenu() {
    //   if (this.$store.state.tikuQueryParams['Level'].options.length > 0) return
    //   const { data } = await this.$uwonhttp.get(`/Question/Question/GetShowMenu?roleId=${localStorage.getItem('role')}`)
    //   if (!data.Success) return this.$message.error(data.Msg)
    //   let Data = data.Data
    //   Data.forEach((item) => {
    //     item.label = item.text
    //   })
    //   if(this.$store.state.Level == '5'){
    //     this.$store.commit('SET_SINGLE_VALUE', { key: 'Level', value: Data[0].value })
    //   }
    //   this.$store.commit('SET_PARAM_OPTIONS', { key: 'Level', options: Data })
    //   setTimeout(() => {
    //     this.boxHeight = this.$refs.searchBarsView.offsetHeight
    //   }, 100)
    // },
    // 获取区数据
    async GetAreaList() {
      const { data } = await this.$uwonhttp.post('/Question/Question/GetAreaList')
      if (!data.Success) return this.$message.error(data.Msg)
      let Data = data.Data
      this.areaList = Data
      this.GetSchoolByArea(Data[0].value)
    },
    // 获取校数据
    async GetSchoolByArea(areaId) {
      const { data } = await this.$uwonhttp.post('/Question/Question/GetSchoolByArea?areaId='+areaId)
      if (!data.Success) return this.$message.error(data.Msg)
      let Data = data.Data
      this.schoolList = Data
    },
    changeTab() {
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/components/lessonPreparationResources'
      })
    }
  },
}
</script>

<style scoped lang="less">
.tabs-view {
  display: flex;
  align-items: center;
  // margin-bottom: 20px;
  // border-bottom: 1px solid #e6e6e6;
  background: white;
  .tab-item {
    position: relative;
    padding: 12px 24px;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    &:hover {
      color: #7fb99a;
    }
    &.active {
      color: #7fb99a;
      font-weight: 500;
      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: #7fb99a;
        border-radius: 2px;
      }
    }
  }
}
.more-filter-view{
    display: flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
  }
.padBotm{
  padding-bottom: 5px;
}
.divTotal{
  font-size: 18px;
  margin-right: 10px;
  span{
    font-size: 22px;
  }
}
.mb100{
  margin-bottom: 150px;
}
.IndexSearch {
  border-bottom: 1px solid #e6e6e6;
  font-size: 26px;
}
.commonPadding {
  padding: 10px 10px;
  //overflow: hidden;
}
.activeColor {
  color: #7fb99a !important;
}
.el-icon-sort-down, .el-icon-sort-up{
  color: rgba(0, 0, 0, 0.65);
}
.empty {
  margin: 100px 0;
}
.h-100 {
  height: 100%;
}
.openInDialogBorder {
  border: 1px solid #e6e6e6;
}
.sort-view{
  display: flex;
  justify-content: flex-end;
  .sortDiv{
    display: flex;
    align-items: center;
    margin: 0 4px;
    .txt{
      font-size: var(--font16-20);
    }
    .icon{
      display: flex;
      flex-direction: column;
      margin-left: 2px;
      i{
        font-size: 16px;
      }
      i:nth-child(1){
        position: relative;
        bottom: -3px;
      }
      i:nth-child(2){
        position: relative;
        top: -3px;
      }
    }
  }
}
/deep/.el-radio__label {
  // font-size: 16px;
  font-size: var(--font16-22);
}
/deep/.el-dialog__wrapper{
  .el-dialog__title, .el-dialog__close{
    //font-size: 22px !important;
    font-size: var(--font16-22);
  }
}
///deep/.el-button{
//  font-size: 22px !important;
//}
/deep/.el-form-item__label{
  //font-size: 22px !important;
  font-size: var(--font16-22);
}
/deep/.el-tree__empty-text{
  //font-size: 20px !important;
  font-size: var(--font16-20);
}
/deep/.el-checkbox{
  .el-checkbox__label{
    //font-size: 22px !important;
    font-size: var(--font16-22);
  }
}
///deep/.el-input{
//  height: 50px;
//  .el-input__inner{
//    height: 50px;
//  }
//}
/deep/.el-radio__inner{
  width: 20px !important;
  height: 20px !important;
}
// 试题栏
.newJobResources{
  position: relative;
  /deep/.elPopover{
    position: absolute;
    right: 0;
    top: 10%;
    width: 40px;
    //height: 120px;
    line-height: 20px;
    padding: 16px 12px;
    font-size: 18px;
    background: #7fb99a;
    color: #FFFFFF;
    text-align: center;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    z-index: 999;
    cursor: pointer;
    .spanRed{
      width: 20px;
      border-radius: 100px;
      font-size: 12px;
      background: #ff0000;
      color: #FFFFFF;
      position: absolute;
      top: -4px;
      right: 0px;
    }
    i{
      font-size: 20px;
    }
  }

}
</style>
<style lang="less">
.commonPadding .el-scrollbar__wrap {
  overflow-x: hidden;
}
//// 题目栏
//.contentPopover{
//  ul{
//    padding: 14px 0;
//    border-bottom: 1px solid gainsboro;
//    margin-bottom: 14px;
//    li{
//      display: flex;
//      align-items: center;
//      p{
//        width: 33%;
//        font-size: 16px;
//      }
//      p:nth-child(2){
//        text-align: center;
//      }
//      p:nth-child(3){
//        text-align: right;
//      }
//    }
//  }
//  .generateAJob{
//    .el-button{
//      width: 100%;
//      background: #7fb99a;
//      color: #ffffff;
//    }
//  }
//  .preview{
//    display: flex;
//    justify-content: space-around;
//    .el-button{
//      font-size: 16px;
//      color: #30303099;
//    }
//  }
//}
</style>
