<template>
  <div class="StemCard border_radius_4 margin_tb_10">
    <div class="flex flex_align_start flex_space_between font_size_22">
<!--      <img v-if="stem.checked && openInDialog" src="../../../assets/dzb/right.png" class="imgPosition" alt="" />-->
<!--      <div v-if="openInDialog || InResources" class="margin_r_15">-->
<!--        <el-checkbox v-model="stem.checked"></el-checkbox>-->
<!--      </div>-->
<!--      <img v-if="stem.checked && checkBoxShow" src="../../../assets/dzb/right.png" class="imgPosition" alt="" />-->
      <div v-if="checkBoxShow" class="margin_r_15">
        <el-checkbox v-model="stem.checked"></el-checkbox>
      </div>

      <div class="flex_item_grow cursor_pointer" @click="checkBoxShow ? stem.checked = !stem.checked : ''">
        <!-- 题目+展开 -->
        <div class="flex flex_align_start flex_space_between font_size_22">
          <p class="font_color_333" style="flex: 1;">
            <span>{{ sortNum ? sortNum : index+1 }}、</span>
            <span :style="{ background: QuestionType_Color.get(stem.QuestionType) }" class="questionType">{{
              stem.QuestionType
            }}</span>
            &nbsp;
            <span class="title font_size_22 font_size_16" v-html="stemTitle" v-katex></span>
          </p>
          <div class="card-img-container" :style="{width:isShowDrag ? '24%' : '5%'}">
            <img class="drag-img" v-if="isShowDrag" src="@/assets/images/image.png">
            <p class="activeColor updown cursor_pointer flex_item_shrink">
              <span v-if="fold" @click.stop="fold = false" class="font_size_16">展开<i class="el-icon-arrow-down"></i></span>
              <span v-if="!fold" @click.stop="fold = true" class="font_size_16">收起<i class="el-icon-arrow-up"></i></span>
            </p>
          </div>
        </div>
        <!--    试题选项    -->
        <div class="ScoreDiv">
          <div>
            <ul class="ulOption" v-if="stem.Options !== null && stem.Options.length">
              <li v-for="item in stem.Options" :key="item.Option">
                {{ item.Option }}. <span v-html="item.Content" v-katex></span>
              </li>
            </ul>
          </div>
          <div class="fenshuNum" v-show="IsSetScore == 1">
            <el-tooltip class="item" effect="dark" content="点击修改该题的分值" placement="top-end">
              <div class="fenzhiTxt" v-show="!stem.scoreShow" @click="stem.scoreShow = true">{{stem.ItemScore + '分'}}</div>
            </el-tooltip>
            <el-input-number v-show="stem.scoreShow" v-model="stem.ItemScore" controls-position="right" @change="$emit('changeScore',{index, stem})" :precision="0" :min="1" :max="100"></el-input-number>
          </div>
        </div>
        <!-- 基本属性+操作按钮 -->
        <div class="flex flex_align_end flex_space_between font_size_22 font_size_16 details-content color_9">
          <div>
            <div class="interval-1">
              <span>来源：{{ stem.Source || '--' }}&nbsp;</span>
              <span>
                <img v-if="stem.SourceType == 4" src="@/assets/teacher/班.png" alt="" />
                <img v-if="stem.SourceType == 1" src="@/assets/teacher/市.png" alt="" />
                <img v-if="stem.SourceType == 3" src="@/assets/teacher/校.png" alt="" />
                <img v-if="stem.SourceType == 2" src="@/assets/teacher/区.png" alt="" />
                &nbsp;&nbsp;&nbsp;
              </span>
              <span>作者：{{ stem.Creator }}</span>
            </div>
            <div class="interval-1">
              <!-- <span>教材：{{ stem.TextBookVersion || '--' }}&nbsp;&nbsp;&nbsp;</span> -->
              <span>年级：{{ stem.Grade }}&nbsp;&nbsp;&nbsp;</span>
              <span>难度：{{ stem.Difficulty }}&nbsp;&nbsp;&nbsp;</span>
              <span>组卷次数：{{ stem.PaperCount }}&nbsp;&nbsp;&nbsp;</span>
              <span>已做学生数：{{ stem.DoAnswerCount }}&nbsp;&nbsp;&nbsp;</span>
              <span>更新时间：{{ stem.UpdateTime }}&nbsp;&nbsp;&nbsp;</span>
            </div>
          </div>
          <div class="btn_text" v-if="!openInDialog">
            <span v-if="InNewJob" class="encoding margin_r_15" @click="$emit('changeInkColor', stem)">笔迹颜色</span>
            <span v-if="$store.state.LoginSource == 1 && InNewJob && PaperTag !== 26" class="encoding margin_r_15" @click="$emit('openVideo', stem)">语音题设置</span>
            <template v-if="useType !== 'scan' && InNewJob && PaperTag !== 26">
              <!-- 根据 IsExistAdaptive 字段判断是绑定还是查看 -->
              <span
                class="encoding"
                v-if="!stem.IsExistAdaptive"
                @click.stop="$emit('addGenJin', stem.ItemId || stem.Id)"
                >添加跟进练习</span
              >
              <span class="encoding" v-else @click.stop="$emit('scanGenJin', { Id: stem.ItemId || stem.Id, index })"
                >&nbsp;&nbsp;&nbsp;查看跟进练习</span
              >
            </template>
            &nbsp;&nbsp;&nbsp;<span class="encoding" style="margin-right: 10px;" @click.stop="encodingHandel">题目信息</span>
<!--            <template v-if="State > 0 || State == -1">-->
<!--              &nbsp;&nbsp;&nbsp;<span @click.stop="copyQuestionClick"-->
<!--                >复制-->
<!--                <el-popover-->
<!--                  placement="top-start"-->
<!--                  width="200"-->
<!--                  trigger="hover"-->
<!--                  popper-class="popper"-->
<!--                  content="因题目已布置下发学生，暂不可更改题目信息，您可以复制到未布置作业中进行编辑。"-->
<!--                >-->
<!--                  <i class="el-icon-question" slot="reference"></i>-->
<!--                </el-popover>-->
<!--              </span>-->
<!--            </template>-->
            <!-- 作业设计中不展示编辑按钮 -->
<!--            <template v-if="useType == 'use' && State == 0 && !InNewJob">-->
<!--              &nbsp;&nbsp;&nbsp;<span @click.stop="editQuestionClick">编辑</span>-->
            <template v-if="useType == 'use' && !InNewJob">
              <span v-show="State == 0" @click.stop="editQuestionClick">编辑</span>
              <span v-show="State != 0" @click.stop="copyQuestionClick">编辑</span>
            </template>
            <template v-if="useType !== 'scan' && State == 0">
              &nbsp;&nbsp;&nbsp;
              <el-popconfirm title="确定删除吗？" @confirm="removeQuestionClick">
                <span slot="reference">删除</span>
              </el-popconfirm>
            </template>
            <template v-if="addRemoveBtn">
              <el-button class="margin_l_15" v-show="stem.IsBasket === 0" style="background: #7fb99a;color:#FFFFFF;" size="mini" icon="el-icon-plus" @click.stop="()=>{ stem.IsBasket = 1; $emit('addItem', stem.Id) }">
                加入
              </el-button>
              <el-button class="margin_l_15" v-show="stem.IsBasket === 1" size="mini" icon="el-icon-minus" @click.stop="() => { stem.IsBasket = 0; $emit('delItem', stem.Id) }">
                移除
              </el-button>
            </template>
          </div>
<!--          <div v-show="openInDialog && !genjin">-->
          <div v-show="openInDialog">
            <el-button
              :style="{background: stem.checked ? '#7FB99A33' : '#7fb99a', color: stem.checked ? '#999999' : '#FFFFFF'}"
              size="mini"
              :disabled="stem.checked"
              icon="el-icon-plus"
              @click="shiTiGenJinjoin">加入</el-button>
          </div>
        </div>
        <!-- 答案和解析 -->
        <div class="analyze-container" v-if="!fold">
          <div class="answer font_size_22">
            <span>答案:&nbsp;&nbsp;</span>
            <span v-html="stem.Answer" v-katex v-if="stem.QuestionType != '连线题'"></span>
            <template v-if="stem.QuestionType == '连线题'">
              <matchIng
                ref="matching"
                :Answer="stem.Answer"
                :SureAnser="stem.Answer"
                :linkExamData="stem.ItemChange.LinkExamData"
                :SubType="2"
                :record="0"
                customStr="true"
              ></matchIng>
            </template>
          </div>
          <div class="font_size_16">
            <span>解析:&nbsp;&nbsp;</span>
            <span v-html="stem.Analysis" v-katex class="analys font_size_22"></span>
          </div>
        </div>
      </div>
    </div>
    <!-- 编码属性抽屉里展开 -->
    <template>
      <el-drawer class="drawer font_size_20" title="题目信息" :visible.sync="settingInfo.isDrawer">
        <div class="tigan">
          <div class="title" v-html="questionDetails.Title" v-katex></div>
          <div class="optDiv" v-show="['2','10','11'].includes(questionDetails.TypeId)">
<!--          <div>-->
            <p class="optio font_size_20" v-for="(item, ind) in questionDetails.Options" :key="ind">
              <span>{{item.Option}}</span>
              <span v-html="item.Content" v-katex></span>
            </p>
          </div>
        </div>
        <ul class="encode-container">
          <!--    作文专题      -->
          <li v-if="questionDetails.TypeId === '26'">
            <span class="font_size_20" style="color:#999999;">字数：{{questionDetails.Words}}</span>
            <grading-criteria :questionDetails="questionDetails"></grading-criteria>
            <personal-profiling :questionDetails="questionDetails"></personal-profiling>
          </li>
          <li>
            <span class="font_size_20" v-show="questionDetails.TypeId !== '26'" style="color:#999999;">答案：</span>
            <span class="span-v font_size_20" v-if="!['50','26'].includes(questionDetails.TypeId)" v-html="questionDetails.Answer" v-katex></span>
            <match-ing
            v-if="questionDetails.TypeId === '50'"
            :Answer="questionDetails.Answer"
            :SureAnser="questionDetails.Answer"
            :linkExamData="questionDetails.ItemChange?.LinkExamData"
            :SubType="2"
            :record="0"
            customStr="true"></match-ing>
          </li>
          <li>
            <span class="font_size_20" style="color:#999999;">解析：</span>
            <span class="span-v font_size_20" v-html="questionDetails.Analysis || '略'" v-katex></span>
            <el-popover
              placement="left"
              width="400"
              trigger="click">
              <div>
                <audio v-show="questionDetails.AnalysisFile?.FileType == 2" controls>
                  <source :src="questionDetails.AnalysisFile?.FileUrl">
                  您的浏览器不支持 audio 元素
                </audio>
                <video v-show="questionDetails.AnalysisFile?.FileType == 1" width="320" height="240" controls>
                  <source :src="questionDetails.AnalysisFile?.FileUrl">
                  您的浏览器不支持 video 标签
                </video>
              </div>
              <el-button class="font_size_20" style="padding: 0;margin-left: 20px;color:#7fb99a;" slot="reference" type="text" v-show="questionDetails.AnalysisFile !== null">查看解析文件</el-button>
            </el-popover>
          </li>
          <li v-for="(item, index) in encodeInfo" :key="index">
            <template v-if="item.itemIdList.includes(questionDetails.TypeId)">
              <span class="span-v font_size_20">{{ item.name }}</span>
            <!-- <span class="span-v" v-katex>{{ item.value }}</span> -->
            <span class="span-v font_size_20" style="line-height: 20px;" v-katex v-html="item.value"></span>
            <el-popover
              placement="left"
              width="400"
              trigger="click">
              <div>
                <audio v-show="questionDetails.GuidanceFile?.FileType == 2" controls>
                  <source :src="questionDetails.GuidanceFile?.FileUrl">
                  您的浏览器不支持 audio 元素
                </audio>
                <video v-show="questionDetails.GuidanceFile?.FileType == 1" width="320" height="240" controls>
                  <source :src="questionDetails.GuidanceFile?.FileUrl">
                  您的浏览器不支持 video 标签
                </video>
              </div>
              <el-button class="font_size_20" style="padding: 0;margin-left: 20px;color:#7fb99a;" slot="reference" type="text" v-show="item.key == 'LearningGuidance' && questionDetails.GuidanceFile !== null">查看辅导文件</el-button>
            </el-popover>
            </template>
          </li>
        </ul>
      </el-drawer>
    </template>
    <div v-show="showMask" class="mask-div"></div>
  </div>
</template>
<script>
import ExamFormatCommon from '@/common/ExamFormatCommon.js'
import matchIng from '@/components/matching/index'
// 作文专题
import gradingCriteria from '@/views/threeAssistants/newJobResources/components/zuoWen/gradingCriteria.vue'
import personalProfiling from '@/views/threeAssistants/newJobResources/components/zuoWen/personalProfiling.vue'
const QuestionType_Color = new Map([
  ['单项选择题', '#FA9415'],
  ['多项选择题', '#5FA9F7'],
  ['判断题', '#D52E20'],
  ['填空题', '#77C357'],
  ['连线题', '#625FF7'],
  ['主观题', '#00ABA1'],
  ['简答题', '#57C3C3'],
  ['作文', '#00ABA1'],
])
export default {
  name: 'StemCard',
  props: {
    sortNum: {
      type: Number,
      default: 0,
    },
    stem: {
      type: Object,
      default() {
        return {}
      },
    },
    index: {
      type: Number,
      default: 0,
    },
    State: {
      type: Number,
      default: 0,
    },
    openInDialog: {
      type: Boolean,
      default: false,
    },
    useType: {
      type: String,
      default: 'use',
    },
    showMask: {
      type: Boolean,
      default: false
    },
    isShowDrag:{
      type: Boolean,
      default: false
    },
    // 限定分数设置需求 1：设置分数，0：不设置
    IsSetScore:{
      type:Number,
      default: 0
    },
    // 同校共享显示复选框
    checkBoxShow:{
      type: Boolean,
      default: false
    },
    // 跟进练习
    genjin:{
      type: Boolean,
      default: false
    },
    PaperTag:{
      type: Number,
      default: 0
    },
    // 控制加入移除按钮在某情况下展示隐藏
    addRemoveBtn:{
      type: Boolean,
      default: true
    }
  },
  components: {
    matchIng,
    gradingCriteria,
    personalProfiling,
  },
  data() {
    return {
      fold: true,
      QuestionType_Color,
      settingInfo: {
        isDrawer: false, // 是否打开抽屉
      },
      questionDetails:{},
      // 编码信息
      encodeInfo: [
        { name: '错误归因：', value: '', key: 'Misattribution', itemIdList:['5','2','10','11','36'] },
        { name: '对应单元：', value: '', key: 'UnitName', itemIdList:['5','2','10','11','36','26'] },
        { name: '对应章节：', value: '', key: 'ChapterName', itemIdList:['5','2','10','11','36','26'] },
        { name: '学习指导：', value: '', key: 'LearningGuidance', GuidanceFile: null , itemIdList:['5','2','10','11','36','26'] }, // TODO 墨刀是学习视频，录入是文字 待和产品确认
        { name: '题目难度：', value: '', key: 'Difficulty', itemIdList:['5','2','10','11','36','26'] },
        { name: '学习水平：', value: '', key: 'LearningLevel', itemIdList:['5','2','10','11','36','26'] },
        { name: '题目编号：', value: '', key: 'QuestionCode', itemIdList:['5','2','10','11','36'] },
        { name: '内容领域：', value: '', key: 'ContentDomain', itemIdList:['5','2','10','11','36','26'] },
        { name: '主题：', value: '', key: 'Theme', itemIdList:['5','2','10','11','36','26'] },
        { name: '单元目标：', value: '', key: 'UnitTargetContent', itemIdList:['5','2','10','11','36','26'] },
        { name: '章节目标：', value: '', key: 'ChapterTargetContent', itemIdList:['5','2','10','11','36','26'] },
        { name: '题目目标：', value: '', key: 'QuestionTargetContent', itemIdList:['5','2','10','11','36','26'] },
        { name: '题目来源：', value: '', key: 'Source', itemIdList:['5','2','10','11','36','26'] },
        { name: '预估完成时间(分钟)：', value: '', key: 'FinishTime', itemIdList:['5','2','10','11','36','26'] },
        { name: '素养属性：', value: '', key: 'CoreLiteracy', itemIdList:['5','2','10','11','36','26'] },
        { name: '拍照设置：', value: '', key: 'PhotographSet', itemIdList:['5','2','10','11','36'] },
        { name: '情景属性：', value: '', key: 'SceneAttribute', itemIdList:['5','2','10','11','36'] },
        // { name: '课程标准版本：', value: '', key: 'CourseVersions' },
      ],
    }
  },
  computed: {
    InNewJob() {
      //判断组件是不是在作业设计模块中使用
      return location.href.indexOf('/threeAssistants/JobDesign/NewJob') > 0
    },
    // InResources() {
    //   // return location.href.indexOf('/threeAssistants/newJobResources/index') > 0
    //   return ['/threeAssistants/newJobResources/index','/Paper/Exam_MicroLesson/QuestionBankResourceCenter'].includes(this.$route.path)
    // },
    stemTitle() {
      let title = ExamFormatCommon.format_space(this.stem.Title)
      let regx = /<img/gi
      title = title.replace(regx, `<img style="max-width:90%" `)
      return title
    },
  },
  watch: {
    'stem.checked': {
      handler(nv) {
        if (nv) this.$emit('handleCheckChange', this.stem.Id || this.stem.ItemId)
      },
    },
  },
  methods: {
    /*试题与跟进的加入*/
    shiTiGenJinjoin(stem){
      this.stem.checked = !stem.checked
      this.genjin ? this.$emit('comfirmAdd') : this.$emit('NewEldio')
    },
    /**
     * @description: 编码信息  根据作业和题库的使用区分一下接口调用
     * @return {*}
     */
    async encodingHandel() {
      let path, params
      //作业设计中
      if (this.InNewJob) path = '/Question/Question/GetItemCode'
      if (this.InNewJob) params = { itemId: this.stem.Id || this.stem.ItemId }
      //作业资源中
      if (!this.InNewJob) path = '/Question/Question/GetQuestionCode'
      if (!this.InNewJob) params = { questionId: this.stem.Id || this.stem.ItemId }
      const {
        data: { Data, Success, Msg },
      } = await this.$uwonhttp.get(path, {
        params,
      })
      if (!Success) return this.$message.error(Msg)
      // console.log(Data)
      this.questionDetails = Data
      this.encodeInfo.map((item) => {
        if (Data.hasOwnProperty(item.key)) {
          item.value = Data[item.key] || '-'
        }
        return item
      })
      this.settingInfo.isDrawer = true
    },
    /**
     * @description: 复制题目
     * @return {*}
     */
    async copyQuestionClick() {
      // const { data } = await this.$uwonhttp.post('/Question/Question/QuestionCopy', {
      const { data } = await this.$uwonhttp.post('Question/Question/CopyQuestion', {
        questionId: this.stem.Id,
        userRoleId: localStorage.getItem('role'),
      })
      console.log(data)
      if (data.Success) {
        // this.$message.success('复制成功')
        this.editQuestionClick(data.Data)
      }
    },
    /**
     * @description: 编辑跳转
     * @return {*}
     */
    editQuestionClick(cloneItemId) {
      let query = {
        ChapterId: this.stem.ChapterId,
        // GradeId: this.stem.GradeId || this.stem.Grade,
        GradeId: this.stem.GradeId,
        Semester: this.stem.Term,
        TextbookId: this.stem.TextbookId,
        Year: this.stem.Year,
        // questionId: this.stem.Id || this.stem.ItemId,
        questionId: typeof cloneItemId === 'string' ? cloneItemId : this.stem.Id || this.stem.ItemId,
        QuestionTypeId: this.stem.QuestionTypeId || this.stem.ItemTypeId,
      }
      if (this.InNewJob) query = Object.assign(query, { paperId: this.$route.query.paperId })
      this.$router.push({
        path: '/threeAssistants/AddANewTopic/index',
        query,
      })
    },
    /**
     * @description: 从作业设计中删除题目
     * @return {*}
     */
    async removeQuestionClick() {
      if (!this.InNewJob) return this.DeleteById()
      const res = await this.$http.post('/Paper/Exam_PaperItemMapping/Remove', {
        id: this.stem.Id || this.stem.ItemId,
      })
      if(res.Success){
        this.PaperItemCalculateScore()
        this.$message.success('删除成功')
      } else {
        this.$message.error(res.Msg)
      }
      // this.$emit('updateList') // 删除成功后更新列表
    },
    // 作业设计删除题目成功后调用接口
    async PaperItemCalculateScore(){
      const res = await this.$uwonhttp.post('/Question/Question/PaperItemCalculateScore', {
        paperId: this.$route.query.paperId,
      })
      if(res.data.Success){
        this.$emit('updateList')
      } else {
        this.$message.error(res.data.Msg)
      }
    },
    /**
     * @description: 作业资源中的删除题目
     * @return {*}
     */
    async DeleteById() {
      const { data } = await this.$uwonhttp.get(
        `/Question/Question/DeleteById?questionId=${this.stem.Id || this.stem.ItemId}`
      )
      if (!data.Success) return this.$message.error(data.Msg)
      this.$message.success('删除成功')
      this.$emit('updateList') // 删除成功后更新列表
    },
  },
}
</script>
<style lang="less" scoped>
@import './index.less';
.StemCard {
  position: relative;
  border: 1px solid #d9d9d9;
  padding: 10px 20px;
  // transition: all ease 0.3s;
  position: relative;
  background-color: white !important;
  .card-img-container{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 24%;
    img{
      margin-right: 10px;
    }
  }
}
.activeColor {
  color: #7fb99a;
}
.updown {
  // width: 5%;
  text-align: center;
}
.questionType {
  display: inline-block;
  color: #fff;
  padding: 3px 5px;
  border-radius: 4px;
  font-size: 12px;
}
.btn_text {
  span {
    cursor: pointer;
    font-weight: normal;
    //font-size: 22px;
    font-size: var(--font16-22);
    color: #4e6799;
    line-height: 19px;
  }
  .encoding {
    cursor: pointer;
  }
}
.imgPosition {
  position: absolute;
  bottom: 10px;
  right: 30px;
  width: 60px;
  height: 60px;
}
.tigan{
  padding-bottom: 20px;
  border-bottom: 1px solid #DCDCDCFF;
  margin-bottom: 20px;
  .title{
    font-size: 18px;
    font-weight: bold;
    padding: 0 20px;
    color: #000000;
  }
  .optDiv{
    padding-left: 20px;
    p{
      font-size: 16px;
      span:nth-child(1){
        margin-right: 8px;
      }
    }
  }
}
.drawer{
  /deep/.el-drawer{
    .el-drawer__header{
      >span{
        font-size: 18px !important;
        font-weight: bold;
      }
    }
  }
}
//.rtl{
//  /deep/.el-drawer__header{
//    span{
//      font-size: 18px;
//      font-weight: bold;
//    }
//  }
//}

.mask-div{
  position: absolute;
  top: 0;
  bottom: -11px;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.6);
}
/deep/ .el-checkbox{
  .el-checkbox__inner{
    width: 20px;
    height: 20px;
    &::after{
      left: 8px;
      top: 3px;
    }
  }
}
/deep/.el-dialog__close{
  font-size: 22px !important;
}
.popper{
  font-size: 20px !important;
}
.ScoreDiv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .ulOption{
    //width: calc(100vw - 400px);
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    li{
      font-size: var(--font16-20);
      margin-right: 78px;
      font-size: 16px;
      color: #333333;
    }
  }
  .fenshuNum{
    .fenzhiTxt{
      width: 70px;
      font-size: 24px;
      color: #F55E2F;
    }
    /deep/.el-input-number{
      width: 110px;
      .el-input{
        font-size: 24px;
        .el-input__inner{
          color: #F55E2F;
        }
      }
    }
  }
}

</style>
