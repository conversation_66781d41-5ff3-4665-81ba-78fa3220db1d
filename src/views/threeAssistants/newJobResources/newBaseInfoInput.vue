<template>
  <el-dialog
    title="题目基本信息"
    :visible.sync="dialogVisible"
    width="24%"
    :before-close="handleClose">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demoRuleForm">
      <el-form-item label="单元" prop="Unit">
        <el-select v-model="ruleForm.Unit" placeholder="请选择" @change="unitChange">
          <el-option v-for="item in unitList" :key="item.value" :label="item.text" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="章节" prop="ChapterId">
        <el-select v-model="ruleForm.ChapterId" placeholder="请选择">
          <el-option v-for="item in childList" :key="item.value" :label="item.text" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="教材" v-show="textBook.length > 1">
        <el-select v-model="ruleForm.TextbookId" placeholder="请选择">
          <el-option v-for="(item, ind) in textBook" :key="ind" :label="item.text" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'newBaseInfoInput',
  data() {
    return {
      dialogVisible: false,
      // yearList: [], // 学年
      // gradeList: [],// 年级
      unitList: [], // 单元
      childList: [], // 章节
      textBook: [], // 教材
      ruleForm: {
        Year: null,
        Semester: null,
        GradeId: null,
        Unit: null,
        ChapterId: null,
        TextbookId: null
      },
      rules: {
        Year: [
          { required: true, message: '请选择学年', trigger: 'blur' },
        ],
        Semester: [
          { required: true, message: '请选择学期', trigger: 'blur' }
        ],
        GradeId: [
          { required: true, message: '请选择年级', trigger: 'blur' }
        ],
        Unit: [
          { required: true, message: '请选择单元', trigger: 'blur' }
        ],
        ChapterId: [
          { required: true, message: '请选择章节', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    init() {
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      const [ unit, ChapterId ] = Ids.length == 1 ? [ParentId[0], Ids[0]] : ['','']
      // this.yearList = Session.get('yearList')
      // this.gradeList = Session.get('gradeList')
      this.ruleForm.Year = Year
      this.ruleForm.Semester = Term
      this.ruleForm.GradeId = GradeId
      this.ruleForm.Unit = unit
      this.ruleForm.ChapterId = ChapterId
      this.GetSubjectUnit()
      this.GetTextbookVersion()
      this.dialogVisible = true
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('goXinZeng', this.ruleForm)
          this.handleClose()
        } else {
          return false;
        }
      });
    },
    handleClose(){
      this.childList = []
      this.dialogVisible = false
    },
    allChange(){
      this.ruleForm.Unit = null
      this.GetSubjectUnit()
    },
    // 单元单机事件
    unitChange(val){
      this.GetSubjectChapter(val)
    },
    // 获取单元目录
    async GetSubjectUnit() {
      const { Year, Semester, GradeId } = this.ruleForm
      const { data:res } = await this.$uwonhttp.get(`/Chapter/Chapter/GetSubjectUnit?year=${Year}&term=${Semester}&grade=${GradeId}`)
      if(res.Success){
        this.unitList = res.Data
        if(this.ruleForm.Unit){
          this.GetSubjectChapter(this.ruleForm.Unit)
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取单元下面的章节目录
    async GetSubjectChapter(unitId) {
      const { data:res } = await this.$uwonhttp.get(`/Chapter/Chapter/GetSubjectChapter?unitId=${unitId}`)
      if(res.Success){
        this.childList = res.Data
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取教材
    // 获取教材
    async GetTextbookVersion() {
      const { data:res } = await this.$uwonhttp.get('/Question/Question/GetTextbookVersion')
      if(res.Success){
        // this.textBookShowHide = res.Data.length > 1 ? true : false
        this.ruleForm.TextbookId = res.Data[0].value
        // 往数组头部添加对象
        this.textBook = res.Data
      } else {
        this.$message.error(res.Msg)
      }
    },
  }
}
</script>

<style lang="less" scoped>
.font-size20{
  font-size: var(--font16-20);
}
.demoRuleForm{
  /deep/.el-input{
    font-size: var(--font16-20);
  }
}
</style>