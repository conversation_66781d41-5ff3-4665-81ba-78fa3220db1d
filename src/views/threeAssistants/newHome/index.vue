<template>
  <div class="h-100 newThreeHome">
    <!-- 数据概况 -->
    <div class="data-overview">
      <h2 class="section-title">数据概况</h2>
      <div class="data-cards">
        <div class="data-card card1">
          <div class="data-label">本周题目布置数量</div>
          <div class="data-value">{{ analysisData.CreateQuestionCount || 0 }}<span class="unit">道</span></div>
        </div>
        <div class="data-card card2">
          <div class="data-label">本周教案生成数量</div>
          <div class="data-value">{{ analysisData.CreateTeachingPlanCount || 0 }}<span class="unit">份</span></div>
        </div>
        <div class="data-card card3">
          <div class="data-label">本周作业参与率</div>
          <div class="data-value">{{ analysisData.PracticeJoinRate || 0 }}<span class="unit">%</span></div>
        </div>
        <div class="data-card card4">
          <div class="data-label">当前待批改作业</div>
          <div class="data-value">{{ analysisData.WaitCorrectCount || 0 }}<span class="unit">份</span></div>
        </div>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="quick-functions">
      <h2 class="section-title">快捷功能</h2>
      <div class="function-cards">
        <div class="function-card" @click="$refs.newBaseInfoInput.init()">
          <img src="@/assets/kuaisubz.png" alt="">
          <!-- <div class="function-icon">
            <div class="icon-placeholder icon-1"></div>
          </div>
          <div class="function-content">
            <h3>快速布置作业</h3>
            <p>AI智能题库，作业快速发布</p>
            <button class="action-btn">开始布置</button>
          </div> -->
        </div>
        <div class="function-card" @click="startClick">
          <img src="@/assets/aizhinpg.png" alt="">
          <!-- <div class="function-icon">
            <div class="icon-placeholder icon-2"></div>
          </div>
          <div class="function-content">
            <h3>AI智能批改</h3>
            <p>快速批改作业，大幅提升批改效率</p>
            <button class="action-btn">开始批改</button>
          </div> -->
        </div>
      </div>
    </div>

    <!-- AI能力中心 -->
    <div class="ai-center">
      <h2 class="section-title">AI能力中心</h2>
      <div class="ai-cards">
        <div class="ai-card">
          <div class="ai-icon">
            <img class="icon-placeholder" src="@/assets/ai-center1.png" alt="">
            <!-- <div class="icon-placeholder ai-icon-1"></div> -->
          </div>
          <h3>AI智能出题</h3>
          <p>智能分析知识点，快速生成题目</p>
          <button class="ai-btn" @click="startClick">开始使用</button>
        </div>
        <div class="ai-card">
          <div class="ai-icon">
            <img class="icon-placeholder" src="@/assets/ai-center2.png" alt="">
          </div>
          <h3>AI作文批改</h3>
          <p>智能评分，提供修改建议</p>
          <button class="ai-btn" @click="startClick">开始使用</button>
        </div>
        <div class="ai-card">
          <div class="ai-icon">
            <img class="icon-placeholder" src="@/assets/ai-center3.png" alt="">
          </div>
          <h3>AI教案生成</h3>
          <p>一键生成完整教学内容</p>
          <button class="ai-btn" @click="startClick">开始使用</button>
        </div>
        <div class="ai-card">
          <div class="ai-icon">
            <img class="icon-placeholder" src="@/assets/ai-center4.png" alt="">
          </div>
          <h3>AI教案评价</h3>
          <p>智能分析教案质量要点</p>
          <button class="ai-btn" @click="startClick">开始使用</button>
        </div>
      </div>
    </div>
    <new-base-info-input ref="newBaseInfoInput" @goXinZeng="goXinZeng"></new-base-info-input>
  </div>
</template>

<script>
import newBaseInfoInput from '@/views/threeAssistants/newJobResources/newBaseInfoInput'
export default {
  name: 'NewThreeHome',
  components: {
    newBaseInfoInput
  },
  data() {
    return {
      analysisData: {}
    }
  },
  activated() {
    this.getHomeAnalysis()
  },
  methods: {
    async getHomeAnalysis() {
      const res = await this.$uwonhttp.get('/Home/Home/GetHomeAnalysis')
      if (res.data.Success) {
        this.analysisData = res.data.Data
      }
    },
    //新增
    goXinZeng(data) {
      const { ChapterId, GradeId, Semester, TextbookId, Year, Unit } = data
      this.$router.push({
        path: '/threeAssistants/BulkImportEditing/Importing',
        query: {
          unitId: Unit,
          ChapterId,
          GradeId,
          Semester,
          TextbookId,
          Year,
        },
      })
    },
    startClick() {
      this.$message({
        message: '该功能暂未开放',
        type: 'warning',
      })
    }
  },
}
</script>

<style scoped lang="less">
@import './index.less';
</style>
