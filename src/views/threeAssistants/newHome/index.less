
.newThreeHome {
  margin: 0 15%;
  padding: 18px;
  background: white;
  overflow-y: auto;

  .section-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    position: relative;
  }

  // 数据概况
  .data-overview {
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    .data-cards {
      display: flex;
      gap: 24px;
      
      .data-card {
        flex: 1;
        background: white;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        
        // &::before {
        //   content: '';
        //   position: absolute;
        //   top: 0;
        //   left: 0;
        //   right: 0;
        //   height: 4px;
        //   background: #f5a623;
        //   border-radius: 8px 8px 0 0;
        // }
        
        .data-label {
          font-size: 18px;
          color: #000;
          font-weight: 400;
          margin-bottom: 12px;
        }
        
        .data-value {
          font-size: 32px;
          font-weight: bold;
          color: #333;
          
          .unit {
            font-size: 16px;
            font-weight: normal;
            color: #666;
            margin-left: 4px;
          }
        }
      }
      .card1{
        background: linear-gradient( 180deg, #F2F9FE 0%, #E6F4FE 100%);
      }
      .card2{
        background: linear-gradient( 180deg, #F5FEF2 0%, #E6FEEE 100%);
      }
      .card3{
        background: linear-gradient( 180deg, #FFEEEE 0%, #FFEFEF 100%);
      }
      .card4{
        background: linear-gradient( 180deg, #EAF9FF 0%, #D0F1FF 100%);
      }
    }
  }

  // 快捷功能
  .quick-functions {
    margin-bottom: 2%;
    padding: 20px;
    background: white;
    .function-cards {
      display: flex;
      gap: 24px;
      
      .function-card {
        flex: 1;
        background: white;
        // padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 20px;
        img{
          width: 100%;
        }
        .function-icon {
          .icon-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            
            &.icon-1 {
              background: linear-gradient(135deg, #7fb99a, #61bb96);
            }
            
            &.icon-2 {
              background: linear-gradient(135deg, #5fa9f7, #4a90e2);
            }
          }
        }
        
        .function-content {
          flex: 1;
          
          h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }
          
          p {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
          }
          
          .action-btn {
            background: #7fb99a;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              background: #61bb96;
            }
          }
        }
      }
    }
  }

  // AI能力中心
  .ai-center {
    margin-bottom: 2%;
    padding: 20px;
    background: white;
    .ai-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      
      .ai-card {
        background: white;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
        
        .ai-icon {
          margin-bottom: 16px;
          
          .icon-placeholder {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            margin: 0 auto;
            
            &.ai-icon-1 {
              background: #7fb99a;
            }
            
            &.ai-icon-2 {
              background: #5fa9f7;
            }
            
            &.ai-icon-3 {
              background: #f5a623;
            }
            
            &.ai-icon-4 {
              background: #e74c3c;
            }
          }
        }
        
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }
        
        p {
          font-size: 14px;
          color: #666;
          margin-bottom: 20px;
          line-height: 1.4;
        }
        
        .ai-btn {
          background: #7fb99a;
          color: white;
          border: none;
          padding: 8px 24px;
          border-radius: 20px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #61bb96;
          }
        }
      }
    }
  }
}

