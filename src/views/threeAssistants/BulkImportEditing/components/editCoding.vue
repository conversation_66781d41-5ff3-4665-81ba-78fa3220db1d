<template>
  <div class="Coding">
    <div class="_title">
      <h6>题目</h6>
<!--      <el-button type="warning">保 存</el-button>-->
    </div>
    <div class="_cont">
      <div
        v-for="(item, ind) in editPaperList"
        :key="ind"
        class="itemLi"
        :id="'rig'+ item.ScoolId"
        :style="{borderColor: 'rig'+item.ScoolId === activeId ? '#7FB99A' : '#FFFFFF'}"
        @click="itemClick(item)">
        <div class="_top">
          <div class="_left">
            <el-select size="medium" v-model="item.QuestionTypeId" @change="setQuestionTypeId(item,ind)" placeholder="请选择题型">
              <el-option label="填空题" :value="5"></el-option>
              <el-option label="单项选择题" :value="2"></el-option>
              <el-option label="多项选择题" :value="10"></el-option>
              <el-option label="判断题" :value="11"></el-option>
              <el-option label="主观题" :value="36"></el-option>
            </el-select>
            <el-button type="text" icon="el-icon-edit-outline" @click="$emit('editItem',{item,ind})">编辑题目</el-button>
            <el-popconfirm
              confirm-button-text='确定'
              cancel-button-text='取消'
              icon="el-icon-info"
              icon-color="red"
              @confirm="del(ind)"
              title="确认删除该题？"
            >
              <el-button type="text" slot="reference" icon="el-icon-delete">删除题目</el-button>
            </el-popconfirm>
          </div>
          <div class="_right">
            <el-tag type="warning" v-show="!item.QuestionTypeId">未选择题型</el-tag>
            <el-tag type="warning" v-show="answerPanDing(item)">缺少答案</el-tag>
          </div>
        </div>
        <div class="_botm">
<!--          <div class="tiHao">{{ind+1}}.</div>-->
          <div class="title">
            {{ind+1}}
            <span v-html="item.Title" v-katex></span>
          </div>
          <ul v-if="[2,10,11].includes(item.QuestionTypeId) && item.Options?.length">
            <li v-for="(ite,ind) in item.Options" :key="ind+ite.Content" v-katex>
<!--              <span>{{ite.Option}}</span> : <span>{{ite.Content}}</span>-->
              <span>{{ite.Option}}</span> : <span v-html="ite.Content"></span>
            </li>
          </ul>
        </div>
        <div class="_answer">
          <p><span>*</span>{{ item.QuestionTypeId !== 36 ? '答案：' : '答案类型：' }}</p>
          <div>
<!--      单选，判断      -->
            <div v-if="[2,11].includes(item.QuestionTypeId)">
              <el-radio-group v-model="item.Answer" @input="setSession">
                <el-radio v-for="(ite,ins) in item.Options" :key="ins" :label="ite.Option">{{ ite.Option }}</el-radio>
              </el-radio-group>
            </div>
<!--      多项选择      -->
            <div v-if="item.QuestionTypeId === 10">
              <el-checkbox-group v-model="item.Answer" @change="setSession">
                <el-checkbox v-for="(ite,its) in item.Options" :key="its" :label="ite.Option">{{ite.Option}}</el-checkbox>
              </el-checkbox-group>
            </div>
<!--      主观题      -->
            <div v-if="item.QuestionTypeId === 36">
              <el-radio-group v-model="item.ResponseFormat" @input="setSession">
                <el-radio :label="1">拍照</el-radio>
                <el-radio :label="2">录音</el-radio>
                <el-radio :label="3">视频</el-radio>
              </el-radio-group>
            </div>
<!--      填空题      -->
            <div class="tiankong" v-if="item.QuestionTypeId === 5">
              <UWEditor
                v-for="(ans,anInd) in item.itemNewAnswer"
                :key="anInd+ind"
                class="uweditor-view"
                :height="ind + anInd == editingNum ? 100 : 40"
                :idx="ind + ',' + anInd"
                :content="ans"
                @updateEditItem="getEditData"
                @editorStateChange="editorStateChange"></UWEditor>
              <!-- <div class="Txt" v-for="(ans,anInd) in item.itemNewAnswer" v-html="ans" v-katex @click="onClickHandel(anInd, ind, ans)"></div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <edit-dialog ref="editDialog" @updateEditItem="getEditData"></edit-dialog>
  </div>
</template>

<script>
import UWEditor from '@/views/threeAssistants/UWEditor'
import editAnswerList from '@/views/threeAssistants/BulkImportEditing/components/editAnswerList'
import fillInTheBlanks from '@/views/threeAssistants/components/itemComponets/fillInTheBlanks'
import onlyMoreSelect from '@/views/threeAssistants/components/itemComponets/onlyMoreSelect'
import subjectiveItem from '@/views/threeAssistants/components/itemComponets/subjectiveItem'
import EditDialog from '@/views/Paper/Exam_Paper/Components/EditDialog'
import { Session } from '@/utils/storage.js';

export default {
  name: 'editCoding',
  props:{
    // 操作区编辑数据
    editPaperList:{
      type: Array,
      default(){
        return []
      }
    },
    // 原始数据
    rawData:{
      type: Array,
      default() {
        return []
      }
    }
  },
  components:{
    editAnswerList,
    fillInTheBlanks,
    onlyMoreSelect,
    subjectiveItem,
    EditDialog,
    UWEditor
  },
  data() {
    return {
      // newSolution:{},
      // listIndex: 0,
      // showHiden: false
      itemInd: 0, // 填空题使用：记录点击试题对象的下标
      dialogInd: 0, // 填空题使用：记录填空题空的下标
      activeId: '',
      editingNum: ''
    }
  },
  methods: {
    // 调取富文本输入框
    onClickHandel(anInd, ind, content) {
      this.itemInd = ind
      this.dialogInd = anInd
      this.$refs.editDialog.init('答案', content)
    },
    editorStateChange(e, idx) {
      const ind = idx.split(',')
      this.itemInd = parseInt(ind[0]) // 第几题
      this.dialogInd = parseInt(ind[1]) // 第几空
      this.editingNum = e ? this.itemInd + this.dialogInd : ''
      // this.editPaperList[this.itemInd].itemNewAnswer[this.dialogInd] = e
      // this.$forceUpdate()
    },
    getEditData(val) {
      this.$set(this.editPaperList[this.itemInd].itemNewAnswer, this.dialogInd, val)
      this.editPaperList[this.itemInd].Answer = this.editPaperList[this.itemInd].itemNewAnswer.join('|')
      Session.set('importItemList', this.editPaperList)
    },
    // 答案判定
    answerPanDing(item){
      const { QuestionTypeId, Answer, itemNewAnswer } = item
      if(QuestionTypeId === 5){
        if(itemNewAnswer.findIndex(s => s === '') > -1){
          return true
        } else {
          return false
        }
      }
      if(QuestionTypeId === 10){
        return Answer.length == 0
      }
      if([2,11].includes(QuestionTypeId)){
        return Answer === ''
      }
      if (QuestionTypeId === 36){
        return false
      }
    },
    // 切换题型
    setQuestionTypeId(item,ind){
      // 操作区数据
      const { QuestionTypeId, ScoolId, Title } = item
      // 原始数据
      const itemObj = this.rawData.find(item => item.ScoolId === ScoolId)
      if(QuestionTypeId === itemObj.QuestionTypeId || (QuestionTypeId !== 11 && itemObj.Options.length)){
        this.editPaperList[ind].Options = itemObj.Options
      } else {
        this.$nextTick(() => {
          // 判断题更改操作区Options数据
          if(QuestionTypeId === 11){
            this.editPaperList[ind].Options = [
              { Option: 'A', Content: '对' },
              { Option: 'B', Content: '错' }
            ]
          }
          if([2,10].includes(QuestionTypeId)){
            this.editPaperList[ind].Options = [
              { Option: 'A', Content: ''},
              { Option: 'B', Content: ''},
              { Option: 'C', Content: ''},
              { Option: 'D', Content: ''},
            ]
          }
          if([5,36].includes(QuestionTypeId)){
            this.editPaperList[ind].Options = []
          }
        })
      }
      // 题型改变后答案Answer重置
      if([2,11,36,5].includes(QuestionTypeId)){
        this.editPaperList[ind].Answer = ''
      } else {
        this.editPaperList[ind].Answer = []
      }
      // const regx = /（.*?）/g
      // let regxList = Title.match(regx)
      // if(regxList){
      //   this.editPaperList[ind].itemNewAnswer = regxList.map(ite => ite.replace(/\（|\）/g, '').trim())
      // }
      this.setSession()
    },
    // 更正Session值
    setSession(){
      Session.set('importItemList', this.editPaperList)
    },
    // 删除题目
    del(ind){
      this.editPaperList.splice(ind, 1)
      this.setSession()
      this.$message.success('删除成功')
      // console.log('删除试题')
      this.$emit('resetLeft')
    },
    // 锚点跳转
    executeAnchorPoint(id){
      const el = document.getElementById(id)
      if(el){
        this.activeId = id
        el.scrollIntoView({ behavior: "smooth"})
      }
    },
    // 试题点击
    itemClick(item){
      this.activeId = 'rig'+item.ScoolId
      this.$emit('anchorPoint', { type: 'lef', id: item.ScoolId })
    },
    // // 拿去题干和选项
    // init(obj){
    //   // console.log('$$$$$$$$$$$$$$$$$$$$$$')
    //   // console.log(obj)
    //   if (!obj) return
    //   const { item, ind } = obj
    //   this.listIndex = ind
    //   // console.log(getItemOptions.getItem(ind))
    //   if(getItemOptions.getItem(ind)){
    //     this.$nextTick(() => {
    //       this.itemObj = getItemOptions.getItem(ind)
    //     })
    //     if([2,10].includes(item.QuestionTypeId)){
    //       const timer = setTimeout(() => {
    //         this.$refs.threeBasicsItem.addEditList(item.Options)
    //         clearTimeout(timer)
    //       },200)
    //     }
    //   } else {
    //     this.clearCentralAll()
    //     this.itemObj.RelevanceErrors = []
    //     this.itemObj.QuestionTypeId = item.QuestionTypeId
    //     this.itemObj.Title = item.Title
    //     this.itemObj.Options = item.Options
    //     this.itemObj.Analysis = item.Analysis
    //     this.itemObj.Misattribution = item.Misattribution
    //     if([2,10].includes(item.QuestionTypeId)){
    //       this.$nextTick(() => {
    //         this.$refs.threeBasicsItem.addEditList(item.Options)
    //       })
    //     }
    //   }
    // },
    // // 展开收起事件
    // encodingShowHide(){
    //   this.showHiden = !this.showHiden
    // },
    // // 清空必填项
    // clearCentralAll(){
    //   // console.log('走清空逻辑')
    //   this.$nextTick(() => {
    //     this.itemObj.Answer = ''
    //     this.itemObj.Analysis = ''
    //     // this.itemObj.CourseVersionsId = ''
    //     this.itemObj.DifficultyId = ''
    //     this.itemObj.LearningLevelId = ''
    //     this.itemObj.LearningGuidance = ''
    //     this.itemObj.ContentDomainId = ''
    //     this.itemObj.QuestionCode = ''
    //     this.itemObj.AnalysisFile = null
    //     this.itemObj.GuidanceFile = null
    //   })
    // },
    // setBotmAll(obj){
    //   this.itemObj[obj.key] = obj.value
    // },
    // setCentralAll(obj){
    //   this.itemObj[obj.key] = obj.value
    // },
    // getAudioVadeo(obj){
    //   this.itemObj.GuidanceFile = obj
    // },
    // getEditData(data) {
    //   this.newContent = data
    //   this.$emit('update:content', data)
    // },
    // getList(){
    //   /*暂时这样处理留意问题点*/
    //   // if(getItemOptions.getItem(this.listIndex)) return true
    //   const type = this.itemObj.QuestionTypeId
    //   if (type === 2 || type === 10) { // 单选/多选
    //     const { Answer, Options, Errors } = this.$refs.threeBasicsItem.getlist();
    //     this.newSolution = {
    //       Options,
    //       Errors,
    //       Answer: type === 10 ? Answer.join('|') : Answer
    //     };
    //   } else if (type === 11) { // 判断题
    //     const { Answer, Options, Errors } = this.$refs.panDuan.getlist();
    //     this.newSolution = {
    //       Answer,
    //       Options,
    //       Errors
    //     };
    //   } else if(type === 5){ // 填空题
    //     // 数组转字符串
    //     this.newSolution = {
    //       Answer: this.$refs.fillSpaces.getAnswer().join('|'),
    //       OtherAnswer: this.$refs.fillSpaces.getOtherAnswer(),
    //       Options: [],
    //       Errors: []
    //     }
    //     this.itemObj.RelevanceErrors = this.$refs.fillSpaces.getRelevanceErrors()
    //   }
    //   // 错因汇总数据库处理
    //   this.itemObj.ErrorCollect = this.setError(this.itemObj.ErrorCollect)
    //   if(this.checkQuestionCompleteness()){
    //     this.$message.error('请完善必填信息')
    //     return false
    //   }
    //   this.setSessionStorage()
    //   return true
    // },
    // // 存储值
    // setSessionStorage(){
    //   const { Answer, OtherAnswer, Options, Errors } = this.newSolution
    //   const retObj = {
    //     ...this.itemObj,
    //     Answer: Answer,
    //     OtherAnswer: OtherAnswer,
    //     Options: Options,
    //     Errors: Errors,
    //   }
    //   getItemOptions.resetItem(this.listIndex,retObj)
    //   // 清空必填项
    //   this.clearCentralAll()
    // },
    // // 检查题目信息是否完整
    // checkQuestionCompleteness() {
    //   const requiredFields = [
    //     this.itemObj.Title,
    //     // this.itemObj.Analysis,
    //     // this.itemObj.CourseVersionsId,
    //     // this.itemObj.LearningLevelId,
    //     // this.itemObj.DifficultyId,
    //     // this.itemObj.ContentDomainId
    //   ];
    //   // 根据 QuestionTypeId 决定是否检查答案
    //   const checkAnswer = [2, 10, 11, 5].includes(this.itemObj.QuestionTypeId);
    //
    //   // 如果需要检查答案，将答案添加到必填字段列表中
    //   const fieldsToCheck = checkAnswer ? [...requiredFields, this.newSolution.Answer] : requiredFields;
    //   // console.log(fieldsToCheck)
    //   // 检查是否有空字符串
    //   return fieldsToCheck.includes('');
    // },
    // setError(arr){
    //   return arr.map(item => {
    //     return {
    //       Content: '',
    //       ErrorId: item,
    //     }
    //   })
    // },
  }
}
</script>

<style lang="less" scoped>
//.OpenAndClose{
//  color: #7FB99A;
//  font-size: 18px;
//}
.Coding{
  width: 50%;
  height: 84.9vh;
  background-color: #FFFFFF;
  margin-left: 5px;
  ._title{
    padding: 12px 24px;
    border-bottom: 1px solid #cccccc;
    color: #333333;
    h6{
      font-weight: normal;
      font-size: 20px;
    }
  }
  ._cont{
    //height: 93%;
    height: 92.8%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 24px;
    .itemLi{
      padding: 8px 12px 12px;
      border-radius: 4px;
      margin: 16px 0;
      border: 1px solid #FFFFFF;
      ._top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        ._left{
          .el-select{
            width: 140px;
            margin-right: 16px;
          }
          .el-button{
            font-size: 16px;
            color: #484949;
            margin-right: 6px;
          }
          .el-button:hover{
            color: #7FB99A;
          }
        }
        ._right{
          .el-tag{
            height: 28px;
            line-height: 26px;
            margin: 0 6px;
          }
        }
      }
      ._botm{
        //display: flex;
        //.tiHao{
        //  font-size: 16px;
        //  margin-top: 10px;
        //}
        /deep/.title{
          font-size: 17px;
          margin: 10px 0;
        }
        ul{
          list-style: none;
          padding-left: 14px;
          margin-bottom: 6px;
          li{
            display: flex;
            align-items: center;
            font-size: 17px;
          }
        }
      }
      ._answer{
        display: flex;
        align-items: center;
        p{
          min-width: 60px;
          font-size: 16px;
          color: #6d7278;
          span{
            color: #FF0000FF;
          }
        }
        .tiankong{
          // display: flex;
          // align-items: center;
          // flex-wrap: wrap;
          width: 600px;
          .uweditor-view{
            width: 90%;
            //width: 540px;
            margin: 4px 0 4px 6px;
          }
        }

      }
    }
    .itemLi:hover{
      border-color: #7FB99A !important;
    }
  }
  //._cont::-webkit-scrollbar {
  //  display: none;
  //}
  // 美化滚动条
  ._cont::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ._cont::-webkit-scrollbar-track {
    width: 6px;
    background: rgba(#101F1C, 0.1);
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  ._cont::-webkit-scrollbar-thumb {
    background-color: rgba(144,147,153,.5);
    background-clip: padding-box;
    min-height: 28px;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
    transition: background-color .3s;
    cursor: pointer;
  }

  ._cont::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144,147,153,.3);
  }
}
</style>