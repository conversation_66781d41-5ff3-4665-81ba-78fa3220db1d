<template>
  <div class="itemEditDrawer">
    <el-drawer
      title="题目编辑"
      :visible.sync="drawer"
      :direction="direction"
      size="50%"
      :before-close="handleClose">
      <div class="editItem">
        <div class="radioBtn">
          <el-radio-group v-model="showHiden" >
            <el-radio-button :label="false">核心内容</el-radio-button>
            <el-radio-button :label="true">题目属性</el-radio-button>
          </el-radio-group>
        </div>
        <div class="listScooll">
          <div v-show="!showHiden">
            <el-select size="medium" v-model="itemObj.QuestionTypeId" @change="setQuestionTypeId" placeholder="请选择题型">
              <el-option label="填空题" :value="5"></el-option>
              <el-option label="单项选择题" :value="2"></el-option>
              <el-option label="多项选择题" :value="10"></el-option>
              <el-option label="判断题" :value="11"></el-option>
              <el-option label="主观题" :value="36"></el-option>
            </el-select>
            <public-textarea txt="题干" :must-have="true" :content.sync="itemObj.Title" :itemId="itemObj.QuestionTypeId"></public-textarea>
            <!--    &lt;!&ndash;   题型答案区   &ndash;&gt;-->
            <tit v-show="itemObj.QuestionTypeId !== 36" txt="答案" :MustHave="true"></tit>
            <!--      &lt;!&ndash; 填空 &ndash;&gt;-->
            <fill-spaces
              v-if="itemObj.QuestionTypeId === 5"
              ref="fillSpaces"
              :title="itemObj.Title"
              :Answer="itemObj.Answer"
              :RelevanceErrors="[]"
              :OtherAnswer="itemObj.OtherAnswer">
            </fill-spaces>
            <!--      &lt;!&ndash; 单选、多选 &ndash;&gt; -->
            <three-basics-item
              v-if="[2,10].includes(itemObj.QuestionTypeId)"
              ref="threeBasicsItem"
              :option="itemObj.Options"
              :answer="itemObj.Answer"
              :errors="itemObj.Errors"
              :type-id="itemObj.QuestionTypeId">
            </three-basics-item>
            <!--      &lt;!&ndash; 判断 &ndash;&gt;-->
            <pan-duan
              v-if="itemObj.QuestionTypeId === 11"
              :answer="itemObj.Answer"
              :errors="itemObj.Errors"
              ref="panDuan"></pan-duan>
            <!--      &lt;!&ndash; 主观 &ndash;&gt;-->
            <public-textarea v-if="itemObj.QuestionTypeId === 36" txt="参考答案" :content.sync="itemObj.ReferenceAnswer"></public-textarea>
            <public-subjective
              v-if="itemObj.QuestionTypeId === 36"
              ref="publicSubjective"
              :answer-form.sync="itemObj.ResponseFormat"></public-subjective>
            <!--      &lt;!&ndash;  错因汇总 &ndash;&gt;-->
            <public-error v-if="[5,10].includes(itemObj.QuestionTypeId)" title-name="错因汇总" class="topicMt" :err.sync="itemObj.ErrorCollect" :CuoYinList="CuoYinList"></public-error>
            <div class="cont">
              <encoding-system1
                :difficulty-id="itemObj.DifficultyId"
                :learning-level-id="itemObj.LearningLevelId"
                :LearningGuidance="itemObj.LearningGuidance"
                v-on:changeInput="setCentralAll"></encoding-system1>
              <div style="width: 48%">
                <upload-audio-video :show-obj="itemObj.GuidanceFile" title="辅导" @getAudioVadeo="getAudioVadeo"></upload-audio-video>
              </div>
            </div>
          </div>
          <encoding-system2
            v-show="showHiden"
            :unit-target-id="itemObj.UnitTargetId"
            :chapter-target-id="itemObj.ChapterTargetId"
            :question-target-id="itemObj.QuestionTargetId"
            :core-literacy-id="itemObj.CoreLiteracyId"
            :question-source="itemObj.QuestionSource"
            :finish-time="itemObj.FinishTime"
            :photograph-set="itemObj.PhotographSet"
            :scene-attribute-id="itemObj.SceneAttributeId"
            :question-code="itemObj.QuestionCode"
            :theme-id="itemObj.ThemeId"
            :content-domain-id="itemObj.ContentDomainId"
            :analysis="itemObj.Analysis"
            :misattribution="itemObj.Misattribution"
            :AnalysisFile="itemObj.AnalysisFile"
            @setAnalysisFile="(val) => { itemObj.AnalysisFile = val }"
            @centralAllMisattribution="val => { itemObj.Misattribution = val }"
            @tiganAnalysis="val => { itemObj.Analysis = val }"
            @setTime="val => { itemObj.FinishTime = val }"
            @setUnit="val => { itemObj.UnitTargetId = val }"
            v-on:changeInput="setBotmAll"
          ></encoding-system2>
          <!-- <el-divider content-position="left">
            <el-button class="OpenAndClose" type="text" @click="encodingShowHide">{{ showHiden ? '收起' : '展开' }}题目属性<i :class="[ showHiden ? 'el-icon-arrow-up' : 'el-icon-arrow-down' ,'el-icon--right']"></i></el-button>
          </el-divider> -->
        </div>
        <div class="claBtn">
          <el-button size="medium" @click="handleClose">取消</el-button>
          <el-button size="medium" type="primary" @click="trueSubmit">确定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import publicTextarea from '@/views/threeAssistants/AddANewTopic/components/module/publicTextarea'
import tit from '@/views/threeAssistants/AddANewTopic/components/module/tit'
import fillSpaces from '@/views/threeAssistants/components/edit/editAnswer/fillSpaces'
import threeBasicsItem from '@/views/threeAssistants/components/edit/editAnswer/threeBasicsItem'
import panDuan from '@/views/threeAssistants/AddANewTopic/components/module/panDuan'
import publicSubjective from '@/views/threeAssistants/AddANewTopic/components/module/publicSubjective'
import publicError from '@/views/threeAssistants/AddANewTopic/components/module/publicError'
import encodingSystem1 from '@/views/threeAssistants/AddANewTopic/components/rightCom/encodingSystem1'
import uploadAudioVideo from '@/views/threeAssistants/AddANewTopic/components/rightCom/uploadAudioVideo'
import encodingSystem2 from '@/views/threeAssistants/AddANewTopic/components/rightCom/encodingSystem2'
import { Session } from '@/utils/storage.js';

export default {
  name: 'itemEditDrawer',
  props:{
    rawData:{
      type: Array,
      default(){
        return []
      }
    }
  },
  components:{
    publicTextarea,
    tit,
    fillSpaces,
    threeBasicsItem,
    panDuan,
    publicSubjective,
    publicError,
    encodingSystem1,
    uploadAudioVideo,
    encodingSystem2
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      // 对象值
      // itemObj: {
      //   // QuestionTypeId: null,
      //   QuestionTypeId: 11,
      //   Year: this.$route.query.Year, //学年
      //   Semester: this.$route.query.Semester, // 学期
      //   GradeId: this.$route.query.GradeId, // 年级Id
      //   SubjectId: localStorage.getItem('UW_SUBJECT'), // 学科Id
      //   TextbookId: this.$route.query.TextbookId, // 教材Id
      //   ChapterId: this.$route.query.ChapterId, // 章节Id
      //   UserId: localStorage.getItem('UserId'), // 创建人Id
      //   UserRoleId: localStorage.getItem('role'),
      //   ResponseFormat: 1, // 答题形式
      //   ShareSet: 1, // 共享设置
      //   ShareAreaCity: 1, // 共享范围
      //   Hint: '', // 作答提示
      //   SpecialItemConfig: '', // 特殊提醒
      //   ReferenceAnswer: '', // 参考答案
      //   Answer:'', // 答案
      //   OtherAnswer:[], // 多答案
      //   Options:[], // 选项
      //   Errors:[],
      //   Id:'', // 编辑时传值，新增传空
      //   Title:'', // 题干
      //   Analysis:'略', // 题目解析
      //   CourseVersionsId:'1811293692187680768', // 课程版本Id
      //   QuestionCode:'', // 题目编号
      //   DifficultyId:'', // 题目难度Id
      //   LearningLevelId:'', // 学习水平Id
      //   LearningGuidance:'', // 学习指导
      //   Misattribution:'', // 错误归因
      //   GuidanceFile:null, // 学习指导
      //   AnalysisFile:null, // 解析文件
      //   UnitTargetId:'', // 单元目标Id
      //   ChapterTargetId:'', // 章节目标Id
      //   QuestionTargetId:'', // 题目目标Id
      //   ThemeId: '', // 主题Id
      //   ContentDomainId:'', // 内容领域Id
      //   CoreLiteracyId:'', // 核心素养Id
      //   SceneAttributeId:null, // 情景属性Id
      //   QuestionSource:'', // 题目来源
      //   PhotographSet:'', // 拍照设置
      //   FinishTime:1, // 预计完成时间分钟
      //   ErrorCollect:[], // 错因汇总
      //   RelevanceErrors:[], // 填空题错因与答案的绑定
      // },
      itemObj:{},
      newSolution:{},
      listIndex: 0, // 缓存数据的下标值
      showHiden: false,
      CuoYinList: [], // 错因数据
    }
  },
  methods:{
    init(item,ind){
      console.log(item)
      this.showHiden = false
      this.itemObj = item
      this.listIndex = ind
      if([2,10].includes(item.QuestionTypeId)){
        const timer = setTimeout(() => {
          this.$refs.threeBasicsItem.addEditList(item.Options)
          clearTimeout(timer)
        },200)
      }
      this.GetWrongCause()
      // console.log(this.itemObj)
      this.drawer = true
    },
    // 是否开启抽屉
    handleClose() {
      this.itemObj = {}
      this.newSolution = {}
      this.drawer = false
    },
    setBotmAll(obj){
      this.itemObj[obj.key] = obj.value
    },
    setCentralAll(obj){
      this.itemObj[obj.key] = obj.value
    },
    getAudioVadeo(obj){
      this.itemObj.GuidanceFile = obj
    },
    // 展开收起事件
    encodingShowHide(){
      this.showHiden = !this.showHiden
    },
    // 题型切换
    setQuestionTypeId(val){
      this.clearCentralAll()
      let options = []
      if([2,10].includes(val)){
        options = [
          { Option: 'A', Content: ''},
          { Option: 'B', Content: ''},
          { Option: 'C', Content: ''},
          { Option: 'D', Content: ''},
        ]
        const state = this.rawData.find(item => item.ScoolId === this.itemObj.ScoolId)
        console.log(state)
        const time = setTimeout(() => {
          // this.$refs.threeBasicsItem.addEditList(this.itemObj.Options.length ? this.itemObj.Options : options)
          this.$refs.threeBasicsItem.addEditList(state && state.Options.length ? state.Options : options)
          clearTimeout(time)
        },200)
      }
    },
    // // 清空必填项
    clearCentralAll(){
      this.$nextTick(() => {
        this.itemObj.Answer = ''
        this.itemObj.Analysis = ''
        // this.itemObj.CourseVersionsId = ''
        this.itemObj.DifficultyId = ''
        this.itemObj.LearningLevelId = ''
        this.itemObj.LearningGuidance = ''
        this.itemObj.ContentDomainId = ''
        this.itemObj.QuestionCode = ''
        this.itemObj.AnalysisFile = null
        this.itemObj.GuidanceFile = null
      })
    },
    // 确定事件
    // trueSubmit(){
      // const { Answer, OtherAnswer, Options, Errors } = this.newSolution
      // const importItemList = Session.get('importItemList')
      // importItemList.splice(this.listIndex, 1, this.itemObj)
      // Session.set('importItemList', importItemList)
      // return false
      // this.$emit('update:itemObj', this.itemObj)
    // },
    // 排列数据
    trueSubmit(){
      /*暂时这样处理留意问题点*/
      // if(getItemOptions.getItem(this.listIndex)) return true
      const type = this.itemObj.QuestionTypeId
      if (type === 2 || type === 10) { // 单选/多选
        const { Answer, Options, Errors } = this.$refs.threeBasicsItem.getlist();
        // console.log(Answer)
        this.newSolution = {
          Options,
          Errors,
          Answer
        };
      } else if (type === 11) { // 判断题
        const { Answer, Options, Errors } = this.$refs.panDuan.getlist();
        this.newSolution = {
          Answer,
          Options,
          Errors
        };
      } else if(type === 5){ // 填空题
        // 数组转字符串
        this.newSolution = {
          Answer: this.$refs.fillSpaces.getAnswer().join('|'),
          OtherAnswer: this.$refs.fillSpaces.getOtherAnswer(),
          Options: [],
          Errors: []
        }
        this.itemObj.RelevanceErrors = this.$refs.fillSpaces.getRelevanceErrors()
      }
      // 错因汇总数据库处理
      this.itemObj.ErrorCollect = this.setError(this.itemObj.ErrorCollect)
      if(this.checkQuestionCompleteness()){
        // this.$message.error('请完善必填信息')
        this.$message.error('题型、题干与答案不能为空！')
        return false
      }
      // 更换session中的数据
      const { Answer, OtherAnswer, Options, Errors } = this.newSolution
      const retObj = {
        ...this.itemObj,
        Answer: Answer,
        OtherAnswer: OtherAnswer,
        Options: Options,
        Errors: Errors,
        itemNewAnswer: this.itemObj.QuestionTypeId === 5 ? Answer.split('|') : [],
      }
      const importItemList = Session.get('importItemList')
      importItemList.splice(this.listIndex, 1, retObj)
      Session.set('importItemList', importItemList)
      this.$message({
        message: '题目编辑成功',
        type: 'success'
      })
      this.$emit('updateList')
      this.handleClose()
    },
    // 检查题目信息是否完整
    checkQuestionCompleteness() {
      console.log(this.newSolution.Answer)
      if(!this.itemObj.QuestionTypeId) return true
      const requiredFields = [
        this.itemObj.Title,
        // this.itemObj.Analysis,
        // this.itemObj.CourseVersionsId,
        // this.itemObj.LearningLevelId,
        // this.itemObj.DifficultyId,
        // this.itemObj.ContentDomainId
      ];
      // 根据 QuestionTypeId 决定是否检查答案
      const checkAnswer = [2, 10, 11, 5].includes(this.itemObj.QuestionTypeId);

      // 如果需要检查答案，将答案添加到必填字段列表中
      const fieldsToCheck = checkAnswer ? [...requiredFields, this.newSolution.Answer] : requiredFields;
      // console.log(fieldsToCheck)
      // 检查是否有空字符串
      // 判断fieldsToCheck数组里面是否存在空字符串和空数组
      if (fieldsToCheck.some(item => item === '' || item === null || item === undefined || item.length === 0)) {
        return true;
      }
      // return fieldsToCheck.includes('');
    },
    // 错因
    async GetWrongCause(){
      const { data: res } = await this.$uwonhttp.get('/Question/Question/GetWrongCause?grade='+this.$route.query.GradeId)
      res.Success ? this.CuoYinList = res.Data : this.$message.error(res.Msg)
    },
    setError(arr){
      return arr.map(item => {
        return {
          Content: '',
          ErrorId: item,
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
//.OpenAndClose{
//  color: #7FB99A;
//  font-size: 18px;
//}
.itemEditDrawer{
  /deep/.el-drawer__header{
    font-size: var(--font16-22) !important;
    margin-bottom: 0 !important;
  }
  .radioBtn{
    padding: 20px;
    /deep/.el-radio-group{
      border: 1px solid #7fb99a;
      border-radius: 8px;
      overflow: hidden;
      display: inline-flex;
      .el-radio-button {
        margin: 0;
        .el-radio-button__inner {
          border: none !important;
          border-radius: 0 !important;
          font-size: 18px;
          font-weight: 500;
          color: #7fb99a;
          background: #fff;
          padding: 0 20px;
          height: 38px;
          line-height: 38px;
          transition: all 0.2s;
        }
        &.is-active .el-radio-button__inner,
        .el-radio-button__inner.is-active {
          background: #7fb99a !important;
          color: #fff !important;
          border: none !important;
        }
      }
      .el-radio-button:first-child .el-radio-button__inner {
        border-top-left-radius: 5px !important;
        border-bottom-left-radius: 5px !important;
      }
      .el-radio-button:last-child .el-radio-button__inner {
        border-top-right-radius: 5px !important;
        border-bottom-right-radius: 5px !important;
      }
    }
  }
  .editItem{
    height: 100%;
    padding: 0 20px;
    .listScooll{
      height: calc(100% - 40px);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      .topicMt{
        margin: 10px 0;
      }
      .el-select{
        margin-bottom: 10px;
      }
    }
    .listScooll::-webkit-scrollbar {
      display: none;
    }
    .claBtn{
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
}
</style>