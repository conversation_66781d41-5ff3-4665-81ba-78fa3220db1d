<template>
  <div class="impro">
    <div class="title" v-show="headerState">
      <div class="_left">
        <i class="el-icon-arrow-left" style="cursor: pointer;" @click="goBack"></i>
        <h6>导入题目</h6>
      </div>
<!--      <div>AI生成</div>-->
    </div>
    <div class="cont">
      <div v-show="false" class="_left">
        <div class="_title">
          <p>方法一：文件导入</p>
          <div class="tipsForDownloading">
            <el-tooltip placement="bottom-start" :visible-arrow="false">
              <div slot="content" class="pList">
                <h6>
                  支持题型：单项选择题，多项选择题，判断题，填空题，主观题
                </h6>
                <p>导入示例：</p>
                <p>题型：单选题</p>
                <p>1、小巧看完一本 352 页的童话书，第一周看了 135 页，第二周看了 165 页，还剩下多少页没有看完?关于如何解决问题，下列说法正确的是( )</p>
                <p>A、只能列算式 352-135-165 解答</p>
                <p>B、只能列算式 352-(135+165)解答</p>
                <p>C、两个算式352-135-165或352-(135+165)都可以解答</p>
                <p>D、丁、两个算式352-135-165或352-(135+165)都不可以解答</p>
              </div>
              <i style="font-size: 24px;color: #E6A23C;margin-left: 10px;cursor: pointer;" class="el-icon-question"></i>
            </el-tooltip>
            <el-link class="elLink" type="primary" href="https://eduwpsfile.obs.cn-east-2.myhuaweicloud.com/A4-three-template.docx" target="_blank">下载Word文件模版</el-link>
          </div>
        </div>
        <div class="_content">
          <el-upload
            class="upload-demo"
            ref="elUploadList"
            :show-file-list="false"
            :http-request="httpRequest"
            :limit="1"
            action="#"
            :on-exceed="handleExceed"
            accept=".doc,.docx"
            drag
            multiple>
            <i class="el-icon-document"></i>
            <div class="text">请将文件拖拽到此区域上传试题或选择文件上传试题</div>
            <div class="xianzhi">仅支持Word文件上传</div>
            <em>选择文件</em>
          </el-upload>
        </div>
      </div>
      <div class="_right" :style="{width: '100%'}">
        <div class="_title">
          <p class="_Ai">AI智能识别</p>
        </div>
        <el-upload
          class="upload-demo"
          ref="uploadDemo"
          drag
          action="#"
          :http-request="httpRequestImgPdf"
          accept=".pdf,.doc,.docx"
          :limit="1"
          :show-file-list="false"
          multiple>
          <i class="iconfont icon-zhinengshibie"></i>
          <div class="el-upload__text">
            <p>请将PDF或WORD拖拽此区域上传试题或选择文件上传试题</p>
            <span class="spanTit">智能识别题目，并根据题目自动匹配解析、学习指导、素养等属性</span>
            <div><el-button type="primary">选择文件</el-button></div>
          </div>
        </el-upload>
      </div>
    </div>
    <!--  解析等待  -->
    <div :class="retRoutePath?'_waiting':'_waitingDialog'" v-show="aiDengDai">
      <h1>提示</h1>
      <div class="gif">
        <img src="@/assets/aigif.gif" />
      </div>
      <p>正在为您智能解析题目，请稍等...</p>
    </div>
  </div>
</template>

<script>
import { getItemOptions } from '@/views/threeAssistants/BulkImportEditing/getItemOptions'
import documentExport from '@/utils/documentExport'
export default {
  name: 'Importing',
  data() {
    return {
      tit: '导入题目',
      aiTextValue: '',
      value1: [],
      num: 4,
      // 题型数据
      itemList:[],
      aiDengDai: false
    }
  },
  props:{
    headerState:{
      type: Boolean,
      default: true
    },
    retRoutePath:{
      type: Boolean,
      default: true
    },
    queryInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed:{
    // retRoutePath() {
    //   return this.$route.path !== '/threeAssistants/JobDesign/NewJob'
    // }
  },
  created() {
    this.GetQuestionType()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    // 自定义上传数据
    async httpRequest(params) {
      // sessionStorage.removeItem('activeModeState')
      const fromData = new FormData()
      fromData.append('file',params.file)
      const { data:res } = await this.$uwonhttp.post('/Question/Question/QuestionImportByWord',fromData)
      if(res.Success) {
        getItemOptions.remove('importOptionsList') // 清空缓存
        const queryParams = {
          fileId: res.Data,
          // disabled: true,
          ...this.$route.query,
          type: 'word'
        };
        if(!this.headerState){
          queryParams['Redirection'] = '/threeAssistants/JobDesign/NewJob'
        }
        this.$router.push({
          path: '/threeAssistants/BulkImportEditing/index',
          query: queryParams
        })
        this.$refs.elUploadList.clearFiles()
      }else{
        this.$message.error(res.Msg)
      }
    },
    handleExceed() {
      this.$message.warning(`当前限制导入一个文件，请删除文件后再重新导入！`);
    },
    // 题型选择多选
    clickSelect(e){
      console.log('处理题型多选逻辑')
    },
    // 获取题型数据
    async GetQuestionType() {
      const { data: res } = await this.$uwonhttp.get('/Question/Question/GetQuestionType')
      res.Success ? this.radioList = res.Data : this.$message.error(res.Msg)
      if(res.Success){
        this.itemList = res.Data
        // this.$emit('radioBtn',res.Data[0].QuestionType)
      } else {
        this.$message.error(res.Msg)
      }
    },
    /* PDF识别逻辑暂时注释掉 */
    // 上传图片或pdf
    async httpRequestImgPdf(params) {
      this.aiDengDai = true
      this.setSharedSettings()
      const { ChapterId, unitId } = this.$route.query
      const { GradeId } = this.$store.state.chapterStorage.ChapterObj
      const url = `/Question/Question/PdfIntelligentImport?userId=${localStorage.getItem('UserId')}&subjectId=${localStorage.getItem('UW_SUBJECT')}&unitId=${unitId?unitId:this.queryInfo.unitId}&unitName=随便&chapterId=${ChapterId?ChapterId:this.queryInfo.ChapterId}&chapterName=章节名称&grade=${GradeId}`
      const fromData = new FormData()
      fromData.append('file',params.file)
      const { data:res } = await documentExport.post(url,fromData)
      if(res.Success) {
        const queryParams = {
          fileId: res.Data.FileId,
          ...this.$route.query,
          type: 'pdf'
        };
        this.aiDengDai = false
        if (this.retRoutePath) {
          this.$router.push({
            path: '/threeAssistants/BulkImportEditing/index',
            query: queryParams
          })
        }else{
          this.$emit('aiFinish', res.Data.FileId)
        }
      } else {
        // this.$refs.uploadDemo.clearFiles();
        this.aiDengDai = false
      }
      this.$refs.uploadDemo.clearFiles();
    },
    // 向vuex中保存试题属性
    async setSharedSettings() {
      const { Year, Term, GradeId } = this.$store.state.chapterStorage.ChapterObj;

      const fetchAndProcessData = async (url, lab, val, mutation) => {
        const { data } = await this.$uwonhttp.get(url);
        if (data.Success) {
          const processedData = this.CleaningData(data.Data, lab, val);
          this.$store.commit(`AiPdfAttr/${mutation}`, processedData);
        } else {
          this.$message.error(data.Msg);
        }
      };
      const requests = [
        fetchAndProcessData('/Question/Question/GetDifficulty?year=' + Year + '&grade=' + GradeId, 'text', 'value', 'setDifficulty'),
        fetchAndProcessData('/Question/Question/GetLearninglevel?year=' + Year + '&grade=' + GradeId, 'title', 'value', 'setLevelOfStudy'),
        fetchAndProcessData('/Question/Question/GetContentDomain?year=' + Year + '&grade=' + GradeId, 'title', 'value', 'setContentAreas'),
        fetchAndProcessData('/Question/Question/GetCoreLiteracy?year=' + Year + '&grade=' + GradeId, 'title', 'value', 'setCoreLiteracy'),
      ];
      await Promise.all(requests);
    },
    CleaningData(arr, lab, val) {
      return arr.map(res => ({
        label: res[lab],
        value: res[val],
      }));
    }
    // async setSharedSettings() {
    //   const { Year, Term, GradeId } = this.$store.state.chapterStorage.ChapterObj
    //   let difficultyList
    //   const { data: Difficulty } = await this.$uwonhttp.get('/Question/Question/GetDifficulty?year=' + Year + '&grade='+ GradeId)
    //   Difficulty.Success ? difficultyList = this.CleaningData(Difficulty.Data,'text', 'value') : this.$message.error(Difficulty.Msg)
    //   this.$store.commit('AiPdfAttr/setDifficulty', difficultyList)
    //   let learninglevelList
    //   const { data: level } = await this.$uwonhttp.get('/Question/Question/GetLearninglevel?year='+Year + '&grade='+GradeId)
    //   level.Success ? learninglevelList = this.CleaningData(level.Data,'title', 'value') : this.$message.error(level.Msg)
    //   this.$store.commit('AiPdfAttr/setLevelOfStudy', learninglevelList)
    //   let contentList
    //   const { data:Domain } = await this.$uwonhttp.get('/Question/Question/GetContentDomain?year='+Year + '&grade='+GradeId)
    //   Domain.Success ? contentList = this.CleaningData(Domain.Data,'title', 'value') : this.$message.error(Domain.Msg)
    //   this.$store.commit('AiPdfAttr/setContentAreas', contentList)
    //   let literacyList
    //   const { data: Literacy } = await this.$uwonhttp.get('/Question/Question/GetCoreLiteracy?year=' +Year + '&grade=' +GradeId)
    //   Literacy.Success ? literacyList = this.CleaningData(Literacy.Data,'title', 'value') : this.$message.error(Literacy.Msg)
    //   this.$store.commit('AiPdfAttr/setCoreLiteracy', literacyList)
    // },
    // CleaningData(arr,lab,val){
    //   return arr.map(res => {
    //     res.label = res[lab]
    //     res.value = res[val]
    //     return res
    //   })
    // }
  },
  //  PDF识别逻辑暂时注释掉
  // beforeRouteLeave(to, from, next) {
  //   this.$refs.uploadDemo.clearFiles();
  //   this.aiDengDai = false
  //   next()
  // },
}
</script>

<style lang="less" scoped>
.pList{
  h6{
    //font-size: 22px;
    font-size: var(--font16-22);
  }
  h6,p{
    //font-size: 21px;
    font-size: var(--font16-20);
    margin-bottom: 4px;
    color: #FFFFFF;
  }
}
.impro{
  position: relative;
  height: 98%;
  background-color: #FFFFFF;
  border-radius: 6px;
  .title{
    display: flex;
    justify-content: space-between;
    padding: 14px 30px;
    border-bottom: 1px solid #E6E6E6;
    ._left{
      display: flex;
      align-items: center;
      i{
        font-size: 24px;
        margin-right: 10px;
      }
      h6{
        font-weight: normal;
        font-size: 24px;
        color: #333333;
      }
    }
  }
  .cont{
    display: flex;
    padding: 24px;
    height: 93%;
    ._left,._right{
      border: 1px solid #E6E6E6;
      border-radius: 8px;
      height: 100%;
    }
    ._left{
      //width: 100%;
      width: 50%;
      margin-right: 10px;
      ._title{
        display: flex;
        justify-content: space-between;
        padding: 14px 24px;
        background: #F3F4F8;
        .tipsForDownloading{
          display: flex;
          align-items: center;
          .el-link{
            margin-left: 10px;
          }
        }
        p{
          font-weight: normal;
          font-size: 22px;
          color: #333333;
        }
        .elLink{
          font-size: var(--font16-22);
        }
      }
      ._content{
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /deep/.upload-demo{
          width: 90%;
          height: 76%;
          .el-upload{
            width: 100%;
            height: 100%;
            .el-upload-dragger{
              width: 100%;
              height: 100%;
              //width: 800px;
              //height: 600px;
              padding-top: 100px;
              //display: flex;
              //flex-direction: column;
              //justify-content: center;
              border: none;
              .text{
                margin-top: 14px;
                font-weight: normal;
                //font-size: 24px;
                font-size: var(--font18-24);
                color: #333333;
              }
              .xianzhi{
                text-align: center;
                margin-bottom: 24px;
                margin-top: 4px;
                font-weight: normal;
                //font-size: 20px;
                font-size: var(--font16-20);
                color: #999999;
              }
              em{
                padding: 8px 15px;
                background-color: #7FB99A;
                color: #FFFFFF;
                //font-size: 22px;
                font-size: var(--font16-22);
                border-radius: 4px;
              }
              .hearf{
                margin-top: 10px;
              }
              i{
                font-size: 80px;
                color: #E6E6E6;
              }
            }
          }
        }
      }
    }
    ._right{
      width: 50%;
      margin-left: 10px;
      ._title {
        display: flex;
        justify-content: space-between;
        padding: 14px 24px;
        background: linear-gradient(90deg,#EBF9FF,#EBF1FF,#F5EBFF);
        ._Ai {
          font-weight: normal;
          font-size: 22px;
          color: #333333;
        }
      }
      /deep/.upload-demo{
        height: 90%;
        .el-upload{
          width: 100%;
          height: 100%;
          .el-upload-dragger{
            width: 100%;
            height: 100%;
            border: none;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            i{
              font-size: 80px;
              background: linear-gradient(#9BDDFE,#A1B7FE,#CB9CFE);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .el-upload__text{
              p{
                font-size: var(--font18-24);
                color: #333333;
                margin-bottom: 8px;
              }
              .spanTit{
                font-size: var(--font16-20);
                color: #999999;
              }
              .el-button{
                margin-top: 10px;
                background: linear-gradient(90deg,#9BDDFE,#A1B7FE,#CB9CFE);
                color: #FFFFFF;
              }
            }
          }
        }
      }
      //  ._Ai_tit{
      //    // 文本颜色渐变
      //    background: linear-gradient(90deg, #36C0FD- 0%, #3678FD 100%);
      //    -webkit-background-clip: text;
      //    -webkit-text-fill-color: transparent;
      //    font-size: var(--font16-22);
      //  }
      //}
      //._Ai_textarea,._itemType,._itemNum{
      //  h1{
      //    font-size: 16px;
      //    margin-bottom: 10px;
      //  }
      //}
      //._Ai_textarea{
      //  padding: 30px 22px 16px;
      //  .el-textarea{
      //    font-size: 16px;
      //  }
      //}
      //._itemType{
      //  padding: 0 22px;
      //  .el-select{
      //    width: 100%;
      //  }
      //}
      //._itemNum{
      //  margin-top: 16px;
      //  padding: 0 22px;
      //  ._type_num{
      //    display: flex;
      //    flex-wrap: wrap;
      //    .ite{
      //      display: flex;
      //      align-items: center;
      //      p{
      //        font-size: 16px;
      //        margin-right: 4px;
      //      }
      //    }
      //  }
      //}
      //._Ai_Btn{
      //  display: flex;
      //  justify-content: center;
      //  margin-top: 20px;
      //  .el-button{
      //    background: linear-gradient(90deg, #36C0FD7F 0%, #3678FD7F 50%, #9A36FD7F 100%);
      //    color: #FFFFFF;
      //    border-color: #FFFFFF;
      //  }
      //}
    }
  }
  ._waiting{
    width: 100%;
    height: 100%;
    background: #FFFFFF;
    position: fixed;
    left: 0;
    top: 0;
    padding-top: 10%;
    h1{
      font-size: 30px;
      text-align: center;
    }
    .gif{
      display: flex;
      justify-content: center;
      img{
        width: 460px;
      }
    }
    p{
      font-size: 26px;
      text-align: center;
    }
  }
  ._waitingDialog{
    width: 100%;
    height: 100%;
    background: #FFFFFF;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    h1{
      font-size: 30px;
      text-align: center;
    }
    .gif{
      display: flex;
      justify-content: center;
      img{
        width: 100px;
      }
    }
    p{
      font-size: 26px;
      text-align: center;
    }
  }
}
</style>