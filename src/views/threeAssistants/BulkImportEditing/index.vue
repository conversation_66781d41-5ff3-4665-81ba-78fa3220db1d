<template>
  <div class="bulkEditing">
    <div class="bianma">
      <paper-list-com ref="paperList" :paperList="paperList" @anchorPoint="anchorPoint"></paper-list-com>
      <edit-coding
        ref="editCoding"
        :editPaperList="editPaperList"
        :rawData="paperList"
        @clickEl="$refs.itemEditDrawer.init()"
        @anchorPoint="anchorPoint"
        @resetLeft="resetLeft"
        @editItem="editItem"></edit-coding>
    </div>
    <div class="btnBotm">
      <el-button size="medium" @click="goBack">取 消</el-button>
      <el-button size="medium" type="primary" @click="saveQuestions">确 定</el-button>
    </div>
    <shared-settings ref="sharedSettings" :queryInfo="queryInfo" @requestInterface="setSharedSettings"></shared-settings>
    <!--  市级管理操作的授权弹窗  -->
    <dialog-com ref="dialogCom" title="授权范围" :timeShow="false" @authorization="goNewJobResources"></dialog-com>
    <!--  试题编辑的抽屉  -->
    <item-edit-drawer ref="itemEditDrawer" :rawData="paperList" @updateList="getPaperInfo"></item-edit-drawer>
  </div>
</template>

<script>
import paperListCom from '@/views/threeAssistants/BulkImportEditing/components/paperList'
import editCoding from '@/views/threeAssistants/BulkImportEditing/components/editCoding'
import sharedSettings from '@/views/threeAssistants/AddANewTopic/components/module/sharedSettings'
import { initWPS } from '@/utils/init_WPS'
import {getItemOptions} from './getItemOptions'
import dialogCom from '@/views/threeAssistants/components/authorization/dialogCom'
import itemEditDrawer from '@/views/threeAssistants/BulkImportEditing/components/itemEditDrawer'
import { Session } from '@/utils/storage.js';
export default {
  name: 'index',
  components:{
    paperListCom,
    editCoding,
    sharedSettings,
    dialogCom,
    itemEditDrawer
  },
  props: {

  },
  data() {
    return {
      // modeState:['题目编辑', '题目设置'],
      // activeModeState: '题目设置',
      // wpsLoading: true,
      // wpsObj: null,
      paperLength:0,// 试卷列表长度
      paperList: [], // 原试卷信息
      editPaperList: [], // 操作区试卷信息
      queryInfo: null,
      fileId: ''
    }
  },
  activated() {
    const { type, fileId } = this.$route.query
    if(!fileId) {
      return
    }
    if(type === 'word'){
      this.getPaperInfo()
    } else {
      this.getPaperPdfInfo()
    }
  },
  watch: {
    $route(to, from) {
      // console.log(to, from)
      if (to.path !== '/threeAssistants/BulkImportEditing/index') {
        this.paperList = []
        Session.remove('importItemList')
        this.$store.commit('AiPdfAttr/resetAll')
      }
    }
  },
  methods:{
    dialogInit(queryInfo, fileId) {
      this.queryInfo = queryInfo
      this.fileId = fileId
      this.getPaperPdfInfo()
    },
    goBack() {
      // sessionStorage.setItem('activeModeState','题目编辑')
      // const { paperId, id, ChapterId, GradeId, Semester, TextbookId, Year } = this.$route.query
      // this.$router.push({
      //   path: '/threeAssistants/JobDesign/NewJob',
      //     query: {
      //       paperId,
      //       id,
      //       ChapterId,
      //       GradeId,
      //       Semester,
      //       TextbookId,
      //       Year,
      //     },
      //   })
      if (this.fileId) {
        this.$emit('cancel')
      } else {
        this.$router.go(-1)
      }
    },
    // init(){
    //   this.wpsObj?.destroy()
    //   const activeModeState = sessionStorage.getItem('activeModeState');
    //   // this.activeModeState = activeModeState || '题目编辑'; // Set a default mode if not found
    //   this.activeModeState = activeModeState || '题目设置'; // Set a default mode if not found
    //   if(this.activeModeState === '题目编辑'){
    //     const timer = setTimeout(() => {
    //       this.openWps()
    //       clearTimeout(timer)
    //     },500)
    //   }
    // },
    // 右侧修正题干信息
    // updateTitle(val){
    //   this.$refs.paperList.editTitle(val)
    // },
    // 左侧试卷试题点击媒介
    VerifyTheRightSide(){
      // if(this.$refs.editCoding.getList()){
      // if(this.$refs.drawerCom.getList()){
      //   this.$refs.paperList.assignment()
      // }
    },
    // 左侧试题锚点
    anchorPoint({ type, id }){
      if(type == 'rig'){
        this.$refs.editCoding.executeAnchorPoint(type+id)
      } else {
        this.$refs.paperList.executeAnchorPoint(type+id)
      }
    },
    // 刷新左侧组件
    resetLeft(){
      this.$refs.paperList.resetShuaxin()
    },
    // 编辑题目抽屉开启
    editItem({ item, ind }){
      // item中QuestionTypeId === 10的多项选择题Answer数组转换为字符串
      item = JSON.parse(JSON.stringify(item))
      item.Answer = item.QuestionTypeId === 10 ? item.Answer.join('|') : item.Answer
      // console.log(item)
      this.$refs.itemEditDrawer.init(item,ind)
    },
    // 根据试题下标替换掉Session.get('importItemList')数组中对应的对象
    // replaceItem(ind, item){
    //   const importItemList = Session.get('importItemList')
    //   importItemList.splice(ind, 1, item)
    //   Session.set('importItemList', importItemList)
    // },
    // paperItemClick(obj){
    //   this.paperLength = obj.perperLen
    //   // this.$refs.editCoding.init(obj)
    //   return false
    //   this.$refs.drawerCom.init(obj)
    // },
    // stateBtn(item){
    //   this.activeModeState = item
    //   sessionStorage.setItem('activeModeState',item)
    //   if(item === '题目编辑') {
    //     this.wpsLoading = true
    //     const timer = setTimeout(() => {
    //       this.openWps()
    //       clearTimeout(timer)
    //     }, 500)
    //   } else {
    //     this.wpsObj.save()
    //   }
    // },
    // 打开wps
    // async openWps() {
    //   const FileId = this.$route.query.fileId
    //   document.getElementById('onlineWord')
    //   const wps = initWPS('w',FileId,{
    //     mount: "#onlineWord",
    //     commandBars: [
    //       {
    //         cmbId: 'HeaderRight', // 组件 ID
    //         attributes: {
    //           visible: false // 隐藏组件
    //         },
    //       },
    //     ],
    //   })
    //   wps.ApiEvent.AddApiEventListener('fileOpen', data => {
    //     if (data.success) {
    //       this.$nextTick(()=>{
    //         this.wpsLoading = false
    //       })
    //     }
    //   });
    //   await wps.ready();
    //   this.wpsObj = wps
    // },
    // 确定保存
    /**
     * @description:确定批量保存导入题目的数据
     * 点击确定的时候需要把教研当前选中的数据存储，校验成功存sessionStorage中，
     * 然后在获取sessionStorage中数据的长度对比试卷的长度是否相等，如果不相等禁止提交数据
     * @return {*}
     */
    async saveQuestions() {
      // const falg = await this.$refs.editCoding.getList()
      // const falg = await this.$refs.drawerCom.getList()
      // if(!falg) return false;
      // const sessionInfo = getItemOptions.getAllItem('importOptionsList')
      // const sessionCount = Object.entries(sessionInfo).length;
      // // console.log(this.paperLength)
      // // console.log(sessionCount)
      // if(sessionCount!=this.paperLength) return this.$message.error('请填写所有题目信息')
      // console.log(this.checkQuestionCompleteness())
      if (this.checkQuestionCompleteness()) return this.$message.error('请完善必填信息')
      // 市级权限授权逻辑
      if(this.$store.state.Level == '5'){
        const data = {
          ShareSet: 1,
          ShareAreaCity: 1,
          PaperName: ''
        }
        this.setSharedSettings(data)
      } else {
        // 出现共享弹窗
        this.$refs.sharedSettings.init()
      }
    },
    // 弹窗确认事件
    async setSharedSettings(obj) {
      // const sessionInfo = getItemOptions.getAllItem('importOptionsList')
      // const Questions = Object.values(sessionInfo);
      let sessionInfo = Session.get('importItemList')
      sessionInfo.forEach(ist =>{
        ist.Answer = ist.QuestionTypeId === 10 ? ist.Answer.join('|') : ist.Answer
        delete ist.ScoolId
        delete ist.itemNewAnswer
      })
      const { ShareSet, ShareAreaCity } = obj
      const data = {
        ShareSet,
        ShareAreaCity,
        // QuestionInfos: Questions
        QuestionInfos: sessionInfo
      }
      const { paperId } = this.$route.query
      // 根据Redirection判断调用接口
      let response
      if(this.$route.query.Redirection || this.queryInfo) {
        const inputParameters = {
          PaperId: paperId || this.queryInfo.paperId,
          // ItemInfos: Questions,
          ItemInfos: sessionInfo,
          UserId: localStorage.getItem('UserId'),
          UserRoleId: localStorage.getItem('role'),
        }
        response = await this.$uwonhttp.post('/Question/Question/HomeworkAddItemInfos', inputParameters)
      } else {
        response = await this.$uwonhttp.post('/Question/Question/SaveQuestions',data)
      }
      if (response.data.Success) {
        // this.$message.success('保存成功')
        /**
         * 场景1:如果是在作业设计中跳过来的，需要原路返回
         * 场景2:如果是在批量导入中跳过来的，需要跳回首页
         */
        this.$refs.sharedSettings.handleClose()
        const { paperId, id, ChapterId, GradeId, Semester, TextbookId, Year } = this.$route.query
        // 生成作业流程
        if(obj.PaperName !== '') {
          const Parameters = {
            ChapterId,
            GradeId,
            Semester,
            TextbookId,
            Year,
            UserId: localStorage.getItem('UserId'),
            UserRoleId: localStorage.getItem('role'),
            SubjectId: localStorage.getItem('UW_SUBJECT'),
            PaperName: obj.PaperName,
            PaperType: obj.PaperType,
          }
          // 生成试卷
          const { data: resPonse } = await this.$uwonhttp.post('/Question/Question/AssignHomework', Parameters);
          if(resPonse.Success){
            this.$router.push({
              path: '/threeAssistants/JobDesign/NewJob',
              query: {
                paperId: resPonse.Data,
                id: 100,
                ChapterId,
                GradeId,
                Semester,
                TextbookId,
                Year,
                testQuestionBankIds: response.data.Data.join(',')
              },
            });
          }else {
            this.$message.error(resPonse.Msg)
          }
        } else {
          if(sessionStorage.getItem('Level') === '5'){
            this.$refs.dialogCom.init(response.data.Data)
          } else {
            if (this.queryInfo) {
              this.$emit('aiAddItemFinish')
            } else {
              // 新增题库及作业流程
              const rouPath = localStorage.getItem('LoginSource') == 2 ? '/threeAssistants/newJobResources/index' : '/Paper/Exam_MicroLesson/QuestionBankResourceCenter'
              const redirectionPath = this.$route.query.Redirection || rouPath
              const hasRedirection =  Boolean(this.$route.query.Redirection)
              const query = this.$route.query.Redirection
                ? { paperId, id, ChapterId, GradeId, Semester, TextbookId, Year, trigger: true , itemList: JSON.stringify(response.data.Data)}
                : {};
              this.$router[hasRedirection? 'replace' : 'push']({path: redirectionPath, query})
            }
          }
        }
      } else {
        this.$message.error(response.data.Msg)
      }
    },
    // 跳转作业资源页面
    goNewJobResources(){
      this.$router.push({
        path: '/threeAssistants/newJobResources/index',
      })
      // sessionStorage.removeItem('activeModeState')
    },
    // 自定义上传文档事件
    // async httpRequest (data) {
    //   const paramsData = new FormData()
    //   paramsData.append(`File`, data.file)
    //   const { data:res } = await this.$uwonhttp.post('/Question/Question/QuestionImportByWord',paramsData)
    //   if (res.Success){
    //     const { ChapterId,GradeId,Semester,TextbookId,Year,disabled } = this.$route.query
    //     const href = `/threeAssistants/BulkImportEditing/index?fileId=${res.Data}&ChapterId=${ChapterId}&GradeId=${GradeId}&Semester=${Semester}&TextbookId=${TextbookId}&Year=${Year}&disabled=${disabled}`
    //     window.location.replace(href)
    //   } else {
    //     this.$message.error(res.Msg)
    //   }
    // },
    // 文档上传超出限制触发
    // handleExceed () {
    //   this.$message.warning(`当前限制导入一个文件，请删除文件后在重新导入!`)
    // },
    // 检查题目必填信息是否完整
    checkQuestionCompleteness() {
      const importObj = Session.get('importItemList')
      if(!Array.isArray(importObj)){
        return false
      }
      const isQuestionInComplete = (item) => {
        if([5, 2, 11].includes(item.QuestionTypeId)){
          return item.Answer === '' || item.Title === ''
        }
        if(item.QuestionTypeId === 10) {
          return item.Answer.length === 0 || item.Title === ''
        }
        if(item.QuestionTypeId === 36){
          return item.Title === ''
        }
        return importObj.some(item => {
          return item && item.QuestionTypeId !== undefined && isQuestionInComplete(item)
        })
      }
    },
    // 获取试卷信息
    async getPaperInfo(){
      // const { ChapterId,GradeId,Semester,TextbookId,Year } = this.$route.query
      const { data:res } = await this.$uwonhttp.get('/Question/Question/WordImportQuestion?fileId='+this.$route.query.fileId)
      if (res.Success){
        // 初始化 editPaperList
        this.editPaperList = []
        if(res.Data && Array.isArray(res.Data)){
          res.Data.forEach((item,ind) => {
            item.ScoolId = 'scool'+ind
            item.QuestionTypeId = item.QuestionTypeId === 0 ? null : item.QuestionTypeId
          })
          this.paperList = res.Data
          this.paperLength = res.Data.length
          if(Session.get('importItemList') && Session.get('importItemList').length > 0){
            // console.log('这里把存储的值给到 editPaperList')
            this.editPaperList = Session.get('importItemList')
          } else {
            const regx = /（.*?）/g
            const newEditPaperList = res.Data.map((item) => {
              // 判断填空题，处理括号空数量在右侧操作区展列填答案使用
              // if(item.QuestionTypeId === 5){
                let regxList = item.Title.match(regx)
                if(regxList){
                  // item.itemNewAnswer = regxList.map(ite => ite.replace(/\（|\）/g, '').trim())
                  item.itemNewAnswer = regxList.map(ite => {return ''})
                }
              // }
              if(item.QuestionTypeId === 10){
                item.Answer = []
              }
              return this.returnItem(item, 'word')
            })
            this.editPaperList = newEditPaperList
            Session.set('importItemList', this.editPaperList)
          }
        } else {
          this.$message.error('接口返回数据类型有误');
        }
      } else {
        this.$message.error(res.Msg)
      }
    },

    // 获取Pdf解析数据
    async getPaperPdfInfo() {
      const { data:res } = await this.$uwonhttp.get('/Question/Question/WordImportQuestionByPdf?fileId=' + (this.fileId ? this.fileId : this.$route.query.fileId))
      // console.log(res)
      if(res.Success) {
        this.editPaperList = []
        if(res.Data && Array.isArray(res.Data)) {
          res.Data.forEach((item,ind) => {
            item.ScoolId = 'scool'+ind
            item.QuestionTypeId = item.QuestionTypeId === 0 ? null : item.QuestionTypeId
          })
          this.paperList = res.Data
          this.paperLength = res.Data.length
          if(Session.get('importItemList') && Session.get('importItemList').length > 0){
            // console.log('这里把存储的值给到 editPaperList')
            this.editPaperList = Session.get('importItemList')
          } else {
            // const regx = /（.*?）/g
            const newEditPaperList = res.Data.map((item) => {
              // 判断填空题，处理括号空数量在右侧操作区展列填答案使用
              // let regxList = item.Title.match(regx)
              // if(regxList){
              //   // item.itemNewAnswer = regxList.map(ite => ite.replace(/\（|\）/g, '').trim())
              //   item.itemNewAnswer = regxList.map(ite => {return ''})
              // } else {
                // if(item.QuestionTypeId === 5){
                  item.itemNewAnswer = item.Answer.split('|')
                // } else {
                //   item.itemNewAnswer = []
                // }
              // }
              if(item.QuestionTypeId === 10){
                item.Answer = []
              }
              return this.returnItem(item, 'pdf')
            })
            this.editPaperList = newEditPaperList
            Session.set('importItemList', this.editPaperList)
          }
        } else {
          this.$message.error('Data is not an array or is null');
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
    // word试题数据
    returnItem(item, type) {
      const getValueById = (list, label) => list.find(res => res.label === label)?.value || '';
      const { difficulty, levelOfStudy, contentAreas, coreLiteracy } = this.$store.state.AiPdfAttr
      console.log(this.$store.state.AiPdfAttr)
      const attr = type === 'pdf' ? {
        DifficultyId: getValueById(difficulty, item.Difficulty),
        LearningLevelId: getValueById(levelOfStudy, item.LearningLevel),
        ContentDomainId: getValueById(contentAreas, item.ContentDomain),
        CoreLiteracyId: getValueById(coreLiteracy, item.CoreLiteracy),
      } : {
        DifficultyId:'', // 题目难度Id
        LearningLevelId:'', // 学习水平Id
        ContentDomainId:'', // 内容领域Id
        CoreLiteracyId:'', // 核心素养Id
      }
      return {
        QuestionTypeId: item.QuestionTypeId,
        Year: this.$route.query.Year || this.queryInfo.Year, // 学年
        Semester: this.$route.query.Semester || this.queryInfo.Semester, // 学期
        GradeId: this.$route.query.GradeId || this.queryInfo.GradeId, // 年级Id
        SubjectId: localStorage.getItem('UW_SUBJECT'), // 学科Id
        TextbookId: this.$route.query.TextbookId || this.queryInfo.TextbookId, // 教材Id
        ChapterId: this.$route.query.ChapterId || this.queryInfo.ChapterId, // 章节Id
        UserId: localStorage.getItem('UserId'), // 创建人Id
        UserRoleId: localStorage.getItem('role'),
        ResponseFormat: 1, // 答题形式
        ShareSet: 1, // 共享设置
        ShareAreaCity: 1, // 共享范围
        Hint: '', // 作答提示
        SpecialItemConfig: '', // 特殊提醒
        ReferenceAnswer: '', // 参考答案
        Answer: item.Answer, // 答案
        OtherAnswer:[], // 多答案
        Options: item.Options, // 选项
        Errors:[],
        Id:'', // 编辑时传值，新增传空
        Title: item.Title, // 题干
        Analysis: item.Analysis === null ? '略' : item.Analysis, // 题目解析
        CourseVersionsId:'1811293692187680768', // 课程版本Id
        QuestionCode:'', // 题目编号

        LearningGuidance: item.LearningGuidance, // 学习指导
        Misattribution: item.Misattribution, // 错误归因
        GuidanceFile:null, // 学习指导
        AnalysisFile:null, // 解析文件
        UnitTargetId:'', // 单元目标Id
        ChapterTargetId:'', // 章节目标Id
        QuestionTargetId:'', // 题目目标Id
        ThemeId: '', // 主题Id

        SceneAttributeId:null, // 情景属性Id
        QuestionSource:'', // 题目来源
        PhotographSet:'', // 拍照设置
        FinishTime:1, // 预计完成时间分钟
        ErrorCollect:[], // 错因汇总
        RelevanceErrors:[], // 填空题错因与答案的绑定
        itemNewAnswer: item.itemNewAnswer, // 前端自定义值向接口传递时去除该值
        ScoolId: item.ScoolId,
        ...attr,
      }
    }
  }
}
</script>

<style lang="less" scoped>
.bulkEditing{
  //.statusBtn{
  //  display: flex;
  //  justify-content: space-between;
  //  background-color: #FFFFFF;
  //  padding: 10px 26px;
  //  border-bottom: 1px solid #E6E6E6;
  //  ._left{
  //    display: flex;
  //    align-items: center;
  //    i{
  //      font-size: 22px;
  //      margin-right: 10px;
  //    }
  //  }
  //  ._right{
  //    display: flex;
  //    align-items: center;
  //    .el-button{
  //      margin-left: 10px;
  //    }
  //    /deep/.upload-demo{
  //      display: flex;
  //      align-items: center;
  //    }
  //  }
  //}
  //.onlineWord{
  //  width: 100%;
  //  height: 86vh;
  //}
  .bianma{
    display: flex;
  }
  .btnBotm{
    display: flex;
    justify-content: center;
    background-color: #FFFFFF;
    padding: 14px 0;
    border-top: 1px solid #E6E6E6;
  }
}
</style>