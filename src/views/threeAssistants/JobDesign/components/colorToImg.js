/**
 * 将颜色值转为 base64 图片
 * @param {string} color - 颜色值，如 #f56c6c
 * @param {number} size - 图片宽高，默认32
 * @returns {string} base64图片
 */
export function colorToImg(color, size = 32) {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d');
  ctx.fillStyle = color;
  ctx.fillRect(0, 0, size, size);
  return canvas.toDataURL('image/png');
}