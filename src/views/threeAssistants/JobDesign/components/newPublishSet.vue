<template>
  <el-dialog
    title="作业布置"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose">
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm">
      <el-form-item label="班级" prop="ClassId">
        <el-checkbox-group v-model="ruleForm.ClassId">
          <el-checkbox v-for="item in classList" :key="item.ClassId" :label="item.ClassId" :disabled="item.IsPublish">{{item.ClassName}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="布置时间">
        <el-radio-group v-model="radio">
          <el-radio :label="1">立即布置</el-radio>
          <el-radio :label="2">选择布置时间</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="radio == 2" label="时间" prop="PublishTime">
        <el-date-picker
          v-model="ruleForm.PublishTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择发布日期时间">
        </el-date-picker>
      </el-form-item>
<!--      <el-form-item label="共享范围" v-show="paperInfo.State == 2 && ruleForm.UseScene == 2">-->
      <el-form-item label="共享范围" v-show="UseSceneShow">
        <el-select v-model="ruleForm.ShareSet" placeholder="请选择" @change="selectChange">
          <el-option label="不共享" :value="1"></el-option>
          <el-option label="同校共享" :value="2"></el-option>
          <el-option label="愿意全区共享" :value="3"></el-option>
          <el-option label="愿意全市共享" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="UseSceneShow&&$store.state.LoginSource == 1" label="作业使用场景">
        <el-radio-group v-model="ruleForm.UseScene" @input="useSceneIn" :disabled="PaperTag === 26">
          <el-radio :label="1">电子作业</el-radio>
          <el-radio :label="2">纸笔作业</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="ruleForm.UseScene == 2" label="纸张" prop="HomeworkType">
        <el-select v-model="ruleForm.HomeworkType" placeholder="请选择" :disabled="paperInfo.IsExistDzBFrame == 1 || PaperTag === 26">
          <el-option
            v-for="item in typeOfPaper"
            :key="item.Type"
            :label="item.Name"
            :value="item.Type">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="!ASelfAnswerSheet && ruleForm.UseScene == 2">
        <div>申请自主答题卡
          <el-tooltip style="margin-left: 6px;" placement="right-start">
            <div slot="content">
              <p style="text-align: center;font-size: 14px;margin-bottom: 6px;">请联系周先生：</p>
              <p style="text-align: center;font-size: 16px;">021-54486816</p>
            </div>
            <i style="color: #E6A23C; font-size: 22px; cursor: pointer;" class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </el-form-item>
      <ul class="subQuestions" v-show="ruleForm.DoItemWays.length > 0 && ruleForm.UseScene == 1">
        <li v-for="(item,ind) in ruleForm.DoItemWays" :key="item.ItemId">
          <p style="width: 130px;text-align: right;padding-right: 10px;">主观题{{ind+1}}作答形式</p>
          <el-select v-model="item.ResponseFormat">
            <el-option label="拍照" :value="1"></el-option>
            <el-option label="音频" :value="2"></el-option>
            <el-option label="视频" :value="3"></el-option>
          </el-select>
        </li>
      </ul>
<!--      {{'作业类型' + ruleForm.UseScene}}-->
<!--      {{'是否铺码' + paperInfo.IsExistDZBFrame}}-->
<!--      {{'纸张类型' + ruleForm.HomeworkType}}-->
<!--   电子作业或已经框选铺码   -->
<!--   确定取消：电子作业 || 试卷已经铺码 || 纸笔作业&&未铺码&&纸张为客观答题卡，互动作业薄，自主答题卡  -->
<!--   求助平台，自行打印：纸笔作业&&未铺码&&纸张为A4，B3   -->
      <el-form-item v-if="ruleForm.UseScene == 1 || paperInfo.IsExistDZBFrame == 1 || ruleForm.UseScene == 2 && (paperInfo.IsExistDZBFrame == 0 || paperInfo.IsExistDZBFrame == undefined) && [3,4,5,9].includes(ruleForm.HomeworkType)">
        <el-button style="margin-top: 20px;" @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
      </el-form-item>
      <el-form-item v-if="ruleForm.UseScene == 2 && (paperInfo.IsExistDZBFrame == 0 || paperInfo.IsExistDZBFrame == undefined) && [1,2].includes(ruleForm.HomeworkType)">
<!--      <el-form-item>-->
<!--        <el-button style="margin-top: 20px;" @click="HelpPlatform">求助平台</el-button>-->
        <el-button style="margin-top: 20px;" @click="submitForm('ruleForm', '求助')">求助平台</el-button>
        <el-button type="primary" @click="submitForm('ruleForm', '纸笔')">自行打印</el-button>
      </el-form-item>
    </el-form>
<!--    <el-dialog title="铺码设置" width="24%" :visible.sync="innerVisible" append-to-body>-->
<!--      <div>-->
<!--        <el-radio-group v-model="paperWordType">-->
<!--          <el-radio :label="0">自行铺码打印</el-radio>-->
<!--          <el-radio :label="1">铺码纸打印</el-radio>-->
<!--        </el-radio-group>-->
<!--      </div>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="innerVisible = false">取 消</el-button>-->
<!--        <el-button type="primary" @click="goLayCodePrint">确 定</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
    <!--  求助平台  -->
    <help-platform ref="helpPlatform" @openParentSuccess="openParentSuccess"></help-platform>
  </el-dialog>
</template>

<script>
import { Session } from '@/utils/storage.js';
import dayjs from 'dayjs'
import helpPlatform from '@/views/threeAssistants/JobDesign/components/helpPlatform'
import { colorToImg } from './colorToImg';
import obsClient from '@/utils/obsClient'
export default {
  name: 'newPublishSet',
  props:{
    QuestionList:{
      type: Array,
      default: () => []
    },
    UseSceneShow: {
      type: Boolean,
      default: true
    },
    PaperTag:{
      type: Number,
      default: 0
    },
    routerText:{
      type: String,
      default: ''
    }
  },
  components:{
    helpPlatform,
  },
  data() {
    return {
      paperId: '',
      classList: [],
      trueClass: [],
      radio: 1,
      dialogVisible: false,
      // 铺码弹窗参
      // innerVisible: false,
      // paperWordType: 1,
      // reviseAreaLevel: 0,
      // 铺码弹窗参
      typeOfPaper: [],
      ASelfAnswerSheet: false,
      ruleForm: {
        PaperId: '',
        ClassId: [],
        PublishTime: '',
        ShareSet: 1, // 1：不共享 2：同校共享
        ShareAreaCity: 1, // 1：不共享，2：愿意全区共享，3：愿意全市共享
        UseScene: 1, // 1:电子， 2：纸笔
        DoItemWays: [],
        HomeworkType: 0
      },
      rules: {
        ClassId: [
          { required: true, message: '请选择布置班级', trigger: 'blur' },
        ],
        PublishTime: [
          { required: true, message: '请选择布置时间', trigger: 'blur' },
        ],
        HomeworkType: [
          { required: true, message: '请选择纸张类型', trigger: 'blur' }
        ]
      },
      // 纸笔练习题型验证提示
      validationTips:{
        1:{ itemType: ['51','52','70'], aler: '存在不支持适应纸笔作业A4类型的题型：表格题、选词填空题、多项单选题' },
        2:{ itemType: ['51','52','70'], aler: '存在不支持适应纸笔作业B4类型的题型：表格题、选词填空题、多项单选题' },
        3:{ itemType: ['2','10','11'], aler: '客观答题卡允许的题型：单项选择题，多项选择题，判断题' },
        4:{ itemType: ['5','36'], aler: '作业薄允许的题型：填空题，主观题' },
        5:{ itemType: ['51','52','70'], aler: '存在不支持适应纸笔作业自主答题卡类型的题型：表格题、选词填空题、多项单选题' },
      },
      // 求助平台弹窗
      helpPlatformShow: false,
      // 试卷详情
      paperInfo: {},
      formName: '',
      formType: ''
    }
  },
  methods:{
    // 获取当前时间
    init(pape, newRuleForm, itemList) {
      // console.log(pape)
      this.ruleForm.ClassId = []
      this.paperInfo = pape // 试卷详情
      this.ruleForm.HomeworkType = pape.HomeworkType // 纸张
      const ItemList = itemList || this.QuestionList
        // console.log(ItemList)
      const list = ItemList
        .filter(item => item && item.ItemTypeId == 36)
        .map(item => ({ ItemId: item.ItemId, ResponseFormat: 1 }));
      // console.log(list)
      this.ruleForm.DoItemWays = list // 收集试卷中主观题修改主观题答题形式
      if(newRuleForm){ // 布置为电子作业/纸笔作业
        const { UseScene } = newRuleForm
        this.ruleForm.UseScene = UseScene
        this.useSceneIn(UseScene)
      }
      /* 二次发布回填共享字段 */
      const { paperInfo } = this;
      if (paperInfo && paperInfo.ShareAreaCity && paperInfo.ShareSet) {
        this.ruleForm.ShareSet = this.paperInfo.ShareSet
        this.ruleForm.ShareAreaCity = this.paperInfo.ShareAreaCity
      }
      this.GetTeachClass()
      this.dialogVisible = true
    },
    handleClose() {
      this.ruleForm.UseScene = 1
      this.dialogVisible = false
    },
    submitForm(formName, type) {
      this.formName = formName
      this.formType = type
      if (this.routerText == 'NewJob') {
        this.$emit('PaperPublishBefore')
        return
      }
      this.submitFormAfter()
    },
    submitFormAfter() {
      const { formName } = this
      const type = this.formType
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // if(type === '电子'){
          if (this.ruleForm.UseScene === 1) {
            const isExist = this.QuestionList.some(item => item.ItemTypeId == 71 || item.ItemTypeId == 72)
            if (isExist) {
              this.$message.error('存在不支持适应电子作业的题型：特殊选择题，填空判断题！')
              return
            }
            // this.PaperPublishToClass()
            this.GetPaperPublishInfo()
          } else {
            if (localStorage.getItem('IsVip') == 0) {
              this.$alert('布置纸笔作业需要点阵笔设备，详情请咨询客服人员:021-54486816', '提示', {
                confirmButtonText: '确定',
                callback: action => {
                  return false
                }
              });
              return
            }
            // 即兴题单走
            if (this.ruleForm.HomeworkType === 9) {
              this.PaperPublishToClass()
              return
            } else {
              const obj = this.validationTips[this.ruleForm.HomeworkType]
              const itemTypeSet = new Set(obj.itemType);
              if([1,2,5].includes(this.ruleForm.HomeworkType)) {
                if (this.QuestionList.some(item => {
                  const questionTypeId = item.QuestionTypeId || item.ItemTypeId;
                  return questionTypeId !== undefined && itemTypeSet.has(questionTypeId);
                })) {
                  this.$message.error(obj.aler)
                  return
                }
              }
              if([3,4].includes(this.ruleForm.HomeworkType)) {
                // if(this.QuestionList.some(item => !obj.itemType.includes(item.QuestionTypeId || item.ItemTypeId))){
                //   this.$message.error(obj.aler)
                //   return
                // }
                if (this.QuestionList.some(item => {
                  const questionTypeId = item.QuestionTypeId || item.ItemTypeId;
                  return questionTypeId !== undefined && !itemTypeSet.has(questionTypeId);
                })) {
                  this.$message.error(obj.aler)
                  return
                }
              }
              // 判断试卷是否已铺码：0：未铺码，1：已铺码
              if (!this.paperInfo.IsExistDZBFrame && new Set([1,2]).has(this.ruleForm.HomeworkType)) {
                this.goLayCodePrint(type)
              } else {
                this.PaperPublishToClass()
              }
              // this.innerVisible = true
            }
          }
        } else {
          return false;
        }
      });
    },
    // resetForm(formName) {
    //   // this.$refs[formName].resetFields();
    //   // this.dialogVisible = false
    //   this.handleClose()
    // },
    useSceneIn(val){
      if(val == 2){
        this.ruleForm.HomeworkType = 1
        this.GetHomeworkTypes(1)
      } else {
        this.ruleForm.HomeworkType = 0
      }
    },
    // 共享范围
    selectChange(val){
      switch (val)
      {
        case 1:
          this.ruleForm.ShareSet= val
          this.ruleForm.ShareAreaCity = 1
          break;
        case 2:
          this.ruleForm.ShareSet= val
          this.ruleForm.ShareAreaCity = 1
          break;
        case 3:
          this.ruleForm.ShareSet= 2
          this.ruleForm.ShareAreaCity = 2
          break;
        case 4:
          this.ruleForm.ShareSet= 2
          this.ruleForm.ShareAreaCity = 3
          break;
      }
    },
    // 求助平台
    // HelpPlatform(){
      // this.$alert('<strong style="font-size: 16px;">平台已收到您的求助，之后会有工作人员与您联系~</strong>', '提示', {
      //   type: 'success',
      //   dangerouslyUseHTMLString: true,
      //   confirmButtonText: '我知道了',
      //   callback: action => {
      //     console.log('后续流程带需求制定。')
      //   }
      // });
      // this.$refs.helpPlatform.init()
    // },
    // 求助成功后
    openParentSuccess(){
      this.$emit('openParentSuccess')
      this.dialogVisible = false
    },
    // 纸笔铺码跳转至铺码及框选
    async goLayCodePrint(type) {
      let paperId
      this.ruleForm.PublishTime = this.radio == 1 ? dayjs().format('YYYY-MM-DD HH:mm:ss') : this.ruleForm.PublishTime
      if(this.paperInfo.CreatorId === localStorage.getItem('UserId') || this.paperInfo.CreatorId == undefined) {
        paperId = this.$route.query.paperId || this.paperInfo.Id
      } else {
        paperId = await this.CopyPaperId()
      }
      if(type === '求助') {
        const ClassIds = this.ruleForm.ClassId.filter(item => !this.trueClass.includes(item)).map(item => item)
        this.ruleForm.ClassId = ClassIds
        this.ruleForm.PaperId = paperId
        const deObj = {
          IsImmediately: this.radio,
          ...this.ruleForm
        }
        this.$refs.helpPlatform.init(deObj)
        // 处理事件显示回归初始状态
        // this.resetForm('ruleForm')
        this.handleClose()
      } else {
        // 走铺码打印逻辑
        const ClassIds = this.ruleForm.ClassId.filter(item => !this.trueClass.includes(item)).map(item => item).join(',')
        this.ruleForm.ClassId = ClassIds
        /*使用sessionStorage跨页面传递参数*/
        this.ruleForm.PaperId = paperId
        Session.set('customEditForm', this.ruleForm)

        console.log('11111');
        const isSuccess = await this.uploadPenColorImgs()
        console.log('4上传笔迹颜色图片完成');
        // 上传笔迹颜色图片完成后，跳转到铺码打印页面
        if (isSuccess) {
          this.$router.push({
            path: '/Paper/Exam_Paper/LayCode/customEdit',
            query: {
              paperId,
              paperWordType: 1,
              reviseAreaLevel: 0,
            }
          })
        }
        // 处理事件显示回归初始状态
        // this.resetForm('ruleForm')
        this.handleClose()
        // this.dialogVisible = false
      }
    },
    // 复制纸卷逻辑
    async CopyPaperId(){
      const { Id, Title, ChapterId, AuthorName, GradeId, IsSetScore, TotalScore } = this.paperInfo
      const response = await this.$uwonhttp.post('/Question/Question/PaperCite', {
        PaperId: Id,
        UserId: localStorage.getItem('UserId'),
        Year: Session.get('yearDefault'),
        Title,
        GradeId,
        ChapterId,
        AuthorName,
        IsSetScore,
        TotalScore,
        UserRoleId: localStorage.getItem('role')
      });
      if (response.data.Success) {
        return response.data.Data
      } else {
        this.$message.error(response.data.Msg);
      }
    },
    // 获取班级
    async GetTeachClass(){
      const userId = localStorage.getItem('UserId')
      // 获取已发布及未发布的班级
      const { data: respons } = await this.$uwonhttp.get(`/MDPen/MDPen/GetPaperPublishClass?userId=${userId}&paperId=${this.paperInfo.Id}`)
      if(respons.Success){
        const list = respons.Data.PaperPublishClass
        const ClassIds = list.filter(item => item.IsPublish).map(item => item.ClassId)
        // console.log(ClassIds)
        this.trueClass = ClassIds
        this.classList = list
      } else {
        this.$message.error(respons.Msg)
      }
    },
    // 获取纸张类型
    async GetHomeworkTypes(bType){
      const { data:res } = await this.$uwonhttp.post('/MDPen/MDPen/GetHomeworkTypes',{ BaseType: bType , SubjectId: localStorage.getItem('UW_SUBJECT') })
      if(res.Success){
        this.typeOfPaper = res.Data
        this.ASelfAnswerSheet = res.Data.some(item => item.Type == 5)
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 布置作业前需要调用的接口
    async GetPaperPublishInfo(){
      if(this.paperInfo.FileId){
       const res = await this.$uwonhttp.post('/MDPen/MDPen/PrePublishPaper', { fileId: this.paperInfo.FileId })
        if(res.data.Success){
          this.PaperPublishToClass()
        }
      } else {
        this.PaperPublishToClass()
      }
    },
    // 作业布置接口
    async PaperPublishToClass() {
      const ClassIds = this.ruleForm.ClassId.filter(item => !this.trueClass.includes(item)).map(item => item).join(',')
      this.ruleForm.ClassId = ClassIds
      this.ruleForm.PublishTime = this.radio == 1 ? dayjs().format('YYYY-MM-DD HH:mm:ss') : this.ruleForm.PublishTime
      this.ruleForm.PaperId = this.$route.query.paperId || this.paperInfo.Id
      // 作答模式（1逐题提交(正常作答)、2整卷提交）目前用于电子作业答题
      this.ruleForm.DoAnswerMode = 1
      const { data: res } = await this.$uwonhttp.post('/Question/Question/PaperPublishToClass', this.ruleForm)
      if(res.Success) {
        this.$message.success('布置成功！')
        this.handleClose()
        if(this.$route.path === '/Paper/Exam_MicroLesson/QuestionBankResourceCenter') {
          this.$parent.$emit('allSuccess')
        } else {
          this.$router.push({
            path: '/Paper/Exam_MicroLesson/QuestionBankResourceCenter',
            query:{
              activeType: 'ZuoYeResources'
            }
          })
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
    /**
     * 将 QuestionList 中每个对象的 penColors 转为图片并上传，返回图片地址
     * @returns {Promise<Array>} 返回每个题目的图片地址数组
     */
    async uploadPenColorImgs() {
      const uploadPromises = [];
      // 1. 遍历 this.QuestionList
      let useColor = false
      for (let question of this.QuestionList) {
        if (question.usePenColors && Array.isArray(question.penColors) && question.penColors.length > 0) {
          useColor = true
          // 2. 对每个颜色生成图片并上传
          for (const color of question.penColors) {
            // 生成 base64 图片
            const imgBase64 = colorToImg(color, 32);
            // 转为Blob对象
            const byteString = atob(imgBase64.split(',')[1]);
            const mimeString = imgBase64.split(',')[0].split(':')[1].split(';')[0];
            const ab = new ArrayBuffer(byteString.length);
            const ia = new Uint8Array(ab);
            for (let i = 0; i < byteString.length; i++) {
              ia[i] = byteString.charCodeAt(i);
            }
            const blob = new Blob([ab], { type: mimeString });
            // 生成唯一文件名
            const fileName = `penColor_${question.ItemId || ''}_${color.replace('#', '')}_${Date.now()}.png`;
            // 使用 obsClient 上传
            const uploadPromise = new Promise((resolve, reject) => {
              obsClient.putObject({
                Bucket: 'instant', // 替换为你的桶名
                Key: fileName,
                Body: blob,
                ContentType: 'image/png'
              }, (err, result) => {
                if (!err && result && result.CommonMsg.Status === 200) {
                  // 拼接图片访问地址（根据你的OBS配置调整）
                  const url = `https://instant.obs.cn-east-2.myhuaweicloud.com/${fileName}`;
                  resolve({
                    ItemId: question.ItemId,
                    Color: color,
                    ImgUrl: url
                  });
                } else {
                  this.$message.error('颜色图片上传失败');
                  resolve(null);
                }
              });
            });
            uploadPromises.push(uploadPromise);
          }
        }
      }
      if (!useColor) {
        return true; // 如果没有颜色数据，直接返回 true
      }
      const results = await Promise.all(uploadPromises);
      console.log('2上传结果:', results);
      const res = await this.$uwonhttp.post('/MDPen/MDPen/ItemHandwritingColorSetting', {
        PaperId: this.$route.query.paperId || this.paperInfo.Id,
        ItemColors: results.filter(Boolean) // 过滤掉上传失败的结果
      });
      if (res.data.Success) {
        console.log('3上传成功');
        return true
      } else {
        this.$message.error('题目笔迹颜色保存失败！');
        return false
      }
    },
  }
}
</script>

<style lang="less" scoped>
.subQuestions{
  li{
    display: flex;
    align-items: center;
    margin: 6px 0;
  }
}
</style>