<template>
  <el-dialog title="笔迹颜色设置" width="40%" :visible.sync="dialogFormVisible">
    <div class="videoPadleft">
      <div class="switchDis margin_b_15">
        <div class="margin_r_15">
          开启学生笔迹颜色：<el-switch v-model="switchFrom" @change="switchChange"></el-switch>
        </div>
      </div>
      <div v-show="switchFrom" class="flex flex_align_center margin_tb_10">
        <div class="color-list">
          <span
            v-for="(color, idx) in penColors"
            :key="color"
            class="color-item"
            :style="{ background: color }"
          >
            <i class="el-icon-close" @click.stop="removeColor(idx)"></i>
          </span>
        </div>
        <el-color-picker
          v-model="currentColor"
          @change="addColor"
          :disabled="penColors.length >= 5"
        ></el-color-picker>
      </div>
      <p>最多选 5 种颜色</p>
      <div slot="footer" class="dialog-footer" style="display: flex;justify-content: center;">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveColors">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectPenColor',
  data() {
    return {
      switchFrom: true, // 是否开启学生笔迹颜色
      dialogFormVisible: false, // 是否显示对话框
      penColors: [], // 已选颜色
      currentColor: '', // 当前选择颜色
      questionItem: null // 当前题目
    }
  },
  computed: {
    
  },
  methods: {
    show(stem) {
      this.dialogFormVisible = true;
      this.penColors = stem.penColors || [];
      this.questionItem = stem;
    },
    switchChange(e) {},
    addColor(color) {
      if (!color) return;
      if (this.penColors.includes(color)) return;
      if (this.penColors.length < 5) {
        this.penColors.push(color);
      }
      this.currentColor = '';
    },
    removeColor(idx) {
      this.penColors.splice(idx, 1);
    },
    saveColors() {
      if (this.penColors.length === 0 && this.switchFrom) {
        this.$message.error('请至少选择一种颜色');
        return;
      }
      this.questionItem.usePenColors = this.switchFrom;
      this.questionItem.penColors = this.penColors
      this.dialogFormVisible = false;
    }
  }
}
</script>

<style lang="less" scoped>
.color-list {
  display: flex;
  align-items: center;
  margin-right: 12px;
  .color-item {
    width: 32px;
    height: 32px;
    border-radius: 5px;
    margin-right: 8px;
    border: 2px solid #eee;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-icon-close {
      position: absolute;
      top: -8px;
      right: -8px;
      font-size: 14px;
      color: #f56c6c;
      background: #fff;
      border-radius: 50%;
      cursor: pointer;
      border: 1px solid #eee;
    }
  }
}
</style>