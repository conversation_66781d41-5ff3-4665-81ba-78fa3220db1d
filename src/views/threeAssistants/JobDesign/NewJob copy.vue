<template>
  <div class="NewJob position_relative NewJob-container" style="height: 100%">
    <div class="flex flex_direction_col" style="height: 100%">
      <div ref="newJobHeader" class="newJobHeader back_white border_radius_4 margin_b_5 commonPadding">
        <span @click="goBack"><i class="el-icon-arrow-left font_size_24 cursor_pointer"></i></span>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <el-button :disabled="useType == 'scan' || IsWriting" @click="goToCheck" type="primary" icon="el-icon-plus"
          >新增题目</el-button
        >
        <span style="color:#7FB99A;font-size:16px;float:right;line-height:38px;">
          <i class="el-icon-warning-outline"></i>
          您可以拖拽题目对其进行排序
        </span>
      </div>
      <div class="commonPadding main back_white border_radius_4 margin_b_5 flex_item_grow" style="width: 100%;">
        <template v-if="QuestionList.length > 0">
          <!-- 注意：这里的删除和编辑和作业资源里调不一样的接口 -->
          <draggable v-model="QuestionList" forceFallback="true" handle=".drag-img" chosenClass="chosen" @change="DragChange" @start="addBackgroundOverlay"
          @end="removeBackgroundOverlay">
            <StemCard
              v-for="(Q, index) in QuestionList"
              :key="index + 'Q'"
              :index="index"
              :stem="Q"
              :openInDialog="openInDialog"
              :useType="useType"
              :isShowDrag="true"
              :showMask="showMask"
              :IsSetScore="IsSetScore"
              :PaperTag="PaperTag"
              :addRemoveBtn="false"
              @updateList="HomeworkItemInfoList"
              @addGenJin="addGenJin"
              @scanGenJin="scanGenJin"
              @openVideo="openVideo"
              @changeScore="changeScore"
            ></StemCard>
          </draggable>
        </template>
        <div v-else class="width_full text_align_center empty font_size_20">没有查询到题目信息哦~</div>
      </div>
      <div class="footer back_white border_radius_4 commonPadding" style="display: flex;justify-content: space-between;align-items: center;">
        <div></div>
        <div>
        <el-button v-if="useType == 'scan'" type="primary" @click="$router.go(-1)">返回</el-button>
        <template v-else>
          <el-button
            @click="BeModelPaper"
            :disabled="QuestionList.length == 0"
            plain
            type="success"
            icon="el-icon-document"
            >保存作业</el-button
          >
          &nbsp;&nbsp;&nbsp;&nbsp;
          <el-button
            :disabled="QuestionList.length == 0"
            type="primary"
            icon="el-icon-s-promotion"
            @click="openPublishSet"
            >作业布置</el-button
          >
          &nbsp;&nbsp;&nbsp;&nbsp;
          <el-button :disabled="QuestionList.length == 0" type="primary" @click="view" icon="el-icon-notebook-2">作业预览</el-button>
        </template>
        </div>
        <div>
          <p style="font-size: 18px;color: #000000;" v-show="$store.state.LoginSource == 1 && IsSetScore == 1">
<!--            当前总计<span style="color: #F55E2F">{{QuestionList.length}}</span>题，共计{{ TotalScore }}分-->
            当前总计<span style="color: #F55E2F">{{QuestionList.length}}</span>题，共计{{ newTotalScore }}分
          </p>
        </div>
      </div>
    </div>
<!--  作业列表中新增试题窗口  -->
    <el-dialog :visible.sync="AddDialogShow" :width="dialogWidth" :top="dialogHeight" :before-close="cancelAdd" @close="cancelAddClose">
      <div slot="title">
        <template v-if="whoUseDialog == 'add'">
          <el-tabs v-model="tabPane" @tab-click="handleClick">
            <el-tab-pane label="从作业资源中选择题目" name="1">
              <div style="height: 67vh">
                <newJobResources ref="newJobResources" :openInDialog="true" :ZKZl="true" :genjin="genjin" @NewEldio="NewEldio" @comfirmAdd="comfirmAdd"></newJobResources>
              </div>
            </el-tab-pane>
            <el-tab-pane label="新增题目" name="2">
              <div style="height: 67vh; overflow-y: auto">
                <singleEntry
                  ref="singleEntry"
                  :headerState="false"
                  @itemList="itemList"
                  :genjin="genjin"
                  @handleData="handleData"
                ></singleEntry>
              </div>
            </el-tab-pane>
            <el-tab-pane label="文件导入" name="3" v-if="!genjin">
              <div style="height: 67vh">
                <importing ref="importing" :headerState="false"></importing>
              </div>
            </el-tab-pane>
          </el-tabs>
        </template>
        <template v-if="whoUseDialog == 'publish'">
          <p class="text_align_center font_size_24"><b>作业布置</b></p>
        </template>
      </div>
      <PublishSet
        style="margin-top: 30px"
        v-if="whoUseDialog == 'publish' && AddDialogShow"
        ref="PublishSet"
      ></PublishSet>
<!--      <div v-show="tabPane == 2" slot="footer" class="text_align_center margin_t_15">-->
      <div v-show="tabPane == 2 || whoUseDialog == 'publish'" slot="footer" class="text_align_center margin_t_15">
        <el-button @click="cancelAdd">取消</el-button>
        <el-button type="primary" @click="comfirmAdd">确认</el-button>
      </div>
    </el-dialog>
    <!--  同步弹窗  -->
    <synchronous-or-not ref="synchronousOrNot"></synchronous-or-not>
    <!-- 预览 -->
    <studentPracticeDialog
      :isShowTitle="false"
      :isDialog="scanGenJinDia > 0 || PaperScan"
      :genjinSlot="scanGenJinDia > 0"
      :paperId="AssignParams.PaperId"
      :BindingId="BindingId"
      @handleGenjin="handleGenjin"
      @close="closeScan"
      ref="studentPracticeDialog"
    >
    </studentPracticeDialog>
    <!--  授权发布弹窗  -->
    <dialogCom ref="dialogCom" title="作业发布" :timeShow="true"></dialogCom>
    <!--  音频上传弹窗  -->
    <specialized-training-voice-binding ref="specializedTrainingVoice" @refresh="HomeworkItemInfoList"></specialized-training-voice-binding>
    <!--  专科专练作业布置窗口  -->
    <newPublishSet ref="newPublishSet" :QuestionList="QuestionList" :PaperTag="PaperTag" @PaperPublishToClass="PaperPublishToClass" @openParentSuccess="()=>$refs.helpSuccess.init()"></newPublishSet>
    <!--  平台求助成功提示弹窗  -->
    <help-success ref="helpSuccess" :showTrue="true" @goHelpSeekingProgress="goHelpSeekingProgress"></help-success>
  </div>
</template>
<script>
import StemCard from '../newJobResources/StemCard.vue'
import newJobResources from '../newJobResources'
import PublishSet from './PublishSet'
import singleEntry from '@/views/threeAssistants/AddANewTopic/index'
import importing from '@/views/threeAssistants/BulkImportEditing/Importing'
import synchronousOrNot from '@/views/threeAssistants/JobDesign/components/synchronousOrNot'
import draggable from 'vuedraggable'
import studentPracticeDialog from '@/views/prepareLessons/achievement/components/studentPracticeDialog'
import dialogCom from '@/views/threeAssistants/components/authorization/dialogCom'
import specializedTrainingVoiceBinding from '@/views/threeAssistants/JobDesign/components/specializedTrainingVoiceBinding'
import newPublishSet from '@/views/threeAssistants/JobDesign/components/newPublishSet'
import helpSuccess from '@/views/threeAssistants/JobDesign/components/helpSuccess'
import { Session } from '@/utils/storage'

export default {
  name: 'NewJob',
  components: {
    StemCard,
    newJobResources,
    PublishSet,
    singleEntry,
    importing,
    synchronousOrNot,
    draggable,
    studentPracticeDialog,
    dialogCom,
    specializedTrainingVoiceBinding,
    newPublishSet,
    helpSuccess
  },
  data() {
    return {
      showMask: false,
      QuestionList: [],
      AddDialogShow: false,
      tabPane: '1',
      whoUseDialog: 'add', // add-添加  publish-布置
      AssignParams: {
        PaperId: this.$route.query.paperId,
        PublishTime: '',
        ShareAreaCity: 1,
      },
      openInDialog: false,
      genjin: false,
      scanGenJinDia: -1,
      BindingId: '', //当前正在操作绑定跟进相关的小题Id
      PaperScan: false,
      replaceBind: {},
      routeReturnStatus: false,
      IsSetScore: 0, // 是否设置分数
      TotalScore: 0, // 试卷总分值
      newTotalScore: 0,
      FileId: '', //文件Id中fileId
      PaperTag: 0 // 试卷作文专项提高
    }
  },
  computed: {
    useType() {
      const { id } = this.$route.query
      if (id == 100) return 'new'
      if (id == 101) return 'scan'
      if (id == 102) return 'use'
      // if (id == 102) return 'edit'
      if (id == 103) return 'edit'
    },
    dialogWidth() {
      return this.whoUseDialog == 'add' ? '96%' : '30%'
    },
    dialogHeight() {
      return this.whoUseDialog == 'add' ? '5vh' : '15vh'
    },
    // 作文题限制
    IsWriting() {
      return this.PaperTag === 26 && this.QuestionList.length == 1
    },
  },
  watch: {
    useType: {
      handler(nv) {
        this.QuestionList = []
      },
    },
    $route: {
      handler(nv) {
        this.AssignParams.PaperId = this.$route.query.paperId
        const routerState = nv.path === '/threeAssistants/JobDesign/NewJob'
        if (nv.query.testQuestionBankIds && routerState) this.AssignHomeworkCopyItem(nv.query.testQuestionBankIds.split(','))
        if (nv.query.paperId && routerState) this.HomeworkItemInfoList()
        if (nv.query.trigger && routerState) this.submitQuestionId(JSON.parse(nv.query.itemList))
        if (!routerState) this.cancelAdd()
      },
    },
  },
  // 监听路由清空分数
  beforeRouteLeave(to, from, next) {
    Session.remove('TotalScore')
    next()
  },
  created() {
    // this.HomeworkItemInfoList()
    /* 生成作业部分 */
    const { testQuestionBankIds } = this.$route.query
    if (testQuestionBankIds) {
      const BankIds = testQuestionBankIds.split(',')
      this.AssignHomeworkCopyItem(BankIds)
    } else {
      this.HomeworkItemInfoList()
    }
  },
  methods: {
    view(){
      this.PaperScan = true
      this.$refs.studentPracticeDialog.lockPreview(this.$route.query.paperId)
    },
    closeScan() {
      this.scanGenJinDia = -1
      this.PaperScan = false
    },
    // 新增试题的弹窗切换组件
    handleClick(tab){
      if(tab.name === '2'){
        this.$refs.singleEntry.getItemType()
      }
    },
    // 开启语音设置弹窗
    openVideo(itemId){
      this.$refs.specializedTrainingVoice.init(itemId)
    },
    // 分值设置
    changeScore(obj){
      const { index, stem } = obj
      const totalScore = this.QuestionList.filter(item => item.ItemId != stem.ItemId).reduce((acc, cur) => acc + cur.ItemScore, 0)
      this.TotalScore = Session.get('TotalScore')
      if(totalScore + stem.ItemScore > this.TotalScore){
        this.$confirm(`<strong>您设置的分数相加已超过 <span style="color:#F55E2F;">总分：${this.TotalScore}</span>， 请返回重新修改</strong>`, '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          center: true,
          dangerouslyUseHTMLString: true,
          showCancelButton: false
        }).then(() => {
          return false
        }).catch(() => {
          return false
        });
      }
      this.$uwonhttp.post('/Question/Question/PaperItemUpdateScore',{
        itemId: stem.ItemId,
        paperId: this.$route.query.paperId,
        score: stem.ItemScore
      }).then(res => {
        if(!res.data.Success) return this.$message.error(res.data.Msg)
        this.HomeworkItemInfoList()
        this.QuestionList[index].scoreShow = false
      })
    },
    // 删除或者替换跟进
    async handleGenjin({ target, todo }) {
      // 1-删除 2-替换
      if (todo === 1) {
        const { data } = await this.$uwonhttp.post('/Question/Question/DelItemAdaptive', { adaptiveId: target.ItemId })
        if (!data.Success) return this.$message.error(data.Msg)
        this.$message.success('删除成功！')
        this.closeScan()
        this.cancelAdd()
        this.HomeworkItemInfoList()
      }
      if (todo === 2) {
        this.closeScan()
        this.replaceBind = {
          IsReplace: true,
          AdaptiveId: target.ItemId,
        }
        this.addGenJin(this.BindingId)
      }
    },
    //添加跟进练习
    addGenJin(Id) {
      this.genjin = true
      this.BindingId = Id
      this.goToCheck()
    },
    scanGenJin({ Id, index }) {
      this.scanGenJinDia = index + 1
      this.BindingId = Id
      this.$refs.studentPracticeDialog.lockPreview(this.$route.query.paperId)
    },
    //拖动
    async DragChange(event) {
      let ItemIds = this.QuestionList.map((item) => item.ItemId)
      const { data } = await this.$uwonhttp.post('/Question/Question/SetItemOrderBy', {
        PaperId: this.$route.query.paperId,
        ItemIds,
      })
      if (!data.Success) return this.$message.error(data.Msg)
    },
    // 返回
    goBack() {
      // if(this.$route.query.trigger){
      //   this.$router.resolve({
      //     path: '/threeAssistants/workResources'
      //   })
      // } else {
      if(localStorage.getItem('LoginSource') == 2){
        if(this.routeReturnStatus){
          this.$router.replace({
            path: '/threeAssistants/newJobResources/index'
          })
          this.routeReturnStatus = false
        } else {
          this.$router.go(-1)
        }
      } else {
        this.$router.go(-1)
      }
      // }
    },
    //选题
    async AssignHomeworkCopyItem(itemIds) {
      const { NewJobType} = this.$route.query
      let res
      if(NewJobType && NewJobType === '试题栏'){
        res = await this.$uwonhttp.post('/Question/Question/QuestionItemBasketCreatePaper', {
          paperId: this.$route.query.paperId,
          UserId: localStorage.getItem('UserId'),
        })
      } else {
        res = await this.$uwonhttp.post('/Question/Question/AssignHomeworkCopyItem', {
          paperId: this.$route.query.paperId,
          questionIds: itemIds,
          UserId: localStorage.getItem('UserId'),
        })
      }
      // const { data } = await this.$uwonhttp.post('/Question/Question/AssignHomeworkCopyItem', {
      // // const { data } = await this.$uwonhttp.post('/Question/Question/QuestionItemBasketCreatePaper', {
      //   paperId: this.$route.query.paperId,
      //   questionIds: itemIds,
      //   UserId: localStorage.getItem('UserId'),
      // })
      if (!res.data.Success) {
        this.$message.error(res.data.Msg)
        return
      } else {
        // 生成作业流程
        if (itemIds) {
          // 更换路由携带的参数
          const { paperId, id, ChapterId, GradeId, Semester, TextbookId, Year } = this.$route.query
          // const href = `/threeAssistants/JobDesign/NewJob?paperId=${paperId}&id=${id}&ChapterId=${ChapterId}&GradeId=${GradeId}&Semester=${Semester}&TextbookId=${TextbookId}&Year=${Year}`
          // window.location.replace(href)
          this.$router.replace({
            path: '/threeAssistants/JobDesign/NewJob',
            query: {
              paperId,
              id,
              ChapterId,
              GradeId,
              Semester,
              TextbookId,
              Year,
            },
          })
          this.routeReturnStatus = true
        }
        this.$message.success('选入成功！')
      }
    },
    //调取试卷包含的试题列表  拿到paperId请求接口
    async HomeworkItemInfoList() {
      const { data } = await this.$uwonhttp.post('/Question/Question/HomeworkItemInfoList', {
        paperId: this.$route.query.paperId,
      })
      if (!data.Success) return this.$message.error(data.Msg)
      this.IsSetScore = data.Data.IsSetScore
      // this.TotalScore = data.Data.TotalScore
      this.newTotalScore = data.Data.TotalScore
      this.PaperTag = data.Data.PaperTag
      this.FileId = data.Data.FileId
      data.Data.ItemInfos.forEach(item => {
        item.scoreShow = false
      })
      this.QuestionList = data.Data.ItemInfos
    },
    //选题
    goToCheck() {
      this.whoUseDialog = 'add'
      this.AddDialogShow = true
      this.openInDialog = true
      //设置默认值
      // this.$store.commit('SET_INIT_VALUE', [
      //   { key: 'ChapterId', value: this.$route.query.ChapterId },
      //   // { key: 'State', value: -1 },
      // ])
      this.$nextTick(() => {
        // 新增题目调用组件内方法
        // console.log('执行逻辑')
        this.$refs.newJobResources.popoverComponentRequest()
        // this.$refs.newJobResources.clearTreeCheckChange([this.$route.query.ChapterId])
        // this.$refs.newJobResources.GetQuestions()
      })
    },
    // 取消添加
    cancelAdd() {
      this.AddDialogShow = false
      this.openInDialog = false
      this.genjin = false
      this.BindingId = ''
      this.tabPane = '1'
      this.replaceBind = {}
    },
    // 弹窗关闭的回调
    cancelAddClose(){
      this.$store.state.chapterStorage.itemIds = []
    },
    // 弹窗加入逻辑
    async NewEldio(){
      let QuestionList = this.$refs.newJobResources.QuestionList.filter((item) => item.checked)
      const itemIds = QuestionList.map((item) => item.Id)
      const newItemIds = [...new Set([...itemIds, ...this.$store.state.chapterStorage.itemIds])]
      this.$store.dispatch('chapterStorage/setItemIds', newItemIds)
      await this.AssignHomeworkCopyItem(itemIds)
      // this.cancelAdd()
      this.HomeworkItemInfoList()
    },
    // 确认添加
    async comfirmAdd() {
      //添加跟进练习-题库选题方式
      if (this.genjin && this.tabPane == '1') {
        let QuestionList = this.$refs.newJobResources.QuestionList.filter((item) => item.checked)
        await this.SaveItemBindingAdaptive({ QuestionId: QuestionList[0].Id })
        this.cancelAdd()
        this.HomeworkItemInfoList()
        return
      }
      //弹窗功能新增试题且是选题增加时
      if (this.tabPane == '1' && this.whoUseDialog == 'add') {
        let QuestionList = this.$refs.newJobResources.QuestionList.filter((item) => item.checked)
        // 作文专项题
        if(this.PaperTag === 26){
          if(QuestionList.length > 1 || QuestionList[0].QuestionTypeId !== '26'){
            this.$message.error('作文专项题只能添加1道作文题！')
            return false
          }
        }
        const itemIds = QuestionList.map((item) => item.Id)
        await this.AssignHomeworkCopyItem(itemIds)
        this.cancelAdd()
        this.HomeworkItemInfoList()
      }
      // 新增题目
      if (this.tabPane == '2' && this.whoUseDialog == 'add') {
        this.$refs.singleEntry.inputItem()
      }
      //弹窗功能-立即发布
      if (this.whoUseDialog == 'publish') {
        let result = this.$refs.PublishSet.handleQueryData()
        result.ClassId = result.ClassId.join(',')
        if (result) {
          this.AssignParams = Object.assign(this.AssignParams, result)
          this.PaperPublishToClass()
        }
      }
      // 试题导入的确认
      if (this.tabPane == '3') {
        this.$message.info('请选择文件！')
        return
      }
    },
    async handleData(data) {
      const { ItemInfos } = data
      await this.SaveItemBindingAdaptive({ AdaptiveInfo: ItemInfos[0] })
      this.cancelAdd()
      this.HomeworkItemInfoList()
    },
    // 添加绑定练习
    async SaveItemBindingAdaptive(query) {
      let params = {
        PaperId: this.$route.query.paperId,
        ItemId: this.BindingId,
        UserId: localStorage.getItem('UserId'),
        UserRoleId: localStorage.getItem('role'),
      }
      params = Object.assign(params, query, this.replaceBind)
      const { data } = await this.$uwonhttp.post('/Question/Question/SaveItemBindingAdaptive', params)
      if (!data.Success) return this.$message.error(data.Msg)
      if (data.Success && this.tabPane == '1') this.$message.success('添加成功！')
      if (data.Success && this.tabPane == '2')
        this.$confirm('添加成功！是否同步录入作业资源', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success',
        })
          .then(async () => {
            const { data } = await this.$uwonhttp.post('/Question/Question/SaveQuestion', query.AdaptiveInfo)
            if (!data.Success) return this.$message.error(data.Msg)
            this.$message.success('同步录入成功！')
          })
          .catch(() => {})
    },
    // 保存作业
    BeModelPaper() {
      if(localStorage.getItem('LoginSource') === '2'){
        this.$router.push({ path: '/threeAssistants/workResources' })
      } else {
        this.$router.push({
          path: '/Paper/Exam_MicroLesson/QuestionBankResourceCenter',
          query:{
            activeType: 'ZuoYeResources'
          }
        })
      }
    },
    //打开立即布置弹窗
    openPublishSet() {
      // 三个助手布置作业
      if(localStorage.getItem('LoginSource') == 2) {
        console.log('三个助手')
        console.log(this.$store.state.Level)
        if (this.$store.state.Level == 5) {
          this.$refs.dialogCom.setPaperId(this.$route.query.paperId)
          this.$refs.dialogCom.init()
        } else {
          this.AddDialogShow = true
          this.whoUseDialog = 'publish'
        }
      } else {
        console.log('专科专练')
        // 专科专练布置
        const paperId = this.$route.query.paperId
        // 作文专题
        if(this.PaperTag === 26){
          this.$refs.newPublishSet.init({ Id: paperId, FileId: this.FileId, HomeworkType: 1 },{ UseScene: 2})
        } else {
          /* 下面这条代码想不起来业务逻辑为何这样写暂代 */
          this.$refs.newPublishSet.init({ Id: paperId, FileId: this.FileId })
        }
      }
    },
    //立即布置 调接口
    async PaperPublishToClass() {
      const { data } = await this.$uwonhttp.post('/Question/Question/PaperPublishToClass', this.AssignParams)
      if (!data.Success) return this.$message.error(data.Msg)
      if (data.Success) this.$message.success('发布成功！')
      this.$router.push({ path: '/threeAssistants/workResources' })
    },
    itemList(list) {
      this.$refs.singleEntry.resetObj()
      this.tabPane = '1'
      this.cancelAdd()
      this.HomeworkItemInfoList()
      // this.$refs.synchronousOrNot.init(list)
      this.submitQuestionId(list)

    },
    // 同步分享试题
    async submitQuestionId(list){
      const data = {
        PaperId: this.$route.query.paperId,
        UserId: localStorage.getItem('UserId'),
        ItemIds: list
      }
      const { data: res } = await this.$uwonhttp.post('/Question/Question/ItemCopyQuestionInfo', data)
      if(res.Success){
        this.$message.success('添加题目成功！')
        // 删除当前路由指定参数trigger/itemList
        const { paperId, id, ChapterId, GradeId, Semester, TextbookId, Year } = this.$route.query
        this.$router.replace({
          query: {
            paperId, id, ChapterId, GradeId, Semester, TextbookId, Year
          }
        })
      } else {
        this.$message.error(res.Msg)
      }
    },
    addBackgroundOverlay() {
      // 添加背景蒙版的逻辑，可以是添加一个有透明度的遮罩层
      // document.body.style.setProperty('background-color', 'rgba(0, 0, 0, 0.5)');
      this.showMask = true
    },
    removeBackgroundOverlay() {
      // 移除背景蒙版的逻辑，移除遮罩层
      // document.body.style.setProperty('background-color', 'transparent');
      this.showMask = false
    },
    // 跳转至求助平台页
    goHelpSeekingProgress(){
      const { Year, GradeId} = this.$route.query
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/helpSeekingProgress',
        query:{
          grade: GradeId,
          year: Year
        }
      })
    }
  },
  mounted() {},
}
</script>
<style lang="less" scoped>
.footer {
  width: calc(100% - 84px);
  box-shadow: 0 -3px 3px 0 #e6e6e6;
  position: fixed;
  bottom: 0;
  text-align: center;
  height: 60px;
}
.commonPadding {
  padding: 10px 20px;
}
.empty {
  padding: 100px 0;
}
.main {
  padding-bottom: 60px;
  overflow-y: scroll;
}
.main::-webkit-scrollbar {
  display: none;
}
.chosen{
  background-color: white !important;
}
/deep/.el-button{
  //font-size: 22px;
  font-size: var(--font16-22);
}
/deep/.el-checkbox__label{
  //font-size: 22px;
  font-size: var(--font16-22);
}
/deep/.el-tabs__item{
  //font-size: 24px;
  font-size: var(--font18-24);
}
/deep/ .el-checkbox{
  .el-checkbox__inner{
    //width: 20px;
    //height: 20px;
    width: var(--font16-20);
    height: var(--font16-20);
    &::after{
      //left: 8px;
      //top: 3px;
      left: var(--le6-8);
      top: var(--le2-3);
    }
  }
}
/deep/.el-radio__inner{
  //width: 20px !important;
  //height: 20px !important;
  width: var(--font16-20);
  height: var(--font16-20);
}
//.NewJob-container{
//  .el-dialog__body {
//    display: none;
//  }
//}
</style>