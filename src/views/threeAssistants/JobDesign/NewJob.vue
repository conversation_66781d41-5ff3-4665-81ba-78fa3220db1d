<template>
  <div class="NewJob position_relative NewJob-container" style="height: 100%">
    <div class="flex flex_direction_col" style="height: 100%">
      <div ref="newJobHeader" class="newJobHeader back_white border_radius_4 margin_b_5 commonPadding header-view">
        <span @click="goBack"><i class="el-icon-arrow-left font_size_24 cursor_pointer"></i></span>
        <el-steps style="flex: 1;" :active="stepActive" finish-status="success" align-center>
          <el-step title="创建作业"></el-step>
          <el-step title="题目组卷"></el-step>
        </el-steps>
        <span ><i class="el-icon-arrow-left font_size_24" style="color: white;"></i></span>
      </div>
      <div v-show="stepActive === 0" class="commonPadding main back_white border_radius_4 margin_b_5 flex_item_grow" style="width: 100%;">
        <addJobForm ref="addJobFormRef" :disabled="selectDisabled"></addJobForm>
      </div>
      <div v-show="stepActive === 1" class="detail-view main border_radius_4 margin_b_5">
        <div class="left-view">
          <div class="question-header" style="display:flex;align-items:center;justify-content:space-between;padding:16px 12px 8px 12px;">
            <span style="font-size:18px;color:#333;">共<span style="color:#7FB99A;">{{ QuestionList.length }}</span>道题目</span>
            <i class="el-icon-setting" style="font-size:18px;color:#ccc;" @click="GroupDialogShow = true"></i>
          </div>
          <!-- 外层 draggable 实现组间拖动 -->
          <draggable
            v-model="sortQuestionList"
            handle=".drag-group-icon"
            animation="200"
            :forceFallback="true"
            @end="DragGroupChange"
          >
            <transition-group>
              <!-- 遍历每个组 -->
              <div v-for="(group, groupIdx) in sortQuestionList" :key="groupIdx" class="question-group">
                <div v-if="itemGroupInfo.QuestionNumbering === 1" class="group-header" style="display: flex; align-items: center; padding: 8px;">
                  <span>{{ `${groupIdx+1}、${group[0].QuestionType}` }}</span>
                  <i class="el-icon-rank drag-group-icon" style="font-size:18px;color:#7FB99A;cursor:grab; margin-right: 8px;" title="拖动分组排序"></i>
                </div>
                <div
                  v-else
                  class="question-item"
                  :class="{ active: currentIndex === group[0].Sort }"
                  @click="selectQuestion(group[0], groupIdx)"
                  style="display:flex;align-items:center;justify-content:center;padding:0 12px;height:56px;;"
                >
                  <div
                    class="question-num"
                    :class="{ selected: currentIndex === group[0].Sort, 'unComplete': (!group[0].complete)&&(!group[0].ItemId) }"
                    style="display:flex;align-items:center;justify-content:center;font-size:18px;border:1px solid #222;border-radius:6px;height:30px;"
                  >
                    {{ group[0].Sort }}
                  </div>
                  <div class="question-actions" style="display:flex;align-items:center;margin-left:8px;">
                    <i
                      class="el-icon-delete"
                      style="font-size:18px;color:#7FB99A;cursor:pointer;margin-right:8px;"
                      @click="deleteQuestion(group[0])"
                    ></i>
                    <i
                      class="el-icon-rank drag-group-icon"
                      style="font-size:18px;color:#7FB99A;cursor:grab;"
                      title="拖动排序"
                    ></i>
                  </div>
                </div>
                <!-- 内层 draggable 实现组内拖动 按题型编号 -->
                <draggable
                  v-if="itemGroupInfo.QuestionNumbering === 1"
                  v-model="sortQuestionList[groupIdx]"
                  handle=".drag-icon"
                  animation="200"
                  :forceFallback="true"
                  @end="DragItemChange(groupIdx, $event)"
                >
                  <transition-group>
                    <!-- 遍历组内每个元素 -->
                    <div
                      v-for="(item, idx) in sortQuestionList[groupIdx]"
                      :key="item?.ItemId || `${groupIdx}-${idx}`"
                      class="question-item"
                      :class="{ active: currentIndex === item.Sort }"
                      @click="selectQuestion(item, idx)"
                      style="display:flex;align-items:center;justify-content:center;padding:0 12px;height:56px;;"
                    >
                      <div
                        class="question-num"
                        :class="{ selected: currentIndex === item.Sort, 'unComplete': (!item.complete)&&(!item.ItemId) }"
                        style="display:flex;align-items:center;justify-content:center;font-size:18px;border:1px solid #222;border-radius:6px;height:30px;"
                      >
                        {{ item.Sort }}
                      </div>
                      <div class="question-actions" style="display:flex;align-items:center;margin-left:8px;">
                        <i
                          class="el-icon-delete"
                          style="font-size:18px;color:#7FB99A;cursor:pointer;margin-right:8px;"
                          @click="deleteQuestion(item)"
                        ></i>
                        <i
                          class="el-icon-rank drag-icon"
                          style="font-size:18px;color:#7FB99A;cursor:grab;"
                          title="拖动排序"
                        ></i>
                      </div>
                    </div>
                  </transition-group>
                </draggable>
              </div>
            </transition-group>
          </draggable>
        </div>
        <div class="right-view">
          <div class="toolbar">
            <div style="display:flex;align-items:center;">
              <span style="font-size:18px;color:#222;margin-right:18px;">
                <i class="el-icon-circle-plus-outline" style="color:#7FB99A;font-size:18px;margin-right:4px;"></i>
                添加题目
              </span>
              <div style="display:flex;align-items:center;">
                <el-button
                  v-for="item in questionTypeList"
                  :key="item.QuestionType"
                  class="type-btn"
                  style="margin-right:16px;"
                  :disabled="PaperTag == 26&&item.QuestionType*1 != 26"
                  @click="handleAddQuestion(item)"
                >
                  {{ item.QuestionName }}
                </el-button>
              </div>
            </div>
            <div style="display:flex;align-items:center;">
              <span class="ai-import" style="color:#7FB99A;cursor:pointer;display:flex;align-items:center;margin-right:24px;" @click="handleAiImport">
                <i class="iconfont icon-zhinengshibie" style="font-size:18px;margin-right:4px;"></i>导入
              </span>
              <span class="bank-select" style="color:#7FB99A;cursor:pointer;" @click="goToCheck">题库选题</span>
            </div>
          </div>
          <el-scrollbar style="height:calc(100vh - 260px)" ref="myScrollbar">
            <div v-for="(item,index) in QuestionList" :key="`item${index}`" class="question-card" :class="{ 'active-card': currentIndex === item.Sort }">
              <StemCard
                v-if="item.ItemId"
                :index="index"
                :sortNum="item.Sort"
                :stem="item"
                :openInDialog="openInDialog"
                :useType="useType"
                :showMask="showMask"
                :IsSetScore="IsSetScore"
                :PaperTag="PaperTag"
                :addRemoveBtn="false"
                @updateList="HomeworkItemInfoList"
                @addGenJin="addGenJin"
                @scanGenJin="scanGenJin"
                @openVideo="openVideo"
                @changeScore="changeScore"
              ></StemCard>
              <template v-else>
                <div v-show="!item.expand" class="expand-view">
                  <p class="font_color_333" style="flex: 1;">
                    <span>{{ item.Sort }}、</span>
                    <span :style="{ background: QuestionType_Color.get(item.QuestionType) }" class="questionType">{{
                      item.QuestionType
                    }}</span>
                  </p>
                  <itemEditer ref="itemEditerRef" :queryInfo="queryInfo" :itemTypeId="item.ItemTypeId*1" :index="index" :itemData="item" @updateData="updateItemData"></itemEditer>
                  <p class="updown">
                    <span @click.stop="item.expand = true" class="font_size_16">收起<i class="el-icon-arrow-up"></i></span>
                  </p>
                </div>
                <div v-show="item.expand" class="expand-view preview-view">
                  <p class="font_color_333" style="flex: 1;">
                    <span>{{ item.Sort }}、</span>
                    <span :style="{ background: QuestionType_Color.get(item.QuestionType) }" class="questionType">{{
                      item.QuestionType
                    }}</span>
                    &nbsp;
                    <span class="title font_size_22 font_size_16" v-html="item.Title" v-katex></span>
                  </p>
                  <div v-if="item.Options !== null && item.Options?.length" class="options-list">
                    <ul class="ulOption">
                      <li v-for="opt in item.Options" :key="opt.Option">
                        {{ opt.Option }}. <span v-html="opt.Content" v-katex></span>
                      </li>
                    </ul>
                  </div>
                  <p class="updown">
                    <span @click.stop="item.expand = false" class="font_size_16">展开<i class="el-icon-arrow-down"></i></span>
                  </p>
                </div>
              </template>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <div v-show="stepActive == 0" class="footer back_white border_radius_4 commonPadding" style="display: flex;justify-content: center;align-items: center;">
        <el-button type="primary" @click="nextStepClick">下一步</el-button>
      </div>
      <div v-show="stepActive == 1">
        <div class="footer back_white border_radius_4 commonPadding" style="display: flex;justify-content: space-between;align-items: center;">
          <div></div>
          <div>
            <el-button v-if="useType == 'scan'" type="primary" @click="$router.go(-1)">返回</el-button>
            <template v-else>
              <el-button v-if="!$route.query.paperId" @click="stepActive = 0" plain type="success">上一步</el-button>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <el-button @click="BeModelPaper" :disabled="QuestionList.length == 0" plain type="success" icon="el-icon-document">保存作业</el-button>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <el-button
                :disabled="QuestionList.length == 0"
                type="primary"
                icon="el-icon-s-promotion"
                @click="openPublishSet">作业布置</el-button>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <el-button :disabled="QuestionList.length == 0" type="primary" @click="view" icon="el-icon-notebook-2">作业预览</el-button>
            </template>
          </div>
          <div>
            <p style="font-size: 18px;color: #000000;" v-show="$store.state.LoginSource == 1 && IsSetScore == 1">
              当前总计<span style="color: #F55E2F">{{QuestionList.length}}</span>题，共计{{ newTotalScore }}分
            </p>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="组题设置" width="40%" :visible.sync="GroupDialogShow" @close="GroupDialogShow = false">
      <div>
        <div class="flex flex_direction_row margin_b_10">
          <span class="margin_right_10">题目序号</span>
          <el-radio-group v-model="itemGroupInfo.QuestionNumbering">
            <el-radio class="font_size_22" :label="0">连续编号</el-radio>
            <el-radio class="font_size_22" :label="1">按题型编号</el-radio>
          </el-radio-group>
        </div>
        <div class="flex flex_direction_row margin_b_10">
          <span class="margin_right_10">题型设置</span>
          <el-radio-group v-model="itemGroupInfo.QuestionType">
            <el-radio class="font_size_22" :label="0">按题型归类</el-radio>
            <el-radio class="font_size_22" :label="1">不按题型归类</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button @click="GroupDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="handleGroupClick">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="AddDialogShow" :width="dialogWidth" :top="dialogHeight" :before-close="cancelAdd" @close="cancelAddClose">
      <div style="height: 77vh">
        <newJobResources ref="newJobResources" :openInDialog="true" :ZKZl="true" :genjin="genjin" @NewEldio="NewEldio" @comfirmAdd="comfirmAdd"></newJobResources>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="AddAiDialogShow" :width="dialogWidth" :top="dialogHeight" :before-close="cancelAdd" @close="cancelAddClose">
      <div style="height: 67vh">
        <importing ref="importing" :queryInfo="queryInfo" :headerState="false" :retRoutePath="false" @aiFinish="aiFinishItem"></importing>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="bulkImportEditingShow" :width="dialogWidth" :top="'0'" @close="bulkImportEditingShow=false">
      <div style="height: 97vh">
        <BulkImportEditing ref="BulkImportEditingRef" @aiAddItemFinish="aiAddItemFinish" @cancel="bulkImportEditingShow=false"></BulkImportEditing>
      </div>
    </el-dialog>
    <!--  同步弹窗  -->
    <synchronous-or-not ref="synchronousOrNot"></synchronous-or-not>
    <!-- 预览 -->
    <studentPracticeDialog
      :isShowTitle="false"
      :isDialog="scanGenJinDia > 0 || PaperScan"
      :genjinSlot="scanGenJinDia > 0"
      :paperId="AssignParams.PaperId"
      :BindingId="BindingId"
      @handleGenjin="handleGenjin"
      @close="closeScan"
      ref="studentPracticeDialog"
    >
    </studentPracticeDialog>
    <!--  授权发布弹窗  -->
    <dialogCom ref="dialogCom" title="作业发布" :timeShow="true"></dialogCom>
    <!--  音频上传弹窗  -->
    <specialized-training-voice-binding ref="specializedTrainingVoice" @refresh="HomeworkItemInfoList"></specialized-training-voice-binding>
    <!--  专科专练作业布置窗口  -->
    <newPublishSet ref="newPublishSet" routerText="NewJob" :QuestionList="QuestionList" :PaperTag="PaperTag" @PaperPublishToClass="PaperPublishToClass" 
      @PaperPublishBefore="PaperPublishBefore"
      @openParentSuccess="()=>$refs.helpSuccess.init()"></newPublishSet>
    <!--  平台求助成功提示弹窗  -->
    <help-success ref="helpSuccess" :showTrue="true" @goHelpSeekingProgress="goHelpSeekingProgress"></help-success>
    <select-pen-color ref="selectPenColorRef" ></select-pen-color>
  </div>
</template>
<script>
import addJobForm from '../components/JobDesign/addJobForm.vue'
import StemCard from '../newJobResources/StemCard.vue'
import newJobResources from '../newJobResources'
import PublishSet from './PublishSet'
import singleEntry from '@/views/threeAssistants/AddANewTopic/index'
import importing from '@/views/threeAssistants/BulkImportEditing/Importing'
import BulkImportEditing from '@/views/threeAssistants/BulkImportEditing/index'
import synchronousOrNot from '@/views/threeAssistants/JobDesign/components/synchronousOrNot'
import draggable from 'vuedraggable'
import studentPracticeDialog from '@/views/prepareLessons/achievement/components/studentPracticeDialog'
import dialogCom from '@/views/threeAssistants/components/authorization/dialogCom'
import specializedTrainingVoiceBinding from '@/views/threeAssistants/JobDesign/components/specializedTrainingVoiceBinding'
import selectPenColor from '@/views/threeAssistants/JobDesign/components/selectPenColor'
import newPublishSet from '@/views/threeAssistants/JobDesign/components/newPublishSet'
import helpSuccess from '@/views/threeAssistants/JobDesign/components/helpSuccess'
import { Session } from '@/utils/storage'
import itemEditer from '@/views/threeAssistants/AddANewTopic/components/itemEditer/index.vue'

export default {
  name: 'NewJob',
  components: {
    addJobForm,
    StemCard,
    newJobResources,
    PublishSet,
    singleEntry,
    importing,
    synchronousOrNot,
    draggable,
    studentPracticeDialog,
    dialogCom,
    specializedTrainingVoiceBinding,
    selectPenColor,
    newPublishSet,
    helpSuccess,
    itemEditer,
    BulkImportEditing
  },
  data() {
    return {
      QuestionType_Color: new Map([
        ['单项选择题', '#FA9415'],
        ['多项选择题', '#5FA9F7'],
        ['判断题', '#D52E20'],
        ['填空题', '#77C357'],
        ['连线题', '#625FF7'],
        ['主观题', '#00ABA1'],
        ['简答题', '#57C3C3'],
        ['作文', '#00ABA1'],
      ]),
      stepActive: 0, // 步骤条的激活状态
      queryInfo: {}, // 路由传递的参数
      currentIndex: 1, // 当前选中的题目索引
      GroupDialogShow: false, // 组题设置弹窗
      selectDisabled: false, // 选项是否禁用
      itemGroupInfo: {
        QuestionNumbering: 0,
        QuestionType: 1
      }, // 组题信息
      showMask: false,
      questionTypeList: [], // 试题类型列表
      QuestionList: [],
      sortQuestionList: [],
      itemTypeId: 0, // 题目类型Id
      AddDialogShow: false,
      AddAiDialogShow: false,
      bulkImportEditingShow: false,
      tabPane: '1',
      whoUseDialog: 'add', // add-添加  publish-布置
      AssignParams: {
        PaperId: '',
        PublishTime: '',
        ShareAreaCity: 1,
      },
      openInDialog: false,
      genjin: false,
      scanGenJinDia: -1,
      BindingId: '', //当前正在操作绑定跟进相关的小题Id
      PaperScan: false,
      replaceBind: {},
      routeReturnStatus: false,
      IsSetScore: 0, // 是否设置分数
      TotalScore: 0, // 试卷总分值
      newTotalScore: 0,
      FileId: '', //文件Id中fileId
      PaperTag: 0 // 试卷作文专项提高
    }
  },
  computed: {
    useType() {
      const { id } = this.queryInfo
      if (id == 100) return 'new'
      if (id == 101) return 'scan'
      if (id == 102) return 'use'
      // if (id == 102) return 'edit'
      if (id == 103) return 'edit'
    },
    dialogWidth() {
      return this.whoUseDialog == 'add' ? '96%' : '30%'
    },
    dialogHeight() {
      return this.whoUseDialog == 'add' ? '5vh' : '15vh'
    },
    // 作文题限制
    IsWriting() {
      return this.PaperTag === 26 && this.QuestionList.length == 1
    },
  },
  watch: {
    useType: {
      handler(nv) {
        this.QuestionList = []
        this.sortQuestionList = []
      },
    },
    $route: {
      handler(nv) {
        if (nv.path != '/threeAssistants/JobDesign/NewJob') {
          return false
        }
        this.AssignParams.PaperId = this.$route.query.paperId
        this.queryInfo = this.$route.query
        this.selectDisabled = false
        const { testQuestionBankIds } = nv.query
        if (testQuestionBankIds) {
          this.$refs.addJobFormRef.setBankIds(testQuestionBankIds.split(','))
          this.$refs.addJobFormRef.init(nv.query, '试题栏')
        } else {
          this.$refs.addJobFormRef.setBankIds([])
          this.$refs.addJobFormRef.init(nv.query, '')
        }
        // this.stepActive = 0
      },
    },
    QuestionList: {
      handler(nv) {
        this.handleGroupClick()
      },
    }
  },
  // 监听路由清空分数
  beforeRouteLeave(to, from, next) {
    Session.remove('TotalScore')
    next()
  },
  activated() {
    this.stepActive = this.$route.query.paperId ? 1 : 0
    if (this.$route.query.copy) {
      this.stepActive = 0
      this.$refs.addJobFormRef.init(this.$route.query, '')
    }
    if (this.$route.query.paperId) {
      this.queryInfo = this.$route.query
      this.HomeworkItemInfoList()
    }
  },
  created() {
    /* 生成作业部分 */
    this.GetQuestionType()
    this.$nextTick(() => {
      const { testQuestionBankIds } = this.$route.query
      if (testQuestionBankIds) {
        this.$refs.addJobFormRef.setBankIds(testQuestionBankIds.split(','))
        this.$refs.addJobFormRef.init(this.$route.query, '试题栏')
      } else {
        this.$refs.addJobFormRef.init(this.$route.query, '')
      }
    })
  },
  methods: {
    // 下一步
    async nextStepClick() {
      const query = await this.$refs.addJobFormRef.submit()
      if (!query) return
      this.queryInfo = query
      this.PaperTag = this.queryInfo.IsComposition === 1 ? 26 : 0
      if (this.selectDisabled) {
        this.stepActive = 1
        return false
      }
      this.selectDisabled = true
      this.AssignParams.PaperId = this.queryInfo.paperId
      const routerState = this.$route.path === '/threeAssistants/JobDesign/NewJob'
      if (this.queryInfo.testQuestionBankIds && routerState) this.QuestionItemBasketCreatePaper(this.queryInfo.testQuestionBankIds.split(','))
      if (this.queryInfo.paperId && routerState) this.HomeworkItemInfoList()
      if (this.queryInfo.trigger && routerState) this.submitQuestionId(JSON.parse(this.queryInfo.itemList))
      if (!routerState) this.cancelAdd()
      this.stepActive = 1
    },
    view() {
      this.PaperScan = true
      this.$refs.studentPracticeDialog.lockPreview(this.queryInfo.paperId)
    },
    // 获取试题类型
    async GetQuestionType() {
      const { data: res } = await this.$uwonhttp.get('/Question/Question/GetQuestionType')
      if(res.Success) {
        this.questionTypeList = res.Data
      } else {
        this.$message.error(res.Msg)
      }
    },
    closeScan() {
      this.scanGenJinDia = -1
      this.PaperScan = false
    },
    // 新增试题的弹窗切换组件
    handleClick(tab) {
      if(tab.name === '2') {
        this.$refs.singleEntry.getItemType()
      }
    },
    // 开启语音设置弹窗
    openVideo(itemId) {
      this.$refs.specializedTrainingVoice.init(itemId)
    },
    // 设置笔迹颜色
    changeInkColor(stem) {
      this.$refs.selectPenColorRef.show(stem)
    },
    // 分值设置
    changeScore(obj) {
      const { index, stem } = obj
      const totalScore = this.QuestionList.filter(item => item.ItemId != stem.ItemId).reduce((acc, cur) => acc + cur.ItemScore, 0)
      this.TotalScore = Session.get('TotalScore')
      if(totalScore + stem.ItemScore > this.TotalScore) {
        this.$confirm(`<strong>您设置的分数相加已超过 <span style="color:#F55E2F;">总分：${this.TotalScore}</span>， 请返回重新修改</strong>`, '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          center: true,
          dangerouslyUseHTMLString: true,
          showCancelButton: false
        }).then(() => {
          return false
        }).catch(() => {
          return false
        });
      }
      this.$uwonhttp.post('/Question/Question/PaperItemUpdateScore',{
        itemId: stem.ItemId,
        paperId: this.queryInfo.paperId,
        score: stem.ItemScore
      }).then(res => {
        if(!res.data.Success) return this.$message.error(res.data.Msg)
        this.HomeworkItemInfoList()
        this.QuestionList[index].scoreShow = false
      })
    },
    // 删除或者替换跟进
    async handleGenjin({ target, todo }) {
      // 1-删除 2-替换
      if (todo === 1) {
        const { data } = await this.$uwonhttp.post('/Question/Question/DelItemAdaptive', { adaptiveId: target.ItemId })
        if (!data.Success) return this.$message.error(data.Msg)
        this.$message.success('删除成功！')
        this.closeScan()
        this.cancelAdd()
        this.HomeworkItemInfoList()
      }
      if (todo === 2) {
        this.closeScan()
        this.replaceBind = {
          IsReplace: true,
          AdaptiveId: target.ItemId,
        }
        this.addGenJin(this.BindingId)
      }
    },
    //添加跟进练习
    addGenJin(Id) {
      this.genjin = true
      this.BindingId = Id
      this.goToCheck()
    },
    scanGenJin({ Id, index }) {
      this.scanGenJinDia = index + 1
      this.BindingId = Id
      this.$refs.studentPracticeDialog.lockPreview(this.queryInfo.paperId)
    },
    // 删除
    deleteQuestion(item) {
      this.$confirm('确定删除该题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.QuestionList = this.QuestionList.filter((i) => i.Sort !== item.Sort)
        this.QuestionList = this.QuestionList.map((item, index) => {
          item.Sort = this.QuestionList.length - index
          return item
        })
        this.handleGroupClick()
      })
    },
    //拖动
    async DragChange(event) {
      let ItemIds = this.QuestionList.map((item) => item.ItemId)
      const { data } = await this.$uwonhttp.post('/Question/Question/SetItemOrderBy', {
        PaperId: this.queryInfo.paperId,
        ItemIds,
      })
      if (!data.Success) return this.$message.error(data.Msg)
    },
    DragGroupChange(event) {
      console.log('分组拖动结束', event, this.sortQuestionList);
      console.log(this.sortQuestionList);
      // 可在这里添加分组拖动结束后的逻辑，如更新服务器数据
    },
    // 组内元素拖动结束回调
    DragItemChange(groupIdx, event) {
      console.log(`第 ${groupIdx + 1} 组内元素拖动结束`, event);
      // 手动更新 sortQuestionList
      this.$set(this.sortQuestionList, groupIdx, event.moved.newIndex !== undefined ? event.moved.draggedContext.element : this.sortQuestionList[groupIdx]);
      // 可在这里添加组内元素拖动结束后的逻辑，如更新服务器数据
      console.log(`第 ${groupIdx + 1} 组内元素拖动结束`, event);
    },
    // 组题
    handleGroupClick() {
      this.GroupDialogShow = false
      const { QuestionNumbering, QuestionType } = this.itemGroupInfo;
      const reverseList = [...this.QuestionList].reverse();
      let sortList = []
      switch (`${QuestionNumbering}${QuestionType}`) {
        case '00': // 连续编号，按题型
          const typeList1 = []
          reverseList.forEach(item => {
            if (!typeList1.includes(item.ItemTypeId)) {
              typeList1.push(item.ItemTypeId)
            }
          })
          typeList1.forEach(type => {
            sortList.push(reverseList.filter(item => item.ItemTypeId === type))
          })
          const temp = []
          sortList.forEach(item => {
            item.forEach(i => {
              temp.push([i])
            })
          })
          sortList = temp
          break;
        case '01':
          // 连续编号，不按题型
          reverseList.forEach(item => {
            sortList.push([item])
          })
          break;
        case '10': // 按题型编号，按题型
          const typeList = []
          reverseList.forEach(item => {
            if (!typeList.includes(item.ItemTypeId)) {
              typeList.push(item.ItemTypeId)
            }
          })
          typeList.forEach(type => {
            sortList.push(reverseList.filter(item => item.ItemTypeId === type))
          })
          break;
        case '11':
          // 按题型编号，不按题型
          let currentGroup = null;
          reverseList.forEach((item) => {
            if (!currentGroup || currentGroup[0].ItemTypeId !== item.ItemTypeId) {
              // 如果当前组不存在或者当前元素的 ItemTypeId 与当前组不同，创建新组
              currentGroup = [item];
              sortList.push(currentGroup);
            } else {
              // 否则将当前元素添加到当前组
              currentGroup.push(item);
            }
          });
          break;
        default:
          break;
      }
      this.sortQuestionList = sortList
    },
    // 返回
    goBack() {
      if(localStorage.getItem('LoginSource') == 2) {
        if(this.routeReturnStatus) {
          this.$router.replace({
            path: '/threeAssistants/newJobResources/index'
          })
          this.routeReturnStatus = false
        } else {
          this.$router.go(-1)
        }
      } else {
        this.$router.go(-1)
      }
      // }
    },
    //选题
    async QuestionItemBasketCreatePaper(itemIds) {
      const res = await this.$uwonhttp.post('/Question/Question/QuestionItemBasketCreatePaper', {
          paperId: this.queryInfo.paperId,
          UserId: localStorage.getItem('UserId'),
        })
      if (!res.data.Success) {
        this.$message.error(res.data.Msg)
        return
      } else {
        // 生成作业流程
        if (itemIds) {
          this.HomeworkItemInfoList()
          this.routeReturnStatus = true
        }
        this.$message.success('选入成功！')
      }
    },
    async AssignCopyItemFromBlank(itemIds) {
      await this.$uwonhttp.post('/Question/Question/AssignHomeworkCopyItem', {
          paperId: this.queryInfo.paperId,
          questionIds: itemIds,
          UserId: localStorage.getItem('UserId'),
        })
    },
    // 调取试卷包含的试题列表  拿到paperId请求接口
    async HomeworkItemInfoList() {
      const { data } = await this.$uwonhttp.post('/Question/Question/HomeworkItemInfoList', {
        paperId: this.queryInfo.paperId,
      })
      if (!data.Success) return this.$message.error(data.Msg)
      this.IsSetScore = data.Data.IsSetScore
      // this.TotalScore = data.Data.TotalScore
      this.newTotalScore = data.Data.TotalScore
      this.PaperTag = data.Data.PaperTag
      this.FileId = data.Data.FileId
      data.Data.ItemInfos.forEach(item => {
        item.scoreShow = false
      })
      this.QuestionList = data.Data.ItemInfos
      this.handleGroupClick()
    },
    // 从题库选题后获取试题
    async selectItemIntoQuestionList() {
      // 保存编辑数据
      this.checkItemCompletion(true)
      const { data } = await this.$uwonhttp.post('/Question/Question/HomeworkItemInfoList', {
        paperId: this.queryInfo.paperId,
      })
      if (!data.Success) return this.$message.error(data.Msg)
      this.newTotalScore = data.Data.TotalScore
      this.PaperTag = data.Data.PaperTag
      this.FileId = data.Data.FileId
      data.Data.ItemInfos.forEach(item => {
        item.scoreShow = false
      })
      console.log('1111', this.QuestionList);
      // 提取当前 QuestionList 中的 ItemId
      const currentItemIds = this.QuestionList.map(item => item.ItemId);
      // 找出 data.Data.ItemInfos 中 ItemId 与当前 QuestionList 不一样的对象
      const differentItems = data.Data.ItemInfos.filter(item => !currentItemIds.includes(item.ItemId));
      this.QuestionList = [...differentItems, ...this.QuestionList]
      this.QuestionList = this.QuestionList.map((item, index) => {
        item.Sort = this.QuestionList.length - index
        return item
      })
      console.log('2222', this.QuestionList);
      this.currentIndex = this.QuestionList.length
      this.handleGroupClick()
    },
    // 取消添加
    cancelAdd() {
      this.AddDialogShow = false
      this.AddAiDialogShow = false
      this.openInDialog = false
      this.genjin = false
      this.BindingId = ''
      this.tabPane = '1'
      this.replaceBind = {}
    },
    // 弹窗关闭的回调
    cancelAddClose() {
      this.$store.state.chapterStorage.itemIds = []
    },
    // 弹窗跟进加入逻辑
    async NewEldio() {
      let QuestionList = this.$refs.newJobResources.QuestionList.filter((item) => item.checked)
      const itemIds = QuestionList.map((item) => item.Id)
      const newItemIds = [...new Set([...itemIds, ...this.$store.state.chapterStorage.itemIds])]
      this.$store.dispatch('chapterStorage/setItemIds', newItemIds)
      await this.AssignCopyItemFromBlank(itemIds)
      // this.cancelAdd()
      this.selectItemIntoQuestionList()
    },
    // 确认添加
    async comfirmAdd() {
      //添加跟进练习-题库选题方式
      if (this.genjin && this.tabPane == '1') {
        let QuestionList = this.$refs.newJobResources.QuestionList.filter((item) => item.checked)
        await this.SaveItemBindingAdaptive({ QuestionId: QuestionList[0].Id })
        this.cancelAdd()
        this.selectItemIntoQuestionList()
        return
      }
      //弹窗功能新增试题且是选题增加时
      if (this.tabPane == '1' && this.whoUseDialog == 'add') {
        let QuestionList = this.$refs.newJobResources.QuestionList.filter((item) => item.checked)
        // 作文专项题
        if(this.PaperTag === 26) {
          if(QuestionList.length > 1 || QuestionList[0].QuestionTypeId !== '26') {
            this.$message.error('作文专项题只能添加1道作文题！')
            return false
          }
        }
        const itemIds = QuestionList.map((item) => item.Id)
        await this.AssignCopyItemFromBlank(itemIds)
        this.cancelAdd()
        this.HomeworkItemInfoList()
      }
      // 新增题目
      if (this.tabPane == '2' && this.whoUseDialog == 'add') {
        this.$refs.singleEntry.inputItem()
      }
      //弹窗功能-立即发布
      if (this.whoUseDialog == 'publish') {
        let result = this.$refs.PublishSet.handleQueryData()
        result.ClassId = result.ClassId.join(',')
        if (result) {
          this.AssignParams = Object.assign(this.AssignParams, result)
          this.PaperPublishToClass()
        }
      }
      // 试题导入的确认
      if (this.tabPane == '3') {
        this.$message.info('请选择文件！')
        return
      }
    },
    async handleData(data) {
      const { ItemInfos } = data
      await this.SaveItemBindingAdaptive({ AdaptiveInfo: ItemInfos[0] })
      this.cancelAdd()
      this.HomeworkItemInfoList()
    },
    // 添加绑定练习
    async SaveItemBindingAdaptive(query) {
      let params = {
        PaperId: this.queryInfo.paperId,
        ItemId: this.BindingId,
        UserId: localStorage.getItem('UserId'),
        UserRoleId: localStorage.getItem('role'),
      }
      params = Object.assign(params, query, this.replaceBind)
      const { data } = await this.$uwonhttp.post('/Question/Question/SaveItemBindingAdaptive', params)
      if (!data.Success) return this.$message.error(data.Msg)
      if (data.Success && this.tabPane == '1') this.$message.success('添加成功！')
      if (data.Success && this.tabPane == '2')
        this.$confirm('添加成功！是否同步录入作业资源', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success',
        })
          .then(async () => {
            const { data } = await this.$uwonhttp.post('/Question/Question/SaveQuestion', query.AdaptiveInfo)
            if (!data.Success) return this.$message.error(data.Msg)
            this.$message.success('同步录入成功！')
          })
          .catch(() => {})
    },
    // 保存作业
    async BeModelPaper() {
      // 检查题目是否编辑完整
      if (!this.checkItemCompletion()) {
        return false
      }
      await this.addQuestionRequest()
      if (localStorage.getItem('LoginSource') === '2') {
        this.$router.push({ path: '/threeAssistants/workResources' })
      } else {
        this.$router.push({
          path: '/Paper/Exam_MicroLesson/QuestionBankResourceCenter',
          query:{
            activeType: 'ZuoYeResources'
          }
        })
      }
    },
    //打开立即布置弹窗
    async openPublishSet() {
      // 检查题目是否编辑完整
      if (!this.checkItemCompletion()) {
        return false
      }
      // 三个助手布置作业
      if(localStorage.getItem('LoginSource') == 2) {
        console.log('三个助手')
        console.log(this.$store.state.Level)
        if (this.$store.state.Level == 5) {
          this.$refs.dialogCom.setPaperId(this.queryInfo.paperId)
          this.$refs.dialogCom.init()
        } else {
          this.$refs.newPublishSet.init({ Id: this.queryInfo.paperId, FileId: this.FileId })
        }
      } else {
        console.log('专科专练')
        // 专科专练布置
        const paperId = this.queryInfo.paperId
        // 作文专题
        if (this.PaperTag === 26) {
          this.$refs.newPublishSet.init({ Id: paperId, FileId: this.FileId, HomeworkType: 1 },{ UseScene: 2})
        } else {
          /* 下面这条代码想不起来业务逻辑为何这样写暂代 */
          this.$refs.newPublishSet.init({ Id: paperId, FileId: this.FileId })
        }
      }
    },
    //立即布置 调接口
    async PaperPublishToClass() {
      await this.addQuestionRequest()
      const { data } = await this.$uwonhttp.post('/Question/Question/PaperPublishToClass', this.AssignParams)
      if (!data.Success) return this.$message.error(data.Msg)
      if (data.Success) this.$message.success('发布成功！')
      this.$router.push({ path: '/threeAssistants/workResources' })
    },
    itemList(list) {
      this.$refs.singleEntry.resetObj()
      this.tabPane = '1'
      this.cancelAdd()
      this.HomeworkItemInfoList()
      // this.$refs.synchronousOrNot.init(list)
      this.submitQuestionId(list)
    },
    // 同步分享试题
    async submitQuestionId(list) {
      const data = {
        PaperId: this.queryInfo.paperId,
        UserId: localStorage.getItem('UserId'),
        ItemIds: list
      }
      const { data: res } = await this.$uwonhttp.post('/Question/Question/ItemCopyQuestionInfo', data)
      if(res.Success){
        this.$message.success('添加题目成功！')
        // 删除当前路由指定参数trigger/itemList
        const { paperId, id, ChapterId, GradeId, Semester, TextbookId, Year } = this.queryInfo
        this.$router.replace({
          query: {
            paperId, id, ChapterId, GradeId, Semester, TextbookId, Year
          }
        })
      } else {
        this.$message.error(res.Msg)
      }
    },
    addBackgroundOverlay() {
      // 添加背景蒙版的逻辑，可以是添加一个有透明度的遮罩层
      // document.body.style.setProperty('background-color', 'rgba(0, 0, 0, 0.5)');
      this.showMask = true
    },
    removeBackgroundOverlay() {
      // 移除背景蒙版的逻辑，移除遮罩层
      // document.body.style.setProperty('background-color', 'transparent');
      this.showMask = false
    },
    // 跳转至求助平台页
    goHelpSeekingProgress() {
      const { Year, GradeId} = this.queryInfo
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/helpSeekingProgress',
        query:{
          grade: GradeId,
          year: Year
        }
      })
    },
    // 添加题目
    handleAddQuestion(item) {
      if (this.IsWriting) {
        this.$message.error('作文专项题只能添加1道作文题！');
        return false;
      }
      this.itemTypeId = item.QuestionType
      this.QuestionList.unshift({
        ItemId: '',
        ItemTypeId: item.QuestionType,
        QuestionTypeId: item.QuestionType,
        QuestionType: item.QuestionName,
        ItemScore: 0,
        expand: false
      })
      this.QuestionList = this.QuestionList.map((item, index) => {
        item.Sort = this.QuestionList.length - index
        return item
      })
      this.currentIndex = this.QuestionList.length
      this.handleGroupClick()
    },
    handleAiImport() {
      if (this.IsWriting) {
        this.$message.error('作文专项题只能添加1道作文题！');
        return false;
      }
      // 处理AI导入逻辑
      this.AddAiDialogShow = true
      // this.aiFinishItem('1948557966921248768')
    },
    // 题库选题
    goToCheck() {
      if (this.IsWriting) {
        this.$message.error('作文专项题只能添加1道作文题！');
        return false;
      }
      this.whoUseDialog = 'add'
      this.AddDialogShow = true
      this.openInDialog = true
      this.$nextTick(() => {
        this.$refs.newJobResources.popoverComponentRequest()
      })
    },
    // 发布弹框点击确定执行
    async PaperPublishBefore() {
      if (!this.checkItemCompletion()) {
        return false
      }
      await this.addQuestionRequest()
      // 组题设置排序
      await this.setItemSort()
      this.$refs.newPublishSet.submitFormAfter()
    },
    async setItemSort() {
      const { data: { Data } } = await this.$uwonhttp.post('/Question/Question/HomeworkItemInfoList', {
        paperId: this.queryInfo.paperId
      })
      const { ItemInfos } = Data
      const ItemOrderInfos = []
      this.sortQuestionList.forEach((item, index) => {
        const ItemOrders = []
        item.forEach((v, k) => {
          ItemOrders.push({
            ItemId: v.ItemId ? v.ItemId : ItemInfos.find(item => item.TemporaryId == v.Sort).ItemId,
            Order: k + 1
          })
        })
        ItemOrderInfos.push({ QuestionType: item[0].QuestionType, Order: index + 1, ItemOrders })
      })
      await this.$uwonhttp.post('/Question/Question/SetItemOrderByBatch', {
        PaperId: this.queryInfo.paperId,
        ItemOrderInfos
      })
    },
    // 更新题目信息
    updateItemData(e, i) {
      this.QuestionList[i] = e
      this.$set(this.QuestionList, i, e)
      this.$nextTick(() => {
        this.$refs.myScrollbar.wrap.scrollTop = 0 // 滚动到顶部
      })
    },
    // 左侧选择题目
    selectQuestion(item, index) {
      this.currentIndex = item.Sort;
      // 滚动到对应 div
      this.$nextTick(() => {
        const scrollbar = this.$refs.myScrollbar.$el.querySelector('.el-scrollbar__wrap');
        const targetDiv = this.$refs.myScrollbar.$el.querySelector(`.question-card:nth-child(${this.QuestionList.length - item.Sort + 1})`);
        if (scrollbar && targetDiv) {
          const scrollTop = targetDiv.offsetTop - scrollbar.offsetTop;
          scrollbar.scrollTop = scrollTop;
        }
      });
    },
    /**
     * @description 保存编辑数据后检查题目编辑是否完成
     * @param isTip 是否提示
     */
    checkItemCompletion(isTip) {
      let res = true
      const refList = this.$refs.itemEditerRef
      if (Array.isArray(refList)) {
        for (let index = 0; index < refList.length; index++) {
          const ref = refList[index];
          ref.inputItem()
          if (!ref.Public.complete) {
            res = false
            if (!isTip) this.$message.error(`第${ref.itemData.Sort}题未完成编辑，请检查！`)
          }
          const data = ref.addQuestion()
          this.updateItemData(data, ref.index)
        }
        return res
      }
      return res
    },
    // 将新增题目信息提交到后端
    async addQuestionRequest() {
      const refList = this.$refs.itemEditerRef
      if (Array.isArray(refList)) {
        try {
          // 依次等待每个组件的 addQuestionRequest 方法执行完成
          for (let index = 0; index < refList.length; index++) {
            const ref = refList[index];
            await ref.addQuestionRequest();
          }
        } catch (error) {
          this.$message.error('题目请求添加失败，请重试');
        }
      }
    },
    // ai识别文件完成
    async aiFinishItem(fileId) {
      this.AddAiDialogShow = false
      this.bulkImportEditingShow = true
      this.$nextTick(() => {
        this.$refs.BulkImportEditingRef?.dialogInit(this.queryInfo, fileId)
      })
    },
    aiAddItemFinish() {
      this.bulkImportEditingShow = false
      this.selectItemIntoQuestionList()
    }
  },
}
</script>
<style lang="less" scoped>
.footer {
  width: calc(100% - 30px);
  box-shadow: 0 -3px 3px 0 #e6e6e6;
  position: fixed;
  bottom: 0;
  text-align: center;
  height: 60px;
}
.commonPadding {
  padding: 10px 20px;
}
.header-view{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.empty {
  padding: 100px 0;
}
.main {
  padding-bottom: 60px;
  overflow-y: scroll;
}
.main::-webkit-scrollbar {
  display: none;
}
.chosen{
  background-color: white !important;
}
.questionType {
  display: inline-block;
  color: #fff;
  padding: 3px 5px;
  border-radius: 4px;
  font-size: 12px;
}
.question-group {
  // border: 1px solid #e6e6e6;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: #fff;
}

.group-header {
  font-size: 20px;
  justify-content: space-between;
  background-color: white;
  border-bottom: 1px solid #e6e6e6;
  cursor: move;
}
.detail-view{
  display: flex;
  width: 100%;
  height: 100%;
  .left-view{
    margin-right: 20px;
    width: 300px;
    height: 100%;
    overflow-y: auto;
    background: white;
    padding: 0;
    .question-header {
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 8px;
    }
    .question-item {
      margin-bottom: 12px;
      cursor: pointer;
      transition: background 0.2s;
      &.active {
        background: #f5faf7;
      }
      .question-num {
        width: 120px !important;
        height: 30px;
        border: 1px solid #222;
        border-radius: 6px;
        font-size: 18px;
        text-align: center;
        line-height: 30px;
        background: #fff;
        transition: all 0.2s;
        &.selected {
          background: #7FB99A;
          color: #fff;
          border-color: #7FB99A;
        }
        &.unComplete{
          background: #FFC600;
          color: #fff;
          border-color: #FFC600;
          border: 1px solid #FFC600;
        }
      }
      .question-actions {
        display: flex;
        align-items: center;
        .el-icon-delete,
        .el-icon-rank {
          transition: color 0.2s;
        }
        .el-icon-delete:hover,
        .el-icon-rank:hover {
          color: #F55E2F;
        }
      }
    }
  }
  .right-view{
    flex: 1;
    // width: 70%;
    height: 100%;
    overflow-y: auto;
    background: rgb(240, 242, 245);
    ::v-deep .el-scrollbar__wrap {
      overflow-x: hidden;
      overflow-y: scroll;

      .el-scrollbar__view {
        height: 100%;
      }
    }
  }
  .toolbar{
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: white;
    margin-bottom: 16px;
  }
  .type-btn {
    border: 1px solid rgb(204, 204, 204);
    border-radius: 8px;
    background: #fff;
    color: #544949;
    font-size: 18px;
    padding: 0 28px;
    height: 44px;
    margin-right: 16px;
    transition: all 0.2s;
    &:hover {
      border-color: #7FB99A;
      color: #7FB99A;
      background: #f5faf7;
    }
  }
  .ai-import {
    font-size: 18px;
  }
  .bank-select {
    font-size: 18px;
    margin-left: 8px;
  }
  .question-card{
    border-radius: 8px;
    margin-bottom: 16px;
  }
  .active-card{
    border: 1px solid #7FB99A;
  }
  .expand-view{
    background: #fff;
    p{
      padding: 16px;
    }
    .updown{
      margin: 0 10px;
      text-align: right;
      color: #7FB99A;
      border-top: 1px dashed #5f5252;
      span{
        cursor: pointer;
      }
    }
  }
  .options-list{
    padding: 0 16px;
  }
  .ulOption{
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    li{
      font-size: var(--font16-20);
      margin-right: 78px;
      font-size: 16px;
      color: #333333;
    }
  }
}
/deep/.el-button{
  //font-size: 22px;
  font-size: var(--font16-22);
}
/deep/.el-checkbox__label{
  //font-size: 22px;
  font-size: var(--font16-22);
}
/deep/.el-tabs__item{
  //font-size: 24px;
  font-size: var(--font18-24);
}
/deep/ .el-checkbox{
  .el-checkbox__inner{
    //width: 20px;
    //height: 20px;
    width: var(--font16-20);
    height: var(--font16-20);
    &::after{
      //left: 8px;
      //top: 3px;
      left: var(--le6-8);
      top: var(--le2-3);
    }
  }
}
/deep/.el-radio__inner{
  //width: 20px !important;
  //height: 20px !important;
  width: var(--font16-20);
  height: var(--font16-20);
}
//.NewJob-container{
//  .el-dialog__body {
//    display: none;
//  }
//}
</style>