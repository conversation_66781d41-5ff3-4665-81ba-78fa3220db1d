<template>
  <div class="workResources h-100">
    <!--  左侧  -->
      <div
        class="margin_r_5 back_white h-100 flex_item_shrink"
        style="transition: all ease 0.3s"
        :style="{ width: leftWidth + '%' }">
        <left-screening :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 16 ? 2.3 : 16" @getAllList="getLeftScreening"></left-screening>
      </div>
    <!--  右侧  -->
    <div class="_right">
      <div class="btnList">
        <test-paper-level-switching ref="testPaperLevelSwitching" @OpenAddAJobPopup="OpenAddAJobPopup" @levelBook="levelBook"></test-paper-level-switching>
      </div>
      <div class="workList" v-if="resourceData.length > 0">
<!--        <div class="cont" :style="{height: PaperType != 3 ? '27.9vh' : '25.3vh'}" v-for="(item, ind) in resourceData" :key="ind">-->
        <div class="cont" v-for="(item, ind) in resourceData" :key="ind">
          <div class="top">
            <img class="titlLog" src="@/assets/word.png" />
            <div class="rightTxt">
              <el-tooltip
                class="item"
                effect="dark"
                :disabled="item.Title.length <= 8"
                :content="item.Title"
                placement="top"
              >
                <div class="tit_img" style="display: flex;align-items: center;">
                  <div>{{ item.Title.length > 8 ? item.Title.substring(0, 8) + '...' : item.Title }}</div>
                  <img v-show="item.PaperType == -2" src="@/assets/市.png" />
                  <img v-show="item.PaperType == 0" src="@/assets/区.png" />
                  <img v-show="item.PaperType == 18" src="@/assets/校.png" />
                  <img v-show="item.PaperType == 1" src="@/assets/班.png" />
                </div>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :disabled="item.ChapterName.length <= 10"
                :content="item.ChapterName"
                placement="bottom"
              >
                <p>
                  所属章节：{{
                    item.ChapterName.length > 10 ? item.ChapterName.substring(0, 10) + '...' : item.ChapterName
                  }}
                </p>
              </el-tooltip>
              <div>
                <p v-if="item.State === '2'">更新时间：{{ item.CreateTime }}</p>
<!--                <p v-show="item.Type === '1'">发布时间：{{ item.PublishDateTime }}</p>-->
                <p v-if="item.State === '1'">创建时间：{{ item.PublishDateTime }}</p>
              </div>
              <p v-show="item.TextbookName !== null">
                教材：{{ item.TextbookName }}
              </p>
              <div style="display: flex;">
                <p style="margin-right: 20px;">布置班级数：{{item.AssignClassQuantity}}</p>
                <p>已做学生数：{{item.DoPaperQuantity}}</p>
              </div>
            </div>
          </div>
          <div class="botm_btn">
              <!--  Type后台枚举：1已发布(已布置)、2未发布(草稿)、3校共享、4教师共享、5愿意共享、6学校发布、7他人共享、8我的共享 -->
              <!--  PaperType后台枚举：(5市、4区、3校、2我的(班)、1共享)  -->
<!--              <el-button v-show="['1', '3', '4', '5', '6', '7', '8'].includes(item.Type)" size="small" plain @click="previewHandel(item)">预览</el-button>-->
<!--              <div v-show="[1,3,4].includes(PaperType * 1)">-->
<!--                <el-button size="small" plain @click="OpenAddAJobPopup(item)">引用</el-button>-->
<!--              </div>-->
<!--              <div v-show="[2,5].includes(PaperType * 1)">-->
<!--                <el-button size="small" plain @click="OpenAddAJobPopup(item)">复制</el-button>-->
<!--              </div>-->
<!--              <div v-show="[2,5].includes(PaperType * 1)">-->
<!--                <el-button size="small" v-show="item.Type === '1'" :disabled="item.DoPaperQuantity > 0" plain @click="RevocationPublish(item)">撤销发布</el-button>-->
<!--                <el-button v-show="item.Type === '2'" size="small" plain @click="GoToNewJob(item, 103)">编辑</el-button>-->
<!--                <el-button size="small" v-show="item.Type === '2'" plain @click="DelPaperDesign(item)">删除</el-button>-->
<!--              </div>-->
<!--              <el-button v-show="['1','2'].includes(item.Type)" class="Arrangement" type="primary" size="small" plain @click="AssignmentLayout(item.Id)">作业布置</el-button>-->
<!--     下面是新业务逻辑     -->
            <el-button v-show="item.State !== '2'" size="small" plain @click="previewHandel(item)">预览</el-button>
            <el-button v-show="item.State === '2'" size="small" plain @click="GoToNewJob(item, 103)">编辑</el-button>
            <el-button v-if="item.State !== '3'" class="Arrangement" type="primary" size="small" plain @click="AssignmentLayout(item.Id)">布置</el-button>
            <el-button v-else class="Arrangement" type="primary" size="small" plain  @click="OpenAddAJobPopup(item)">复制</el-button>
            <el-popover
              placement="right"
              trigger="hover">
              <div style="display: flex;flex-direction: column;">
                <el-button type="text" style="font-size: 16px;color: #333;" @click="OpenAddAJobPopup(item)">复制</el-button>
                <el-button type="text" v-show="item.State === '1'" style="font-size: 16px;color: #333;" @click="nullifyOfPubliDel('作废',item.Id)">作废</el-button>
                <el-button type="text" v-show="(PaperType == 2 && item.State === '2')" style="font-size: 16px;color: #333;" @click="nullifyOfPubliDel('删除',item.Id)">删除</el-button>
                <el-button type="text" v-show="PaperType == 2 && item.State === '1'" style="font-size: 16px;color: #333;" @click="collegialSharings(item)">同校共享</el-button>
                <el-button type="text" v-show="item.State === '1' && item.DoPaperQuantity <= 0" style="font-size: 16px;color: #333;" @click="nullifyOfPubliDel('撤销',item.Id)">撤销发布</el-button>
              </div>
              <el-button class="More" v-show="item.State !== '3'" slot="reference" type="text" style="margin-left: 12px;">
                <i class="el-icon-more"></i>
              </el-button>
            </el-popover>
          </div>
          <div class="statusImg">
            <img v-show="item.State === '1'" src="@/assets/yibuzhi.png" />
            <img v-show="item.State === '2'" src="@/assets/weibuzhi.png" />
            <img v-show="item.State === '3'" src="@/assets/zuoFei.png" />
          </div>
        </div>
      </div>
      <div v-else class="noData">
        <div>
          <img src="@/assets/lack/暂无搜索记录.png" alt="" />
        </div>
      </div>
      <div v-if="total > 6" class="pageFenYe">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page.sync="queryText.PageIndex"
          :page-size="queryText.PageSize"
          layout="total, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>
    <!-- 预览 -->
    <preview-paper ref="previewPaper" @addPaperItem="addPaperItem" @removePreviewItem="removePreviewItem" :btn-show="true"></preview-paper>
<!--    <studentPracticeDialog :isShowTitle="false" :isDialog.sync="isDialog" :paperId="PaperId" ref="studentPracticeDialog"></studentPracticeDialog>-->
<!--  新增作业弹窗  -->
    <add-a-job-popup ref="addAJobPopup"></add-a-job-popup>
    <!--  布置作业弹窗  -->
    <el-dialog
      title="作业布置"
      :visible.sync="AssignmentLayoutShow"
      width="30%"
      :before-close="handleClose">
        <!-- <publish-set ref="PublishSet" v-if="AssignmentLayoutShow" :paperId="PaperId" :params="queryText.PaperType === 2 ? ['ClassId','IsImmediatelyPublish','ShareSet'] : ['ClassId','IsImmediatelyPublish']"></publish-set> -->
        <publish-set ref="PublishSet" v-if="AssignmentLayoutShow" :paperId="PaperId" :params="publishParams"></publish-set>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
    </el-dialog>
  <!--  市管理端作业发布  -->
    <dialog-com ref="dialogCom" title="作业发布" :time-show="true" :close-on-click-modal="true" @authorization="GetPaperDesignInfo"></dialog-com>
    <!--  同校共享  -->
    <collegial-sharing ref="collegialSharing" @getSuccess="GetPaperDesignInfo"></collegial-sharing>
    <!--  题目栏  -->
    <item-list
    ref="itemList"
    @preview="() => {$refs.previewPaper.GetQuestionItemBasketPreviewInfo()}"
    @generateAJob="generatingJobs"></item-list>
  </div>
</template>

<script>
import ChapterMenus from '@/views/threeAssistants/newJobResources/ChapterMenus'
import TestPaperLevelSwitching from '@/views/threeAssistants/components/JobDesign/TestPaperLevelSwitching'
import AddAJobPopup from '@/views/threeAssistants/components/JobDesign/AddAJobPopup'
import PublishSet from './JobDesign/PublishSet'
// import studentPracticeDialog from '@/views/prepareLessons/achievement/components/studentPracticeDialog'
import dialogCom from '@/views/threeAssistants/components/authorization/dialogCom'
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import collegialSharing from '@/views/threeAssistants/JobDesign/components/collegialSharing.vue'
import itemList from '@/views/threeAssistants/newJobResources/components/itemList/index.vue'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper.vue'

export default {
  name: 'workResources',
  components:{
    collegialSharing,
    ChapterMenus,
    TestPaperLevelSwitching,
    AddAJobPopup,
    PublishSet,
    // studentPracticeDialog,
    dialogCom,
    leftScreening,
    itemList,
    previewPaper
  },
  data() {
    return {
      isDialog:false,// 预览弹框
      resourceData: [],
      total: 0,
      leftWidth: 16, //动态控制布局
      // chapterIds: [],
      GradeId: '',
      // 获取数据定义的参数
      queryText: {
        // AreaId: '',
        // SchoolId: '',
        // PaperName: '',
        // TextbookId: '',
        // Type: 0,
        // PaperType: 2,
        TextbookId: '',
        PageIndex: 1,
        PageSize: 6,
        // Sort: 1,
      },
      // 布置作业显示和隐藏
      PaperId: '', // 记录试卷Id
      AssignmentLayoutShow: false,
      publishParams:null,
      // 左侧组件缓存入参
      // leftSearch: {},
      PaperType: '',
    }
  },
  // 使用watch监听路由/threeAssistants/workResources
  watch: {
    $route(to, from) {
      if (to.path === '/threeAssistants/workResources') {
        this.GetQuestionItemBasketInfo()
      }
    }
  },
  methods:{
    // 生成作业开弹窗
    generatingJobs(checkedIds) {
      // const { Year, Semester, GradeId } = this.commonData
      // const [ unit, ChapterId ] = this.chapterIds
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      const [ unit, ChapterId ] = Ids.length == 1 ? [ParentId[0], Ids[0]] : ['','']
      const passingParameters = {
        Year: Year,
        Semester: Term,
        GradeId: GradeId,
        unit ,
        ChapterId,
        TextbookId: -1,
      }
      // 题库ID数组
      // this.$refs.addAJobPopup.setBankIds(this.checkedIds)
      this.$refs.addAJobPopup.setBankIds(checkedIds)
      this.$refs.addAJobPopup.init(passingParameters, '试题栏')
    },
    // 更新试题栏数据
    GetQuestionItemBasketInfo(){
      this.$refs.itemList.GetQuestionItemBasketInfo()
    },
    // 试卷预览的添加
    addPaperItem(Id){
      this.$refs.itemList.SaveQuestionItemBasket(Id)
      this.getPaperItemBasketPreviewInfo()
    },
    // 预览页删除题目栏数据
    removePreviewItem({btnText, itemId}){
      this.$refs.itemList.DelQuestionItemBasket({type: 'item', Id: itemId})
      this.getPaperItemBasketPreviewInfo(btnText)
    },
    // 添加移出重新执行接口数据
    getPaperItemBasketPreviewInfo(btnText){
      const time = setTimeout(() => {
          if(btnText === '题目栏预览'){
            this.$refs.previewPaper.GetQuestionItemBasketPreviewInfo()
          }else {
            this.$refs.previewPaper.HomeworkItemInfoList(this.PaperId)
          }
        clearTimeout(time)
      },500)
    },
    // 同校共享
    collegialSharings(paper){
      this.$refs.collegialSharing.init(paper)
    },
    // 左侧组件方法
    getLeftScreening(obj){
      // this.leftSearch = obj
      this.GetPaperDesignInfo()
    },
    previewHandel({Id}){
      this.isDialog = true;
      this.PaperId = Id
      this.$refs.previewPaper.HomeworkItemInfoList(Id)
      // this.$refs.studentPracticeDialog.lockPreview(Id)
    },
    //跳转作业列表
    GoToNewJob(item, id) {
      // const { yearActive, semesterActive, gradeActive, chapterList } = this.leftSearch
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: {
          paperId: item.Id,
          id,
          ChapterId: item.ChapterId,
          Semester: Term,
          Year:Year,
          GradeId:GradeId,
          TextbookId: item.TextbookId
        },
      })
    },
    // 作业布置
    AssignmentLayout(id){
      if(this.$store.state.Level == 5){
        this.$refs.dialogCom.setPaperId(id)
        this.$refs.dialogCom.init()
      } else {
        this.PaperId = id
        this.AssignmentLayoutShow = true
        const timer = setTimeout(() => {
          this.$refs.PublishSet.GetTeachClass(id)
          clearTimeout(timer)
        },200)
      }
    },
    handleClose(){
      this.AssignmentLayoutShow = false
    },
    async handleSubmit(){
      const obj = {
          PaperId: this.PaperId,
          PublishTime: '',
          ShareAreaCity: 1,
      }
      // 获取已发布班级
      const date = this.$refs.PublishSet.getClassId()
      // 选中班级
      let result = this.$refs.PublishSet.handleQueryData()
      // 去除已发布班级Id
      const newArr = result.ClassId.filter(item=>!date.includes(item))
      if(newArr.length > 0){
        result.ClassId = newArr.join(',')
        // const { ClassId, IsImmediatelyPublish, ShareSet, PublishTime, ShareAreaCity } = result
        delete result.IsImmediatelyPublish
        const inputParameters = Object.assign(obj, result)
        const { data } = await this.$uwonhttp.post('/Question/Question/PaperPublishToClass', inputParameters)
        if(data.Success){
          this.handleClose()
          this.$message.success('作业布置成功')
          this.GetPaperDesignInfo()
        } else {
          this.$message.error(data.Msg)
        }
      } else {
        this.handleClose()
      }
    },
    // 开启弹窗
    OpenAddAJobPopup(item) {
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      const [ unit, ChapterId ] = Ids.length == 1 ? [ParentId[0], Ids[0]] : ['','']
      const passingParameters = {
        PaperId: item ? item.Id : '',
        Year:Year,
        Semester:Term,
        GradeId:GradeId,
        unit: item ? item.UnitId : unit,
        ChapterId: item ? item.ChapterId : ChapterId,
        TextbookId: '',
        title: item ? item.Title : '',
      }
      // console.log(passingParameters)
      // return
      this.$refs.addAJobPopup.init(passingParameters)
    },
    // 资源list的筛选条件
    levelBook(obj) {
      // const { type, val } = obj
      const { PaperType } = this.$refs.testPaperLevelSwitching.throwObj
      if(PaperType ==2){this.publishParams = ['ClassId','IsImmediatelyPublish','ShareSet']}
      if(PaperType ==3){this.publishParams = ['ClassId','IsImmediatelyPublish']}
      // this.queryText[type] = val
      this.queryText.PageIndex = 1
      this.GetPaperDesignInfo()
    },
    // 获取章节组件参数
    // TossingValuesToTheParent(ite){
    //   const idList = ite.filter(item => item.Third === null).map(item => item.ChapterId).join(',')
    //   if(this.queryText.ChapterId == idList) return
    //   this.queryText.ChapterId = idList
    //   if(ite.length === 1){
    //     const { ParentId, ChapterId } = ite[0]
    //     this.chapterIds = [ParentId, ChapterId]
    //   } else {
    //     this.chapterIds = []
    //   }
    //   this.GetPaperDesignInfo()
    // },
  // 分页
    handleCurrentChange(val) {
      this.queryText.PageIndex = val
      this.GetPaperDesignInfo()
    },
    // 作废/撤销发布/删除
    nullifyOfPubliDel(type,id){
      const titl = {
        '作废':'确定作废该份作业吗，作废后作业不可编辑与布置！',
        '删除':'确定删除该份作业吗，删除后不可恢复！',
        '撤销':'确定撤销发布该份作业吗，撤销后可对作业重新编辑并进行发布'
      }
      this.$confirm(titl[type], '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        if(type === '作废'){
          this.PaperStopPublish(id)
        } else if(type === '删除'){
          this.DelPaperDesign(id)
        } else if(type === '撤销'){
          this.PaperDesignRevocationPublish(id)
        }
      }).catch(() => {
        return false
      });
    },
    // 教师身份的撤销发布接口
    async PaperDesignRevocationPublish(paperId){
      const resPone = await this.$uwonhttp.post('/Question/Question/PaperDesignRevocationPublish', {
        paperId,
        userId: localStorage.getItem('UserId')
      })
      if(resPone.data.Success) {
        this.GetPaperDesignInfo()
        this.$message.success('试卷已撤销！')
      } else {
        this.$message.error(resPone.data.Msg)
      }
    },
    // 删除试卷
    async DelPaperDesign(paperId){
      const { data } = await this.$uwonhttp.post('/Question/Question/PaperDesignDel', {
        paperId,
        userId: localStorage.getItem('UserId')
      })
      if(data.Success) {
        this.GetPaperDesignInfo()
        this.$message.success('删除成功！')
      } else {
        this.$message.error(data.Msg)
      }
    },
    // 作废接口
    async PaperStopPublish(paperId){
      const { data } = await this.$uwonhttp.post('/Question/Question/PaperStopPublish', { paperId: paperId, userId: localStorage.getItem('UserId') })
      if(data.Success){
        this.GetPaperDesignInfo()
        this.$message.success('该试卷已被作废！')
      } else {
        this.$message.error(data.Msg)
      }
    },
    // // 教师的撤销发布
    // async RevocationPublish(item){
    //   let resPone
    //   if(this.$store.state.Level == 2){
    //     resPone = await this.$uwonhttp.post('/Question/Question/PaperDesignRevocationPublish', {
    //       paperId: item.Id,
    //       userId: localStorage.getItem('UserId')
    //     })
    //   } else {
    //     resPone = await this.$uwonhttp.post('/Question/Question/PaperDesignRevocationAssign', {
    //       paperId: item.Id,
    //     })
    //   }
    //   if(resPone.data.Success) {
    //     this.GetPaperDesignInfo()
    //     this.$message.success('撤销发布成功！')
    //   } else {
    //     this.$message.error(resPone.data.Msg)
    //   }
    // },
    // // 删除试卷
    // async DelPaperDesign(item){
    //   const { data } = await this.$uwonhttp.post('/Question/Question/PaperDesignDel', {
    //     paperId: item.Id,
    //     userId: localStorage.getItem('UserId')
    //   })
    //   if(data.Success) {
    //     this.GetPaperDesignInfo()
    //     this.$message.success('删除成功！')
    //   } else {
    //     this.$message.error(data.Msg)
    //   }
    // },
    // 获取作业资源
    async GetPaperDesignInfo(){
      // const { yearActive, semesterActive, gradeActive, chapterList } = this.leftSearch
      // const chiled = chapterList.map(item => item.ChapterId)
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      const chiled = Ids.join(',')
      this.PaperType = this.$refs.testPaperLevelSwitching.throwObj.PaperType
      // console.log(this.$refs.testPaperLevelSwitching.throwObj)
      // return false
      const data = {
        Year: Year,
        Semester: Term,
        GradeId: GradeId,
        ChapterId: chiled,
        SubjectId: localStorage.getItem('UW_SUBJECT'),
        UserId: localStorage.getItem('UserId'),
        UserRoleId: localStorage.getItem('role'),
        ...this.$refs.testPaperLevelSwitching.throwObj,
        ...this.queryText,
      }
      const { data: res } = await this.$uwonhttp.post('/Question/Question/GetPaperDesignInfo', data)
      // console.log(res)
      if (res.Success) {
        this.total = res.Data.Total
        this.resourceData = res.Data.DataInfos === null ? [] : res.Data.DataInfos
      } else {
        this.$message.error(res.Msg)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.workResources {
  width: 100%;
  display: flex;
  position: relative;
  // 试题栏
  /deep/.elPopover{
    position: absolute;
    right: 0;
    top: 10%;
    width: 40px;
    //height: 120px;
    line-height: 20px;
    padding: 16px 12px;
    font-size: 18px;
    background: #7fb99a;
    color: #FFFFFF;
    text-align: center;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    z-index: 999;
    cursor: pointer;
    .spanRed{
      width: 20px;
      border-radius: 100px;
      font-size: 12px;
      background: #ff0000;
      color: #FFFFFF;
      position: absolute;
      top: -4px;
      right: 0px;
    }
    i{
      font-size: 20px;
    }
  }
  .back_white {
    height: 86vh;
  }
  ._right {
    //width: 90%;
    flex: 1;
  }
  .btnList {
    .left {
      display: flex;
      align-items: center;
      .el-button {
        padding: 8px 16px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.1);
        color: rgba(0, 0, 0, 0.25);
      }
      .active {
        background: #6abb97 !important;
        color: #ffffff !important;
      }
    }
    .right {
      .el-select {
        width: 110px;
      }
    }
  }
  .workList {
    background-color: #ffffff;
    padding: 16px 16px 0;
    display: flex;
    flex-wrap: wrap;
    .cont {
      //width: 460px;
      width: 32.5%;
      //height: 150px;
      //height: 20vh;
      border: 1px solid #E6E6E6;
      min-height: 150px;
      background: #ffffff;
      border-radius: 4px;
      margin-right: 8px;
      margin-bottom: 10px;
      padding: 28px 18px 18px;
      position: relative;
      .top {
        display: flex;
        .titlLog {
          width: 73px;
          height: 75px;
          margin-right: 18px;
        }
        .rightTxt {
          div {
            font-weight: 500;
            font-size: 24px !important;
            color: #4d4d4d;
          }
          p {
            font-weight: 400;
            font-size: 20px !important;
            color: #919191;
            margin-top: 2px;
          }
          .tit_img{
            img{
              width: 20px;
              height: 24px;
              margin-left: 6px;
            }
          }
        }
      }
      .botm_btn {
        display: flex;
        //justify-content: space-between;
        justify-content: flex-end;
        align-items: center;
        margin-top: 6px;
        .Arrangement {
          color: #ffffff !important;
          margin-left: 10px;
        }
        .el-button {
          border-color: #6abb97;
          color: #6abb97;
          padding: 7px 14px !important;
          font-size: 20px !important;
        }
        p {
          font-weight: 400;
          font-size: 20px;
          color: #919191;
          margin-top: 2px;
        }
        > div {
          display: flex;
          align-items: center;
          .el-button {
            margin-left: 10px;
            //margin-right: 10px;
          }
        }
        .More{
          padding: 0 !important;
          border: none;
          i{
            transform: rotate(90deg);
          }
        }
      }
      .statusImg {
        position: absolute;
        top: 7px;
        right: 18px;
        img {
          width: 56px;
          height: 50px;
        }
      }
    }
  }
  .pageFenYe {
    display: flex;
    justify-content: center;
    background-color: #ffffff;
    padding: 2px 0 10px;
    /deep/.el-pager li {
      font-size: 24px;
    }
    /deep/.el-pagination__total {
      font-size: 24px;
    }
  }
  .noData {
    width: 100%;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    p {
      margin-top: 20px;
      font-size: 22px;
      color: grey;
      text-align: center;
    }
  }
  .title {
    font-size: 22px;
    font-weight: bold;
    //text-align: center;
  }
  // 右侧抽屉
  .drawerContent {
    padding: 10px 20px 20px;
    //.questionStem{
    .all {
      margin-bottom: 12px;
      border-bottom: 1px dashed #f1f1f1;
      padding-bottom: 14px;
      > span {
        background: #6abb97;
        font-size: 20px;
        color: #ffffff;
        padding: 4px 10px;
        border-radius: 4px;
      }
    }
    .txt {
      margin-top: 12px;
      h6 {
        font-size: 20px;
        color: #404040;
        margin-bottom: 6px;
      }
      div {
        font-size: 20px;
        color: #8b8b8b;
        margin-bottom: 4px;
      }
    }
    //}
  }
}
/deep/.el-dialog{
  .el-dialog__title, .el-dialog__close{
    font-size: 22px !important;
  }
}
/deep/.el-button{
  font-size: 22px !important;
}
/deep/.el-form-item__label{
  font-size: 22px !important;
}
///deep/.el-checkbox__label{
//  font-size: 20px !important;
//}
///deep/ .el-checkbox{
//  .el-checkbox__inner{
//    width: 20px;
//    height: 20px;
//    &::after{
//      left: 8px;
//      top: 3px;
//    }
//  }
//}
/deep/.el-radio__inner{
  width: 20px !important;
  height: 20px !important;
}
/deep/.el-input{
  height: 50px;
  .el-input__inner{
    height: 50px;
  }
}
</style>
<style>
.ziDingYi > .el-drawer__header {
  margin-bottom: 0 !important;
  padding: 14px 20px 14px !important;
  border-bottom: 1px solid #f1f1f1;
}
</style>