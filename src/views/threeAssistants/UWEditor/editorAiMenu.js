export default (callback) => {
  class InsertAiMenu {
    constructor() {
      this.title = 'Ai识别'
      this.tag = 'button'
    }

    getValue() {
      return ''
    }

    isActive() {
      return false
    }

    isDisabled() {
      return false
    }

    exec() {
      // 调用回调函数
      if (typeof callback === 'function') {
        callback()
      }
    }
  }

  return {
    key: `AiMenu${Math.floor(Math.random() * 10000)}`,
    factory: () => new InsertAiMenu()
  }
}