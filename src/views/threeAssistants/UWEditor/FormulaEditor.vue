<template>
  <el-dialog
    :visible.sync="visible"
    width="800px"
    title="公式编辑器"
    @close="reset"
    append-to-body
  >
    <div class="formula-editor-main">
      <!-- 工具栏（可自定义按钮和分组，示例为静态） -->
      <div class="toolbar">
        <el-button-group>
          <el-tooltip
            placement="bottom"
            effect="light"
            :visible-arrow="false"
            popper-class="formula-dropdown"
            v-model="fractionPopover"
          >
            <template #content>
              <div class="formula-dropdown-content">
                <div class="dropdown-title">分数根号符号</div>
                <div class="dropdown-symbols">
                  <span class="symbol-btn" v-for="item in fractionSymbols" :key="item.value" @click="insertSymbol(item.value)">
                    {{ item.label }}
                  </span>
                </div>
              </div>
            </template>
            <el-button
              @mouseenter="fractionPopover = true"
              @mouseleave="fractionPopover = false"
            >
              <span>＋ ÷ % √</span>
            </el-button>
          </el-tooltip>
          <!-- 其它常用符号按钮 -->
          <el-button v-for="item in commonSymbols" :key="item.label" @click="insertSymbol(item.value)">
            {{ item.label }}
          </el-button>
        </el-button-group>
        <!-- 可继续扩展分组和下拉菜单 -->
      </div>
      <div class="formula-tabs">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="常用" name="common">
            <div class="symbols">
              <span v-for="item in commonSymbols" :key="item.value" class="symbol-btn" @click="insertSymbol(item.value)">
                {{ item.label }}
              </span>
            </div>
          </el-tab-pane>
          <el-tab-pane label="最近使用" name="recent">
            <div class="symbols">
              <span v-for="item in recentSymbols" :key="item.value" class="symbol-btn" @click="insertSymbol(item.value)">
                {{ item.label }}
              </span>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="input-area">
          <el-input
            type="textarea"
            v-model="latex"
            :rows="3"
            placeholder="输入数学公式"
            @input="renderPreview"
          />
        </div>
      </div>
      <div class="preview-area">
        <div style="margin-bottom: 6px;">预览公式</div>
        <div class="preview-box" v-html="previewHtml"></div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleInsert" :disabled="!latex">插入</el-button>
    </span>
  </el-dialog>
</template>

<script>
import katex from 'katex'
import 'katex/dist/katex.min.css'

export default {
  name: 'FormulaEditor',
  props: {
    value: Boolean
  },
  data() {
    return {
      visible: false,
      latex: '',
      previewHtml: '',
      activeTab: 'common',
      fractionPopover: false,
      fractionSymbols: [
        { label: '＋', value: '+' },
        { label: '－', value: '-' },
        { label: '÷', value: '\\div' },
        { label: '%', value: '\\%' },
        { label: '/', value: '/' },
        { label: '\\frac{}{}', value: '\\frac{}{}', imgUrl: require('@/assets/keyboard/5.png') },
        { label: '\\sqrt{}', value: '\\sqrt{}', imgUrl: require('@/assets/keyboard/juxing.png') },
        { label: '\\sqrt[n]{}', value: '\\sqrt[n]{}', imgUrl: require('@/assets/keyboard/juxing.png') }
      ],
      commonSymbols: [
        { label: 'π', value: '\\pi' },
        { label: '∞', value: '\\infty' },
        { label: '∑', value: '\\sum' },
        { label: '√', value: '\\sqrt{}' },
        { label: '∫', value: '\\int' },
        { label: 'α', value: '\\alpha' },
        { label: 'β', value: '\\beta' },
        { label: 'θ', value: '\\theta' },
        { label: '≤', value: '\\leq' },
        { label: '≥', value: '\\geq' },
        { label: '≠', value: '\\neq' },
        { label: '≈', value: '\\approx' },
        // ...可继续扩展
      ],
      recentSymbols: []
    }
  },
  watch: {
    value(val) {
      this.visible = val
    },
    visible(val) {
      this.$emit('input', val)
      if (val) this.$nextTick(this.renderPreview)
    }
  },
  methods: {
    insertSymbol(symbol) {
      // 插入符号到光标处
      const textarea = this.$el.querySelector('textarea')
      if (textarea) {
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        this.latex = this.latex.slice(0, start) + symbol + this.latex.slice(end)
        this.$nextTick(() => {
          textarea.focus()
          textarea.selectionStart = textarea.selectionEnd = start + symbol.length
        })
        this.renderPreview()
      } else {
        this.latex += symbol
        this.renderPreview()
      }
      // 记录最近使用
      if (!this.recentSymbols.find(item => item.value === symbol)) {
        this.recentSymbols.unshift({ label: symbol, value: symbol })
        if (this.recentSymbols.length > 10) this.recentSymbols.pop()
      }
    },
    renderPreview() {
      try {
        this.previewHtml = katex.renderToString(this.latex, { throwOnError: false })
      } catch (e) {
        this.previewHtml = '<span style="color:#f56c6c;">公式有误</span>'
      }
    },
    handleInsert() {
      this.$emit('insert', this.latex, this.previewHtml)
      this.visible = false
      this.reset()
    },
    reset() {
      this.latex = ''
      this.previewHtml = ''
      this.activeTab = 'common'
    },
    open(latex = '') {
      this.latex = latex
      this.visible = true
      this.renderPreview()
    }
  }
}
</script>

<style scoped>
.formula-editor-main {
  min-height: 340px;
}
.toolbar {
  margin-bottom: 12px;
}
.symbols {
  margin-bottom: 8px;
}
.symbol-btn {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px 4px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  font-size: 18px;
  background: #fafbfc;
  transition: background 0.2s;
}
.symbol-btn:hover {
  background: #e6f7ff;
}
.input-area {
  margin-bottom: 12px;
}
.preview-area {
  margin-top: 10px;
}
.preview-box {
  min-height: 40px;
  background: #f7f8fa;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 20px;
}
.formula-dropdown-content {
  padding: 10px;
  font-size: 14px;
}
.dropdown-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.dropdown-symbols {
  display: flex;
  flex-wrap: wrap;
}
</style>