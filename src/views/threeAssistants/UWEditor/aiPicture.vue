<template>
  <div class="ai-editor">
    <el-upload
      v-loading="imgLoading"
      element-loading-text="识别中"
      element-loading-spinner="el-icon-loading"
      :show-file-list="false"
      drag
      action="#"
      accept=".JPG,.PNG"
      :http-request="customUploads"
      multiple>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em>开始Ai识别<br/>文件类型 *.jpg,*.png</div>
    </el-upload>
  </div>
</template>

<script>
import { notGtLt } from '@/utils/util';

export default {
  components: {
    
  },
  props: {
    
  },
  data() {
    return {
      // AI识别
      imgLoading: false
    }
  },
  mounted() {
    
  },
  computed: {
    
  },
  methods: {
    // Ai上传
    async customUploads(file) {
      const forData = new FormData()
      forData.append('file', file.file)
      const { data:res } = await this.$uwonhttp.post('/file/Upload/UploadFileByForm',forData)
      this.imgLoading = true
      this.OcrToText(res.thumbUrl)
    },
    // OCR公式图文识别接口
    async OcrToText(url) {
      const { data: res } = await this.$uwonhttp.post('/MDPen/MDPen/OcrToText', { ImgUrl: url })
      if (res.Success) {
        this.myValue = res.Data.Content
        console.log('识别结果:', this.myValue);
        this.$emit('ocrResult', this.cleaning(res.Data.Content))
        // this.myValue = this.cleaning(res.Data.Content)
        /*内部这些关键代码*/
        // let newElement = this.myEditor.getDoc().createElement('span')
        // newElement.innerHTML = this.myValue
        // // newElement.classList.add('math-tex')
        // this.checkElement(newElement)
        // // this.myEditor.insertContent(this.myValue)
        // this.myEditor.insertContent(newElement.outerHTML)
        /*关键代码*/
        this.imgLoading = false
      } else {
        this.$message.error(res.Msg);
      }
    },
    // 清楚字符串前后格式
    cleaning(result) {
      return result.replace(/\\\([^)]*\\\)/g, match => `$$${match}$$`);
      // return result.replace(/\\\(/g, '<span class="math-tex">').replace(/\\\)/g, '</span>')
    }
  },
  watch: {
    value(newValue) {
      this.myValue = newValue
    },
    myValue(newValue) {
      // console.log('change', newValue);
      let reg = /\<span class="math-tex">(.*?)<\/span>/g
      // 公式里面的>< 不转义
      this.$emit('input', notGtLt(newValue, reg))
    },
  },
}
</script>
<style lang="less" scoped>
.ai-editor{
  display: flex;
  justify-content: center;
  /deep/.el-upload{
    margin-left: 30px;
    // width: 50%;
    .el-upload-dragger{
      width: 500px;
      .el-upload__text{
        font-size: 18px;
      }
    }
  }
}
</style>