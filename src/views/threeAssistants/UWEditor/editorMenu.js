export default (callback) => {
  class InsertFormulaMenu {
    constructor(formula) {
      this.formula = formula
      this.title = '公式'
      this.tag = 'button'
    }

    getValue() {
      return ''
    }

    isActive() {
      return false
    }

    isDisabled() {
      return false
    }

    exec() {
      // 调用回调函数
      if (typeof callback === 'function') {
        callback()
      }
    }
  }

  return {
    key: `FormulaMenu${Math.floor(Math.random() * 10000)}`,
    factory: () => new InsertFormulaMenu()
  }
}