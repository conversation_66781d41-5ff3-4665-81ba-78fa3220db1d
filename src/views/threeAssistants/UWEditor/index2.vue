<template>
  <div class="public-textarea">
    <div v-show="!editState" class="preview-view" :style="{ minHeight: height + 'px' }" @click="editorStateChange">
      <div v-katex v-html="newContent" class="html-content"></div>
    </div>
    <div v-show="editState" class="top-preview-view" :style="{ bottom: height + 'px' }" @click="editorStateChange">
      <div v-katex v-html="newContent" class="html-content"></div>
    </div>
    <div v-show="editState" class="editor-container" :style="{ minHeight: height + 'px' }">
      <Toolbar
        :editor="editor"
        :default-config="toolbarConfig"
        :mode="mode"
        style="border-bottom: 1px solid #ccc"
        v-bind="$attrs">
      </Toolbar>
      <Editor
        v-model="textHtml"
        :default-config="editorConfig"
        :mode="mode"
        style="flex: 1; height: 0; overflow: auto"
        @onCreated="onCreated"
        @onChange="onEditorChange"
        @onFocus="editorFocus"
        @onBlur="editorBlur"
      />
    </div>
    <FormulaEditor ref="formulaEditor" @insert="handleInsertFormula" />
    <!--  AI识别  -->
    <el-dialog
      class="math-dialog"
      title="文本提取工具"
      :visible.sync="aiDialogVisible"
      :append-to-body="true"
      width="42%">
      <aiPicture @ocrResult="aiDialogVisible=false;(e) => handleInsertText(e)"></aiPicture>
    </el-dialog>
  </div>
</template>

<script>
import FormulaEditor from './FormulaEditor.vue'
import createMenuConf from './editorMenu.js'
import createAiMenu from './editorAiMenu.js'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { Boot } from '@wangeditor/editor'
import formulaModule from '@wangeditor/plugin-formula'
import katex from 'katex'
import 'katex/dist/katex.min.css'
import aiPicture from './aiPicture.vue'

Boot.registerModule(formulaModule)

export default {
  name: 'questionStem',
  model:{
    prop: 'content',
  },
  props:{
    content:{
      type:String,
      default:''
    },
    height:{
      type:Number,
      default:40
    },
    idx:{
      type:[Number,String],
      default:0
    },
    itemId:{
      type:Number,
      default:0
    }
  },
  components: {
    Editor,
    Toolbar,
    FormulaEditor,
    aiPicture
  },
  watch: {
    content(val) {
      this.newContent = val?.replace(/\$\$([^$]*)\$\$/g, (match, p1) => `<span class="math-tex">\\(${p1}\\)</span>`);
      this.textHtml = val
    }
  },
  data() {
    return {
      editState: false,
      aiDialogVisible: false,
      newContent: this.content,
      // 富文本
      editor: null,
      textHtml: '',
      toolbarConfig: {
        insertKeys: {
          index: 0, // 自定义
          keys: [
            // 'FormulaMenu',
            // 'AiMenu'
            'insertFormula', // “插入公式”菜单
            // 'editFormula' // “编辑公式”菜单
          ],
        },
        excludeKeys: ['insertVideo', 'insertImage', 'bgColor', 'blockquote', 'fontFamily', 'lineHeight', 'bulletedList', 'numberedList', 'todo', 'group-justify', 'group-indent', 'insertLink', 'insertTable', 'codeBlock', 'divider', 'group-video']
      },
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {
            customUpload: this.onUploadImg
          }
        }
      },
      mode: 'default', // or 'simple'
      showFormulaDialog: false
    }
  },
  created() {
    
  },
  mounted() {
    this.renderMathTex();
  },
  updated() {
    this.renderMathTex();
  },
  methods:{
    openingRichText(type,content){
      this.$refs.editDialog.init(type,content)
    },
    getEditData(data) {
      this.newContent = data
      this.$emit('update:content', data)
    },
    renderMathTex() {
      const elements = this.$el.querySelectorAll('span[data-math-tex="2"]');
      elements.forEach(el => {
        const latex = el.textContent.replace(/^\\\(|\\\)$/g, '');
        try {
          katex.render(latex, el, { throwOnError: false });
        } catch (e) {
          el.innerHTML = '<span style="color:#f56c6c;">公式有误</span>';
        }
      });
    },
    handleFormulaMenuExec() {
      if (this.$refs.formulaEditor) {
        this.$refs.formulaEditor.open()
      }
    },
    editorStateChange() {
      this.editState = !this.editState;
      if (this.editState) {
        this.$nextTick(() => {
          this.editor.focus();
        });
      } else {
        this.renderMathTex();
      }
      this.$emit('editorStateChange', this.editState, this.idx);
    },
    /**
     * 富文本编辑
     */
    onCreated(editor) {
      this.editor = Object.seal(editor);
    },
    onEditorChange(editor) {
      // this.newContent = this.textHtml.replace(/\\\([^)]*\\\)/g, match => `<span class="math-tex">${match}</span>`);
      this.newContent = this.textHtml.replace(/\$\$([^$]*)\$\$/g, (match, p1) => `<span class="math-tex">\\(${p1}\\)</span>`);
      // this.textHtml = this.textHtml.replace(/\\\(([^)]*)\\\)/g, (match, p1) => `$$${p1}$$`);
    },
    editorFocus() {
      // this.textHtml = this.textHtml.replace(/\\\(([^)]*)\\\)/g, (match, p1) => `$$${p1}$$`);
    },
    editorBlur() {
      // this.editState = false;
      // this.newContent = this.textHtml.replace(/\\\([^)]*\\\)/g, match => `<span class="math-tex">${match}</span>`);
      this.newContent = this.textHtml.replace(/\$\$([^$]*)\$\$/g, (match, p1) => `<span class="math-tex">\\(${p1}\\)</span>`).replace(/^<p[^>]*>|<\/p>$/g, '');
      this.$emit('editorStateChange', this.editState, this.idx);
      this.$emit('updateEditItem', this.newContent=='<br>' ? '' : this.newContent, this.idx);
    },
    async onUploadImg(file, insertFn) {
      const forData = new FormData()
      forData.append('file', file)
      let resData = await this.$uwonhttp.post('/file/Upload/UploadFileByForm', forData)
      if (resData.status === 200) {
        let image = new Image()
        image.src = resData.data.thumbUrl
        setTimeout(() => {
          this.editor.blur()
        }, 500)
        image.onload = () => {
          insertFn(resData.data.thumbUrl, 'filename', resData.data.thumbUrl)
          this.$nextTick(() => {
            this.editState = true;
            this.editor.focus()
          })
        }
      }
    },
    // 插入公式
    handleInsertFormula(latex) {
      // this.textHtml = `<span data-math-tex="1">\\(${latex}\\)</span>`
      if (this.editor) {
        this.editState = true;
        this.editor.focus();
        const html = `<span class="math-tex">$$${latex}$$</span>`;
        // const html = `<span data-math-tex="2">\\(${latex}\\)</span>`;
        setTimeout(() => {
          this.editor.dangerouslyInsertHtml(html);
        }, 0);
      }
    },
    // 插入text
    handleInsertText(text) {
      // this.textHtml = `<span data-math-tex="1">\\(${latex}\\)</span>`
      if (this.editor) {
        this.editState = true;
        this.editor.focus();
        const html = `<span class="math-tex">${text}</span>`;
        // const html = `<span data-math-tex="2">\\(${latex}\\)</span>`;
        setTimeout(() => {
          this.editor.dangerouslyInsertHtml(html);
        }, 0);
      }
    }
  }
}
</script>

<style lang="less" scoped>
.public-textarea{
  position: relative;
}
.preview-view{
  border: 1px solid #DCDCDCFF;
  border-radius: 5px;
  padding: 10px;
  // min-height: 200px;
  // height: 100%;
  font-size: 20px;
}
.top-preview-view{
  position: absolute;
  padding: 10px;
  // bottom: 250px;
  left: 0;
  right: 0;
  border: 1px solid #DCDCDCFF;
  border-radius: 5px;
  background: #fff;
  font-size: 20px;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.editor-container {
    // height: 300px;
    // height: 100%;
    border: 1px solid rgb(204, 204, 204);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    &.none {
      border: none;
      flex: auto;
      height: auto;
    }
    // ::v-deep .w-e-scroll {
    //   overflow-y: inherit !important;
    // }
  }
</style>
<style src="@wangeditor/editor/dist/css/style.css" />