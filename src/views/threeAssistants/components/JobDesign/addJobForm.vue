<template>
  <div class="addJobForm">
    <el-form :inline="true" class="demo-form-inline form" :model="form">
      <el-form-item label="作业名称">
        <el-input v-model="form.PaperName" placeholder="请输入"></el-input>
      </el-form-item>
      <div class="flex">
        <el-form-item label="单元">
          <el-select :disabled="disabled" v-model="unit" popper-class="picker-popper" placeholder="请选择" @change="switchingUnit">
            <el-option class="font_size_22" v-for="(item, ind) in unitList" :key="ind" :label="item.text" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="章节">
          <el-select :disabled="disabled" v-model="form.ChapterId" popper-class="picker-popper">
            <el-option class="font_size_22" v-for="(item,ind) in chapterList" :key="ind" :label="item.text" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <el-form-item label="出卷人">
        <el-input v-model="form.AuthorName" placeholder="请输入出卷人"></el-input>
      </el-form-item>
      <el-form-item label="类型" v-show="['1','21','3','23','37','100'].includes(subject)">
        <el-radio-group :disabled="disabled" v-model="form.IsComposition">
          <el-radio class="font_size_22" :label="0">日常作业</el-radio>
          <el-radio class="font_size_22" :label="1">作文专项提高</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="教材" v-show="textBook.length > 1">
        <el-select v-model="form.TextbookId" popper-class="picker-popper" disabled placeholder="请选择">
          <el-option class="font_size_22" v-for="(item, ind) in textBook" :key="ind" :label="item.text" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <div class="flex">
        <el-form-item v-if="$store.state.LoginSource == 1 && form.IsComposition == 0" label="设置分数">
          <el-select v-model="form.IsSetScore" popper-class="picker-popper" placeholder="请选择">
            <el-option class="font_size_22" label="是" :value="1"></el-option>
            <el-option class="font_size_22" label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-show="$store.state.LoginSource == 1 && form.IsSetScore == 1" label="试卷总分">
          <el-input v-model="form.TotalScore" type="number" placeholder="请输入"></el-input>
          分
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import { Session } from '@/utils/storage.js'
export default {
  name: 'AddAJobPopup',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    QuestionList: {
      type: Array,
      default(){
        return []
      }
    }
  },
  data() {
    return {
      GradeList: [],
      Academicyear: [],
      value: [],
      // newOptions: [],
      unit: '',
      unitList: [],
      chapterList: [],
      region: '',
      // userName: '',
      paperId: '',
      form: {
        Year: '',
        Semester: '',
        GradeId: '',
        ChapterId: '',
        PaperName: '',
        UserId: '',
        UserRoleId: localStorage.getItem('role'),
        SubjectId: localStorage.getItem('UW_SUBJECT'),
        PaperType: 1,
        TextbookId: '',
        IsSetScore: 0,
        TotalScore: 100,
        AuthorName: '',
        IsComposition: 0,
      },
      // 教材
      textBook: [],
      testQuestionBankIds: [], // 题库Ids,
      NewJobType: '', // 生成作业业务类型线
    }
  },
  computed: {
    // 获取用户角色
    subject() {
      return localStorage.getItem('UW_SUBJECT')
    }
  },
  mounted() {
    // this.init(this.$route.query, this.NewJobType)
  },
  methods: {
    async init(passingParameters, NewJobType) {
      this.NewJobType = NewJobType
      const { PaperId, Year, Semester, GradeId, unit, ChapterId, TextbookId, title} = passingParameters
      // console.log(GradeId)
      this.form.Year = Year // 学年
      this.form.Semester = Semester // 学期
      this.form.GradeId = GradeId // 年级
      this.unit = unit // 单元
      this.form.ChapterId = ChapterId // 章节
      this.form.TextbookId = TextbookId // 教材id
      this.form.PaperName = title
      this.paperId = PaperId
      await this.GetGradeList() // 获取年级列表
      await this.GetAcademicyear() // 获取学年列表
      await this.GetSubjectUnit() // 获取单元
      await this.GetSubjectChapter(this.unit) // 获取章节
      await this.GetTextbookVersion() // 获取教材
      const userInfo = Session.get('userInfo')
      this.form.AuthorName = userInfo.RealName
    },
    // 作文专项题的验证
    // IsComposition(e){
    //   if(e === 1){
    //     // console.log(this.testQuestionBankIds)
    //     const isCompositionQuestion = this.QuestionList.some(
    //       res => res.Id === this.testQuestionBankIds[0] && res.QuestionTypeId === '26'
    //     );
    //     if(this.testQuestionBankIds.length > 1 || !isCompositionQuestion){
    //       this.$message.error('作文专项题只能添加1道作文题！')
    //       return false
    //     }
    //   }
    // },
    setYear(val){
      this.form.Year = val
      this.GetGradeList()
      // this.GetSubjectUnit()
    },
    setBankIds(val){
      this.testQuestionBankIds = val
    },
    async submit() {
      // 生成试卷Id
      const { ChapterId, PaperName, UserId, Year, GradeId, PaperType, Semester, TextbookId, AuthorName, IsSetScore, TotalScore, IsComposition } = this.form;
      const paperId = this.paperId

      if (!ChapterId || !PaperName) {
        this.$message.warning('请检查章节和作业名称是否填写完整！');
        return false;
      }
      // 判断是否为作文专项题
      if (IsComposition === 1 && this.testQuestionBankIds.length > 0) {
        if (this.testQuestionBankIds.length > 1) {
          this.$message.error('作文专项题只能添加1道作文题！');
          return false;
        }

        const isCompositionQuestion = this.QuestionList.some(
          res => res.Id === this.testQuestionBankIds[0] && res.QuestionTypeId === '26'
        );

        if (!isCompositionQuestion) {
          this.$message.error('作文专项题只能添加作文题！');
          return false;
        }
      }

      // 根据路由判断生成作业要走哪条业务线
      const path = this.$route.path === '/Teacher/My_Task/HighWrong/HighWrong'
      if(path){
        this.$emit('setErrorPaperList', this.form)
        this.handleClose()
        return false
      }

      try {
        let response;
        if (paperId) {
          // 试卷引用
          response = await this.$uwonhttp.post('/Question/Question/PaperCite', {
            PaperId: paperId,
            UserId,
            Year,
            Title: PaperName,
            GradeId,
            ChapterId,
            PaperType,
            AuthorName,
            IsSetScore,
            TotalScore,
            UserRoleId: localStorage.getItem('role')
          });
        } else {
          // 生成试卷
          response = await this.$uwonhttp.post('/Question/Question/AssignHomework', this.form);
        }
        if (response.data.Success) {
          const urlObj = {
            paperId: response.data.Data,
            id: paperId ? 102 : 100,
            ChapterId,
            GradeId,
            Semester,
            Year,
            TextbookId,
            NewJobType: this.NewJobType,
            IsComposition
          }
          if(IsSetScore == 1) {
            // 记录入口设置的总分值
            Session.set('TotalScore', TotalScore)
          }
          return this.testQuestionBankIds.length > 0 ? {...urlObj, testQuestionBankIds: this.testQuestionBankIds.join(',')} : urlObj
          // this.$router.push({
          //   path: '/threeAssistants/JobDesign/NewJob',
          //   query: this.testQuestionBankIds.length > 0 ? {...urlObj, testQuestionBankIds: this.testQuestionBankIds.join(',')} : urlObj,
          // });
        } else {
          this.$message.error(response.data.Msg);
        }
      } catch (error) {
        this.$message.error('提交过程中发生错误，请稍后再试。');
      } finally {
        // this.handleClose();
      }
    },
    handleClose() {
      console.log('handleClose');
      Object.assign(this.$data, this.$options.data.call(this))
    },
    // 获取年级
    async GetGradeList() {
      const { data: res } = await this.$uwonhttp.get(`/Class/TeacherClassManager/GetGradeList?year=${this.form.Year}`)
      if(res.Success) {
        this.GradeList = res.Data
        if(res.Data.length == 0){
          this.form.GradeId = ''
        }
        // else {
        //   this.form.GradeId = res.Data[0].Id
        // }
      }else {
        this.$message.error(res.Msg)
      }
    },
    // 获取学年
    async GetAcademicyear() {
      const { data: res } = await this.$uwonhttp.get('/Question/Question/GetAcademicyear')
      res.Success ? (this.Academicyear = res.Data) : this.$message.error(res.Msg)
    },
    // 获取单元
    async GetSubjectUnit(unit) {
      // 暂时忘记为何添加这块代码先注调，可判断为undefind
      const { Year, Semester, GradeId } = this.form
      const { data } = await this.$uwonhttp.get(
        `/Chapter/Chapter/GetSubjectUnit?year=${Year}&term=${Semester}&grade=${GradeId}`
      )
      if(data.Success){
        this.unitList = data.Data
        // if (unit){
        //   this.GetSubjectChapter()
        // }
      } else {
        this.$message.error(data.Msg)
      }
    },
    switchingUnit(val){
      this.form.ChapterId = ''
      this.chapterList = []
      this.GetSubjectChapter(val)
    },
    // 获取章节
    async GetSubjectChapter(unitId) {
      const { data } = await this.$uwonhttp.get(
        `/Chapter/Chapter/GetSubjectChapter?unitId=${unitId}`
      )
      if(data.Success){
        this.chapterList = data.Data
      } else {
        this.$message.error(data.Msg)
      }
     // console.log(data)
    },
    // 获取教材
    async GetTextbookVersion(){
      const { data:res } = await this.$uwonhttp.get('/Question/Question/GetTextbookVersion')
      if(res.Success){
        // this.textBookShowHide = res.Data.length > 1 ? true : false
        this.form.TextbookId = res.Data[0].value
        // 往数组头部添加对象
        this.textBook = res.Data
      } else {
        this.$message.error(res.Msg)
      }
    },
  },
}
</script>

<style lang="less" scoped>
.addJobForm{
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.form {
  display: flex;
  flex-direction: column;
  /deep/.el-form-item__label{
    position: relative;
    font-size: var(--font16-22);
    &::before {
      content: '';
      width: 3px;
      height: 15px;
      background: #7fb99a;
      border-radius: 4px;
      margin-right: 10px;
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  /deep/.el-select {
    width: 300px;
    //font-size: 22px !important;
    font-size: var(--font16-22);
  }
  /deep/.el-input {
    width: 300px;
    //font-size: 22px !important;
    font-size: var(--font16-22);
  }
  /deep/.el-option{
  //font-size: 22px !important;
    font-size: var(--font16-22);
  }
  /deep/.el-form-item{
    //font-size: 22px !important;
    margin-left: 30px !important;
    font-size: var(--font16-22);
    // width: 600px;
  }
  /deep/.el-form-item__label{
    text-align: left !important;
    width: 100px !important;
  }
}
</style>
<style lang="less">
//.picker-popper{
//  .el-select-dropdown__item{
//    height: 40px;
//  }
//}
</style>