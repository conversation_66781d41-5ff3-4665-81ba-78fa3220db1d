<template>
  <div>
<!--  单项/多项  -->
    <div class="selectMultipleOptions" v-show="[2,10].includes(typeId)">
      <div v-for="(item,ind) in newOption" :key="ind" style="margin: 2px 0;">
        <div class="optionList">
          <el-radio v-if="typeId === 2" v-model="rade" :label="item.Option" @input="radioBtn(ind)">{{ item.Option }}</el-radio>
          <el-checkbox v-else v-model="item.check" @change="check(ind)">{{ item.Option }}</el-checkbox>
          <!-- <div class="devInput" contenteditable="true" v-html="item.Content" v-katex @click="openDialog(ind, item.Content)"></div> -->
          <UWEditor
            class="uweditor-view"
            :height="item.edit ? 100 : 40"
            :idx="ind"
            :content="item.Content"
            @updateEditItem="getEditData"
            @editorStateChange="editorStateChange"></UWEditor>
          <el-button type="text" v-show="!item.check && typeId === 2" :icon="item.err ? 'el-icon-remove' : 'el-icon-circle-plus'" @click="addDel(ind)">
            {{ item.err ? '删除' : '添加' }}错因
          </el-button>
        </div>
        <public-error v-show="item.err && typeId === 2" titleName="错因" :err.sync="item.errList" :CuoYinList="CuoYinList"></public-error>
      </div>
      <div class="btn">
        <el-button type="primary" size="small" icon="el-icon-circle-plus" @click="add">添加新选项</el-button>
        <el-button type="danger" size="small" icon="el-icon-remove" @click="del">删除选项</el-button>
      </div>
    </div>
    <!-- <edit-dialog ref="editDialog" @updateEditItem="getEditData"></edit-dialog> -->
  </div>
</template>

<script>
import UWEditor from '@/views/threeAssistants/UWEditor'
import EditDialog from '@/views/Paper/Exam_Paper/Components/EditDialog'
import publicError from '@/views/threeAssistants/AddANewTopic/components/module/publicError'
export default {
  name: 'threeBasicsItem',
  props: ['option', 'answer', 'errors' , 'typeId', 'CuoYinList'],
  components:{
    EditDialog,
    publicError,
    UWEditor
  },
  watch:{
    answer(val){
      // console.log('########################')
      this.rade = val
    },
  },
  data() {
    return {
      oneMoreOptions: Object.freeze(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N']),
      dialogInd: null,
      rade: '',
      newOption: [],
    }
  },
  methods:{
    addEditList(arr){
      // console.log('$$$$$$$$$$$$$$$$$$$$$$$$$$')
      this.rade = this.answer
      if (!arr){
        this.newOption = [
          {check: false, err: false, errList: [], Option: this.oneMoreOptions[0], Content: ''},
          {check: false, err: false, errList: [], Option: this.oneMoreOptions[1], Content: ''},
          {check: false, err: false, errList: [], Option: this.oneMoreOptions[2], Content: ''},
          {check: false, err: false, errList: [], Option: this.oneMoreOptions[3], Content: ''}
        ]
      } else {
        const answerSet = new Set(this.answer.split('|'));
          arr.forEach(res => {
            if(answerSet.size === 0 || answerSet.has('')){
              res.check = false
              res.errList = []
              res.err = false
            } else {
              res.check = answerSet.has(res.Option);
              // res.err = !res.check;
              res.err = this.getOption(res.Option).length > 0;
              res.errList = res.check ? [] : this.getOption(res.Option);
            }
          });
        // console.log(arr)
        this.newOption = arr
      }
    },
    add(){
      const ind = this.newOption.length
      this.newOption.push({check: false, err: false, errList:[], Option: this.oneMoreOptions[ind], Content: ''})
    },
    del(){
      this.newOption.pop()
    },
    addDel(ind){
      const option = this.newOption[ind];
      option.err = !option.err; // 翻转 err 的值
      if (!option.err) {
        option.errList = []; // 如果 err 为 false，则清空 errList
      }
      this.$forceUpdate()
    },
    editorStateChange(e, idx) {
      this.dialogInd = idx
      this.newOption[this.dialogInd].edit = e
      this.$forceUpdate()
    },
    getEditData(val, idx) {
      this.dialogInd = idx
      this.newOption[this.dialogInd].Content = val
    },
    openDialog(ind, content){
      this.dialogInd = ind
      this.$refs.editDialog?.init('答案', content)
    },
    // 单选框
    radioBtn(ind){
      this.newOption[ind].check = true;
      this.newOption[ind].err = false;
      this.newOption[ind].errList = [];

      this.newOption.forEach((ite, ine) => {
        if (ine !== ind) {
          ite.check = false;
        }
      });
    },
    // 复选框
    check(ind){
      const option = this.newOption[ind];
      this.newOption.splice(ind, 1, option)
    },
    getlist(){
      return this.clearLocal(this.newOption)
    },
    // 数据清理
    clearLocal(arr) {
      let options = [];
      let errors = [];
      let answer = this.typeId === 2 ? "" : []
      arr.forEach(({ Content, Option, check, err, errList }) => {
        options.push({ Content, Option });
        if(check){
          if(this.typeId === 2){
            answer = Option;
          } else {
            answer.push(Option)
          }
        }
        if (err && errList.length > 0) {
          // 使用展开运算符直接将错误信息合并进总错误数组
          errors.push(...errList.map(ite => ({ Content: Option, ErrorId: ite })));
        }
      });
      // 如果 answer 需要在组件外部使用，确保它被正确地定义和暴露
      return {
        Answer: answer,
        Options: options,
        Errors: errors
      }
    },
    getOption(opt) {
      console.log(opt)
      return this.errors.filter(res => res.Content === opt).map(res => res.ErrorId);
    }
  }
}
</script>

<style lang="less" scoped>
.selectMultipleOptions{
  .btn{
    margin-top: 10px;
  }
}
.optionList{
  display: flex;
  align-items: center;
  justify-content: left;
  //font-size: 22px;
  font-size: var(--font16-22);
  .el-radio{
    margin-right: 0 !important;
  }
  .devInput{
    width: 90%;
    //width: 540px;
    padding: 4px 10px;
    border-radius: 4px;
    border: 1px solid gainsboro;
    margin: 4px 0 4px 6px;
  }
  .uweditor-view{
    width: 90%;
    //width: 540px;
    margin: 4px 0 4px 6px;
  }
  .el-button{
    color: #7FB99A;
    margin-left: 4px;
  }
}
</style>