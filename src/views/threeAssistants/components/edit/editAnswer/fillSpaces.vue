<template>
  <div class="answer">
    <ul class="ul1">
      <li v-for="(item, ind) in answer" :key="ind">
        <div class="newAnswer">
          <p>空{{ ind + 1 }}</p>
          <div>
            <!-- <div v-if="item.Answer != ''" class="Txt" v-html="item.Answer" v-katex></div>
            <el-input v-else v-model="item.Answer" placeholder="请输入"></el-input> -->
            <!-- <div class="Txt" v-html="item.Answer" v-katex @click="onClickHandel(ind, item.Answer)"></div> -->
            <UWEditor
              class="Txt"
              :height="item.edit ? 100 : 40"
              :idx="ind"
              :content="item.Answer"
              @updateEditItem="getEditData"
              @editorStateChange="editorStateChange"></UWEditor>
            <el-button type="text" v-show="item.ErrorInfo.length <= 0" icon="el-icon-circle-plus" @click="addError(ind)">添加错因</el-button>
          </div>
        </div>
        <div class="errors">
          <div class="errList" v-for="(ite,i) in item.ErrorInfo"  :key="ind">
            <div class="cuowu">
              <p>错误答案{{i+1}}</p>
              <el-input v-model="ite.ErrorAnswer" placeholder="请输入"></el-input>
            </div>
            <public-error title-name="错因" :err.sync="ite.ErrorId" :CuoYinList="CuoYinList"></public-error>
            <div class="addDel">
              <i class="el-icon-circle-plus" @click="addError(ind)"></i>
              <i class="el-icon-remove" @click="delError(ind,i)"></i>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <el-button class="addingSynonymy" type="primary" size="medium" icon="el-icon-circle-plus" @click="addAnswer">添加同义答案</el-button>
    <ul class="ul2" v-show="cloneAnswer.length">
      <li v-for="(item, ind) in cloneAnswer" :key="ind">
        <p class="TY font_size_22">同义{{ ind+1 }}</p>
        <div class="cent font_size_22">
          <div class="cloneList" v-for="(ite, i) in item" :key="i + 'blank'" style="position: relative;">
            <p>空{{ i + 1 }}</p>
            <UWEditor
              class="Txt"
              :height="ite.edit ? 100 : 40"
              :idx="ind + ',' + i"
              :content="ite.value"
              @updateEditItem="getVal"
              @editorStateChange="getFocus"></UWEditor>
            <!-- <div class="bor"> -->
              <!-- <addlineitem class="input" :value="ite" :data-index="ind + ',' + i" @blur="getVal" @focus="getFocus"></addlineitem> -->
            <!-- </div> -->
          </div>
        </div>
        <div class="btn">
          <i class="el-icon-plus add" @click="addAnswer"></i>
          <i class="el-icon-minus rem" @click="remove(ind)"></i>
        </div>
      </li>
    </ul>
    <!-- <edit-dialog ref="editDialog" @updateEditItem="getEditData"></edit-dialog> -->
  </div>
</template>

<script>
import UWEditor from '@/views/threeAssistants/UWEditor'
import addlineitem from '@/components/Mathquill/addlineitem'
import EditDialog from '@/views/Paper/Exam_Paper/Components/EditDialog'
import publicError from '@/views/threeAssistants/AddANewTopic/components/module/publicError'
export default {
  name: 'fillSpaces',
  props:['title', 'OtherAnswer','RelevanceErrors', 'Answer','CuoYinList'],
  data() {
    return {
      answer: [],
      cloneAnswer: [],
      currentInputVal: '',
      currentInputIndex: '',
      dialogInd: 0, // 填空题弹窗答案下标
    }
  },
  watch:{
    title:{
      handler(val){
        this.answer = []
        this.dealWithItemTitle(val)
      },
      immediate: true
    },
    OtherAnswer: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.cloneAnswer = val.map(item => {
              return item.split('|').map(ite => {
                return { value: ite, edit: false }
              })
            })
          })
        }
      },
      immediate: true
    },
    RelevanceErrors:{
      handler(val){
        if(!val.length) return
        this.$nextTick(() => {
          this.answer = val.map(item => {
            return {
              Answer: item.Answer,
              ErrorInfo: item.ErrorInfo.map(ite => {
                return {
                  ErrorAnswer: ite.ErrorAnswer,
                  ErrorId: ite.ErrorId.split(',')
                }
              })
            }
          })
        })
      },
      immediate: true
    },
    Answer:{
      handler(val,newVal){
        let answerList
        newVal ? answerList = newVal.split('|') : answerList = val.split('|')
        // if(newVal){
        //   // console.log('新值')
        //   answerList = newVal.split('|')
        // } else {
        //   // console.log('旧知')
        //   answerList = val.split('|')
        // }
        if(val !== '' && this.RelevanceErrors.length <= 0){
          this.$nextTick(() => {
            this.answer = answerList.map(ans => {
              return {
                Answer: ans,
                ErrorInfo: []
              }
            });
          })
        }
      },
      immediate: true
    }
  },
  components:{
    addlineitem,
    publicError,
    EditDialog,
    UWEditor
  },
  methods:{
    onClickHandel(ind, content){
      this.dialogInd = ind
      this.$refs.editDialog.init('答案', content)
      // console.log('显示弹窗')
    },
    editorStateChange(e, idx) {
      this.dialogInd = idx
      this.answer[this.dialogInd].edit = e
      this.$forceUpdate()
    },
    getEditData(val, idx) {
      this.dialogInd = idx
      this.answer[this.dialogInd].Answer = val
    },
    // getEditData(val){
    //   this.answer[this.dialogInd].Answer = val
    // },
    addAnswer() {
      let arr = this.answer.map(item => {
        // 映射为新的数据格式
        return { value: item.Answer, edit: false }; 
      });
      this.cloneAnswer.push(arr);
    },
    remove(i) {
      this.cloneAnswer.splice(i,1)
    },
    // add(i){
    //   let arr = []
    //   arr = this.answer.map(item => {
    //     return item.Answer
    //   })
    //   this.cloneAnswer.splice(i,0,arr)
    // },
    //获取焦点
    getFocus(e, idx) {
      // console.log(e+'getfocus')
      // this.currentInputVal = e.target.innerHTML
      // this.currentInputIndex = idx
      // e.target.innerHTML = ''
      let ind = idx.split(',')
      let i_x = parseInt(ind[0]) //答案几
      let i_y = parseInt(ind[1]) //第几空
      this.cloneAnswer[i_x][i_y].edit = e
      this.$forceUpdate()
    },
    // 失焦后获取值
    getVal(e, idx) {
      let ind = idx.split(',')
      let i_x = parseInt(ind[0]) //答案几
      let i_y = parseInt(ind[1]) //第几空
      if (this.currentInputIndex == idx && e == '') {
        // e.target.innerHTML = this.currentInputVal
        return
      }
      // 更新 value 属性
      this.cloneAnswer[i_x][i_y].value = e.replace(/[\r\n]/g, '');
    },
    // 填空题答案处理
    dealWithItemTitle(title){
      const regx = /（.*?）/g
      let regxList = title.match(regx)
      if (regxList) {
        this.$nextTick(() => {
          this.answer = regxList.map(match => ({
            Answer: match.replace(/\（|\）/g, '').trim(),
            ErrorInfo: []
          }));
        })
      }
    },
    // 增加错因
    addError(ind){
      this.answer[ind].ErrorInfo.push({
        ErrorAnswer: '',
        ErrorId: []
      })
    },
    // 删除错因
    delError(ind, i){
      this.answer[ind].ErrorInfo.splice(i,1)
    },
    // 获取关联错因
    getRelevanceErrors(){
      return this.answer.map(item => {
        return {
          Answer: item.Answer,
          ErrorInfo: item.ErrorInfo.map(ite => {
            return {
              ErrorAnswer: ite.ErrorAnswer,
              ErrorId: ite.ErrorId.join(',')
            }
          })
        }
      })
    },
    // 获取答案
    getAnswer(){
      return this.answer.map(item => item.Answer);
    },
    // 获取同意答案
    getOtherAnswer(){
      return this.cloneAnswer.map(item => {
        // 提取 value 属性再拼接
        return item.map(ite => ite.value).join('|'); 
      });
    },

  }
}
</script>

<style lang="less" scoped>
.answer{
  .ul1{
    //display: flex;
    //align-items: center;
    //flex-wrap: wrap;
    //border: 1px dotted #DCDCDCFF;
    //border-radius: 4px;
    //padding: 8px 10px;
    li{
      display: flex;
      min-width: 100px;
      margin: 0 10px 8px 0;
      .newAnswer{
        .Txt{
          // max-width: 100px;
          // min-width: 100px;
          // min-height: 34px;
          width: 600px;
          // div内文本内容换行
          word-break: break-all;
          white-space: pre-wrap;
        }
        p{
          font-weight: normal;
          font-size: 16px;
          color: #000000;
          margin-bottom: 6px;
        }
        >div{
          display: flex;
          align-items: center;
          >div{
            width: 100%;
            padding: 4px 10px;
            text-align: center;
            border: 1px solid #00000069;
            border-radius: 4px;
            font-size: 16px;
          }
        }
        .el-button{
          color: #7FB99A;
          margin-left: 10px;
        }
      }
      .errors{
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
      .errList{
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        .cuowu{
          display: flex;
          align-items: center;
          margin-right: 6px;
          p{
            min-width: 84px;
            font-weight: normal;
            font-size: 16px;
          }
        }
        .addDel{
          i{
            font-size: 16px;
            margin-left: 6px;
            color: #7FB99A;
            cursor: pointer;
          }
        }
        /deep/.el-select{
          width: 260px !important;
        }
      }
    }
  }
  .addingSynonymy{
    margin: 10px 0;
  }
  .ul2{
    li{
      display: flex;
      align-items: center;
      .TY{
        font-size: 16px;
      }
      .cent{
        width: 86%;
        min-height: 60px;
        border: 1px dotted #DCDCDCFF;
        padding: 8px 10px;
        border-radius: 4px;
        margin: 0 10px;
        // display: flex;
        // flex-wrap: wrap;
        .cloneList{
          min-width: 140px;
          margin-right: 10px;
          p{
            font-size: 16px;
          }
          .bor{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 10px;
            border: 1px solid #DCDCDCFF;
            border-radius: 4px;
            .input {
              min-width: 100px;
              max-width: 700px;
              border: 1px solid rgb(230, 230, 230);
              height: 40px;
              line-height: 40px;
              font-size: 16px;
              overflow: hidden;
            }
            //div{
            //  font-size: 16px;
            //}
            //i{
            //  font-size: 18px;
            //}
          }
          p{
            text-align: center;
          }
        }
      }
      .btn{
        i{
          font-size: 16px;
          padding: 2px;
          border-radius: 4px;
          display: block;
          margin: 10px 0;
          background-color: #7FB99A;
          color: #FFFFFF;
        }
        //.add{
        //  color: #409EFF;
        //  border: 1px solid #409EFF;
        //}
        //.rem{
        //  color: #dd0000;
        //  border: 1px solid #dd0000;
        //}
      }
    }
  }
}
</style>