<template>
  <div class="divGal">
    <el-popover
      placement="bottom"
      width="420"
      :disabled="popoverDis"
      trigger="click">
      <!-- 弹窗选择区 -->
      <div class="cardList">
        <div class="academicYear">
          <div class="txt">学年：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in yearList" :key="item" :class="{active: item == throwObj.yearActive}" @click="allClick('year',item)">{{ item }}</el-button>
          </div>
        </div>
        <div class="semester">
          <div class="txt">学期：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in semesterList" :key="item.id" :class="{active: item.id == throwObj.semesterActive}" @click="allClick('seme',item)">{{ item.label }}</el-button>
          </div>
        </div>
        <div class="grade">
          <div class="txt">年级：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in gradeList" :key="item.Id" :class="{active: item.Id == throwObj.gradeActive}" @click="allClick('grade',item)">{{ item.Gradename }}</el-button>
          </div>
        </div>
      </div>
      <!-- 展示区 -->
      <div slot="reference" class="headDis">
        <img src="@/assets/teacher/tikuChapter.png" alt="" />
        <div class="txtList" v-show="leftWidth >= 16">
          <div>{{throwObj.yearActive}}学年</div>
          <i class="el-icon-arrow-right"></i>
          <div>{{throwObj.semesterActive == 1 ? '上' : '下'}}学期</div>
          <i class="el-icon-arrow-right"></i>
          <div>{{ getGradeName() }}</div>
        </div>
      </div>
    </el-popover>
    <!--  章节目录  -->
    <div class="childList" v-show="leftWidth >= 16">
      <div class="chapter-header">
        <h1>章节目录</h1>
        <el-popover placement="bottom-end" width="120" trigger="hover">
          <div class="add-unit-options">
            <div class="option-item" @click="addUnit(1)">
              新增同校单元
            </div>
            <div class="option-item" @click="addUnit(2)">
              新增个人单元
            </div>
          </div>
          <el-button slot="reference" type="success" size="mini" class="add-unit-btn">
            添加单元
          </el-button>
        </el-popover>
      </div>
      <!-- 自定义章节列表 -->
      <div class="custom-chapter-list">
        <draggable
          v-model="childList"
          :group="`level-1-${instanceId}`"
          handle=".drag-handle"
          :move="onMoveCheck"
          @change="onDragChange">
          <div
            v-for="(chapter, index) in childList"
            :key="chapter.ChapterId"
            class="chapter-item level-1"
            :data-type="chapter.Type">
            <!-- 一级章节内容 -->
            <div class="chapter-content">
              <div class="chapter-left">
                <!-- 拖拽图标 -->
                <i v-if="chapter.Type === 1 || chapter.Type === 2" class="el-icon-rank drag-handle" title="拖拽排序"></i>
                <span v-else class="drag-placeholder"></span>
                <!-- 展开/收起图标 -->
                <i
                  v-if="chapter.Second && chapter.Second.length > 0"
                  :class="chapter.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
                  @click="toggleExpand(chapter)">
                </i>
                <span v-else class="chapter-indent"></span>
                <!-- 复选框 -->
                <el-checkbox v-model="chapter.checked" @change="onChapterCheck(chapter)"></el-checkbox>
                <!-- 章节标签 -->
                <span v-if="chapter.Type === 1" class="chapter-tag school-tag">
                  同校
                </span>
                <span
                  v-if="chapter.Type === 2"
                  class="chapter-tag personal-tag">
                  个人
                </span>
                <!-- 章节名称 -->
                <el-input
                  v-if="chapter.editing"
                  v-model="chapter.ChapterName"
                  size="mini"
                  class="chapter-edit-input"
                  @blur="saveChapterName(chapter)"
                  @keyup.enter.native="saveChapterName(chapter)"
                  ref="chapterInput">
                </el-input>
                <span v-else class="chapter-name" @dblclick="startEdit(chapter)">
                  {{ chapter.ChapterName }}
                </span>
              </div>
              <!-- 操作按钮 -->
              <div class="chapter-actions">
                <el-popover
                  v-model="chapter.popoverVisible"
                  placement="left"
                  width="140"
                  trigger="hover"
                  :append-to-body="false"
                  :popper-class="`chapter-popover-${_uid}-${index}`">
                  <div class="action-options">
                    <div v-if="chapter.Creator=== userId" class="action-item" @click="editChapter(chapter); chapter.popoverVisible = false">
                      编辑
                    </div>
                    <div v-if="chapter.Type !== 2" class="action-item" @click="addChildChapter(chapter, 1, 2); chapter.popoverVisible = false">
                      新增同校章节
                    </div>
                    <div class="action-item" @click="addChildChapter(chapter, 2, 2); chapter.popoverVisible = false">
                      新增个人章节
                    </div>
                    <div v-if="chapter.Creator=== userId" class="action-item delete-item" @click="deleteChapter(chapter, index); chapter.popoverVisible = false">
                      删除
                    </div>
                  </div>
                  <i 
                    slot="reference"
                    class="el-icon-more action-icon">
                  </i>
                </el-popover>
              </div>
            </div>
            <!-- 二级章节 -->
            <div
              v-if="chapter.Second && chapter.Second.length > 0 && chapter.expanded"
              class="chapter-children level-2">
              <draggable
                v-model="chapter.Second"
                :group="`level-2-${instanceId}-${index}`"
                handle=".drag-handle-second"
                :move="onSecondMoveCheck"
                @change="onSecondDragChange(chapter)">
                <div 
                  v-for="(child, childIndex) in chapter.Second" 
                  :key="child.ChapterId"
                  class="chapter-item"
                  :data-type="child.Type">
                  <div class="chapter-content">
                    <div class="chapter-left">
                      <!-- 二级拖拽图标 -->
                      <i 
                        v-if="child.Type === 1 || child.Type === 2"
                        class="el-icon-rank drag-handle-second"
                        title="拖拽排序">
                      </i>
                      <span v-else class="drag-placeholder"></span>
                      
                      <!-- 展开/收起图标 -->
                      <i
                        v-if="child.Third && child.Third.length > 0"
                        :class="child.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
                        @click="toggleExpand(child)">
                      </i>
                      <span v-else class="chapter-indent"></span>
                      
                      <el-checkbox
                        v-model="child.checked"
                        @change="onChapterCheck(child)">
                      </el-checkbox>
                      
                      <span
                        v-if="child.Type === 1"
                        class="chapter-tag school-tag">
                        同校
                      </span>
                      <span 
                        v-if="child.Type === 2"
                        class="chapter-tag personal-tag">
                        个人
                      </span>
                      
                      <el-input
                        v-if="child.editing"
                        v-model="child.ChapterName"
                        size="mini"
                        class="chapter-edit-input"
                        @blur="saveChapterName(child)"
                        @keyup.enter.native="saveChapterName(child)"
                        ref="childInput">
                      </el-input>
                      <span 
                        v-else
                        class="chapter-name"
                        @dblclick="startEdit(child)">
                        {{ child.ChapterName }}
                      </span>
                    </div>
                    
                    <div class="chapter-actions">
                      <el-popover
                        v-model="child.popoverVisible"
                        placement="left"
                        width="140"
                        trigger="hover"
                        :append-to-body="false"
                        :popper-class="`chapter-popover-${_uid}-${childIndex}`">
                        <div class="action-options">
                          <div v-if="child.Creator=== userId" class="action-item" @click="editChapter(child); child.popoverVisible = false">
                            编辑
                          </div>
                          <div v-if="child.Type !== 2" class="action-item" @click="addChildChapter(child, 1, 3); child.popoverVisible = false">
                            新增同校课时
                          </div>
                          <div class="action-item" @click="addChildChapter(child, 2, 3); child.popoverVisible = false">
                            新增个人课时
                          </div>
                          <div v-if="child.Creator=== userId" class="action-item delete-item" @click="deleteChildChapter(chapter, childIndex); child.popoverVisible = false">
                            删除
                          </div>
                        </div>
                        <i slot="reference" class="el-icon-more action-icon"></i>
                      </el-popover>
                    </div>
                  </div>
                  
                  <!-- 三级章节 -->
                  <div 
                    v-if="child.Third && child.Third.length > 0 && child.expanded"
                    class="chapter-children level-3">
                    <draggable
                      v-model="child.Third"
                      :group="`level-3-${instanceId}-${index}-${childIndex}`"
                      handle=".drag-handle-third"
                      :move="onThirdMoveCheck"
                      @change="onThirdDragChange(child)">
                      <div 
                        v-for="(grandChild, grandChildIndex) in child.Third" 
                        :key="grandChild.ChapterId"
                        class="chapter-item"
                        :data-type="grandChild.Type">
                        <div class="chapter-content">
                          <div class="chapter-left">
                            <!-- 三级拖拽图标 -->
                            <i 
                              v-if="grandChild.Type === 1 || grandChild.Type === 2"
                              class="el-icon-rank drag-handle-third"
                              title="拖拽排序">
                            </i>
                            <span v-else class="drag-placeholder"></span>
                            
                            <span class="chapter-indent"></span>
                            <el-checkbox 
                              v-model="grandChild.checked"
                              @change="onChapterCheck(grandChild)">
                            </el-checkbox>
                            <span 
                              v-if="grandChild.Type === 1" 
                              class="chapter-tag school-tag">
                              同校
                            </span>
                            <span 
                              v-if="grandChild.Type === 2" 
                              class="chapter-tag personal-tag">
                              个人
                            </span>
                            <el-input
                              v-if="grandChild.editing"
                              v-model="grandChild.ChapterName"
                              size="mini"
                              class="chapter-edit-input"
                              @blur="saveChapterName(grandChild)"
                              @keyup.enter.native="saveChapterName(grandChild)"
                              ref="grandChildInput">
                            </el-input>
                            <span 
                              v-else
                              class="chapter-name"
                              @dblclick="startEdit(grandChild)">
                              {{ grandChild.ChapterName }}
                            </span>
                          </div>
                          <div class="chapter-actions">
                            <el-popover
                              v-model="grandChild.popoverVisible"
                              placement="left"
                              width="100"
                              trigger="hover"
                              :append-to-body="false"
                              :popper-class="`chapter-popover-${_uid}-${grandChildIndex}`">
                              <div class="action-options">
                                <div v-if="grandChild.Creator=== userId" class="action-item" @click="editChapter(grandChild); grandChild.popoverVisible = false">
                                  编辑
                                </div>
                                <div v-if="grandChild.Creator=== userId" class="action-item delete-item" @click="deleteGrandChildChapter(child, grandChildIndex); grandChild.popoverVisible = false">
                                  删除
                                </div>
                              </div>
                              <i 
                                slot="reference"
                                class="el-icon-more action-icon">
                              </i>
                            </el-popover>
                          </div>
                        </div>
                      </div>
                    </draggable>
                  </div>
                </div>
              </draggable>
            </div>
          </div>
        </draggable>
      </div>
    </div>
    <div class="MenuFooter" v-show="!openInDialog">
      <i class="el-icon-s-unfold" v-if="leftWidth < 16" @click="$emit('changeWidth')"></i>
      <i class="el-icon-s-fold" v-else @click="$emit('changeWidth')"></i>
    </div>
  </div>
</template>

<script>
import { Session } from '@/utils/storage.js';
import eventBus from '@/views/Teacher/eventBus/eventBus'
import draggable from 'vuedraggable'

export default {
  name: 'LeftScreening',
  components: {
    draggable
  },
  props:{
    leftWidth: {
      type: Number,
      default: 16,
    },
    openInDialog:{
      type: Boolean,
      default: false,
    },
    popoverDis:{
      type: Boolean,
      default: false,
    },
    // 资源模块下，试题资源显示
    tabActive:{
      type: String,
      default: ''
    },
    // 教学页面新增资源弹窗
    givelessons:{
      type: String,
      default: '',
    },
    // 添加页面标识符
    pageId: {
      type: String,
      default: () => Math.random().toString(36).substr(2, 9)
    }
  },
  data(){
    return {
      throwObj: {
        yearActive: null,
        semesterActive: null,
        gradeActive: 0,
        // gradeActive: getSessionValue('ChapterGradeId', ''),
        chapterList: [],
        level: 2, // 市区校级别
      },
      yearList:[],
      semesterList:[
        {label: '上学期', id: 1},
        {label: '下学期', id: 2}
      ],
      gradeList:[],
      geadeName: '',
      // 树形结构
      childList: [],
      defaultProps: {
        children: 'Second',
        label: 'ChapterName',
      },
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      showTitle: false,
      tooltipTitle: '',
      userId: localStorage.getItem('UserId'),
      nextId: 1000, // 新增章节的ID起始值
      ParentId: '',
      // 添加组件实例唯一标识
      instanceId: this.pageId
    }
  },
  computed: {
    childParentLs(){
      return this.$store.state.chapterStorage.ChapterObj.ParentId
    },
    childListIds(){
      return this.$store.state.chapterStorage.ChapterObj.Ids
    },
    year(){
      return this.$store.state.chapterStorage.ChapterObj.Year
    },
    term(){
      return this.$store.state.chapterStorage.ChapterObj.Term
    },
    grade(){
      return this.$store.state.chapterStorage.ChapterObj.GradeId
    },
    GradeList(){
      return this.$store.state.chapterStorage.ChapterObj.gradeList
    },
    ChildList(){
      return this.$store.state.chapterStorage.ChapterObj.childList
    },
    setActivename(){
      return this.$store.state.chapterStorage.ChapterObj.activeName
    }
  },
  // 使用watch监听childListIds的变化
  watch: {
    // 监听章节父级展开
    childParentLs(newValue, oldValue){
      this.defaultExpandedKeys = newValue
    },
    // 监听章节
    childListIds(newValue, oldValue) {
      this.$refs.ELtree.setCheckedKeys(newValue)
    },
    year(newValue, oldValue){
      this.throwObj.yearActive = newValue
    },
    term(newValue, oldValue){
      this.throwObj.semesterActive = newValue
    },
    grade(newValue, oldValue){
      this.throwObj.gradeActive = newValue
    },
    GradeList(newValue, oldValue){
      this.gradeList = newValue
    },
    ChildList(newValue, oldValue){
      this.childList = newValue
    },
    setActivename(newValue, oldValue){
      if(newValue){
        this.getChild()
      }
    },
  },
  // 添加生命周期钩子，确保组件销毁时清理弹框
  beforeDestroy() {
    this.closeAllPopovers();
  },
  created() {
    this.getYear()
  },
  methods:{
    check(data) {
      if(data.length > 0) {
        // const data = nodes.filter(item => !item.Second)
        this.throwObj.chapterList = data
        if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)) {
          // 处理章节树选择值做存储
          const parentIds = [...new Set(data.map(item => item.ParentId))];
          const chapterIds = data.map(item => item.ChapterId);
          this.$store.dispatch('chapterStorage/setIds', chapterIds)
          this.$store.dispatch('chapterStorage/setParentId', parentIds)
        }
      } else {
        if(this.$route.path !== '/threeAssistants/JobDesign/NewJob'&& (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
          this.$store.dispatch('chapterStorage/resetParentIdAndIds')
        }
        this.clearAll()
      }
      // console.log(this.throwObj)
      this.$emit('getAllList',this.throwObj)
    },
    onShowNameTipsMouseenter(e) {
      // console.log(e.target)
      // return false
      const target = e.target
      let textLength = target.clientWidth;
      let containerLength = target.scrollWidth;
      if (textLength < containerLength) {
        this.tooltipTitle = e.target.innerText;
        this.showTitle = false;
      } else {
        this.showTitle = true;
      }
    },
    // 弹窗点击事件
    allClick(type,val) {
      const actions = {
        year: () => {
          if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
            this.$store.dispatch('chapterStorage/setYear', val)
            this.$store.dispatch('chapterStorage/resetParentIdAndIds')
          }
          this.throwObj.yearActive = val
          if(localStorage.getItem('LoginSource') === '1'){
            this.$emit('verification', val)
          }
          this.getGrade()
          // this.setEventBus()
          // this.$emit('getAllList',this.throwObj)
        },
        seme: () => {
          if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
            this.$store.dispatch('chapterStorage/setTerm', val.id)
            this.$store.dispatch('chapterStorage/resetParentIdAndIds')
          }
          this.throwObj.semesterActive = val.id
          // this.$emit('getAllList',this.throwObj)
          this.getChild()
        },
        grade: () => {
          if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
            this.$store.dispatch('chapterStorage/setGradeId', val.Id)
            this.$store.dispatch('chapterStorage/resetParentIdAndIds')
          }
          this.throwObj.gradeActive = val.Id
          this.geadeName = val.Gradename
          this.getChild()
          this.setEventBus()
          // this.$emit('getAllList',this.throwObj)
        }
      }
      if(actions[type]) actions[type]()
    },
    // 获取当前学年接口
    async getYear() {
      const { Year, Term } = this.$store.state.chapterStorage.ChapterObj
      const { data:res } = await this.$uwonhttp.get(`SemesterTime/SemesterTime/GetNowYearSemesterAndList`)
      if(res.Success){
        this.yearList = res.Data.Years
        // 当前学期
        Session.set('MomentSemester',res.Data.NowTerm)
        // 作业设计弹窗执行逻辑
        this.throwObj.yearActive = Year || res.Data.NowYear;
        this.throwObj.semesterActive = Term || res.Data.NowTerm;
        if (!Year || !Term) {
          this.$store.dispatch('chapterStorage/setYear', res.Data.NowYear);
          this.$store.dispatch('chapterStorage/setTerm', res.Data.NowTerm);
        }
        this.getGrade()
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取年级接口
    async getGrade(){
      // const { GradeId } = this.$route.query
      const { GradeId, Year } = this.$store.state.chapterStorage.ChapterObj
      const { yearActive } = this.throwObj
      const { data:res } = await this.$uwonhttp.get('/Class/TeacherClassManager/GetGradeList?year='+ Year || yearActive)
      if(res.Success){
        this.gradeList = res.Data
        this.$store.dispatch('chapterStorage/setGradeList', res.Data)
        if(res.Data.length == 0){
          this.throwObj.gradeActive = 0
          this.geadeName = ''
          this.childList = []
          this.$store.dispatch('chapterStorage/setGradeId', 0)
          this.$store.dispatch('chapterStorage/setChildList', [])
          this.clearAll()
          return false
        }
        // 判断ChapapterGradeId中是否有值
        if(GradeId){
          this.throwObj.gradeActive = GradeId
          // this.geadeName = res.Data.filter(item => item.Id == ChapterGradeId)[0].Gradename
          //
          const grade = res.Data.find(item => item.Id == GradeId)
          if(grade){
            this.geadeName = grade.Gradename
          } else {
            this.geadeName = res.Data[0].Gradename
            this.throwObj.gradeActive = res.Data[0].Id
            this.geadeName = res.Data[0].Gradename
            if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
              this.$store.dispatch('chapterStorage/setGradeId', res.Data[0].Id)
            }
          }
        } else {
          this.throwObj.gradeActive = res.Data[0].Id
          this.geadeName = res.Data[0].Gradename
          if (this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)) {
            this.$store.dispatch('chapterStorage/setGradeId', res.Data[0].Id)
          }
        }
        this.getChild()
        this.setEventBus()
        // this.$emit('getAllList',this.throwObj)
      } else {
        this.gradeList = []
        Session.remove('gradeList')
        this.$message.error(res.Msg)
      }
    },
    // 获取章节接口
    async getChild() {
      this.clearAll()
      const { Ids, ParentId, Year, Term, GradeId, activeName } = this.$store.state.chapterStorage.ChapterObj
      const { yearActive, semesterActive, gradeActive, level } = this.throwObj
      // 处理章节树试题资源添加章节下试题数量
      let urlData
      if((this.$route.path === '/threeAssistants/newJobResources/index') || (this.$route.path === '/Paper/Exam_MicroLesson/QuestionBankResourceCenter' && activeName === 'TiMuResource')){
        urlData = `Chapter/Chapter/GetChapterListByCondition?year=${Year || yearActive}&term=${Term || semesterActive}&grade=${GradeId || gradeActive}&level=${level}&roleId=${localStorage.getItem('role')}`
      }else{
        urlData = `Chapter/Chapter/GetChapterListByCondition?year=${Year || yearActive}&term=${Term || semesterActive}&grade=${GradeId || gradeActive}`
      }
      const { data:res } = await this.$uwonhttp.get(urlData)
      if(res.Success) {
        if((this.$route.path === '/threeAssistants/newJobResources/index') || (this.$route.path === '/Paper/Exam_MicroLesson/QuestionBankResourceCenter' && activeName === 'TiMuResource')) {
          res.Data.forEach(item => {
            if(item.Second.length > 0) {
              item.Second.forEach(item => {
                item.ChapterName = item.ChapterName + `[${item.ResourceCount}]`
              })
            }
          })
          this.childList = res.Data
        } else {
          this.childList = res.Data
        }
        this.ParentId = res.Data[res.Data.length - 1].ChapterId
        this.$store.dispatch('chapterStorage/setChildList', this.childList)
        const chapterIdMap = new Map(Ids.map((id, index) => [id, index]));
        const arr = this.childList
          .filter(child => child?.Second?.length)
          .flatMap(child => child.Second)
          .filter(item => chapterIdMap.has(item.ChapterId))
        if(arr.length > 0) {
          this.throwObj.chapterList = arr
          // 展开
          this.defaultExpandedKeys = ParentId
          // 选中
          this.defaultCheckedKeys = Ids
        } else {
          this.clearAll()
        }
        this.$emit('getAllList',this.throwObj)
      } else {
        this.$message.error(res.Msg)
      }
    },
    getGradeName() {
      const { gradeList, GradeId } = this.$store.state.chapterStorage.ChapterObj
      if(gradeList.length){
        const name = gradeList.find(item => item.Id == GradeId)
        return name.Gradename
      } else {
        return ''
      }
    },
    // 章节清空逻辑
    clearAll(){
      this.throwObj.chapterList = []
      this.defaultExpandedKeys = []
      this.defaultCheckedKeys = []

    },
    // 该方法只在资源下题目资源模块中使用
    setLevel(val){
      this.throwObj.level = val
      this.getChild()
    },
    // 传参调用兄弟组件难度接口
    setEventBus(){
      eventBus.$emit('getDifficulty',this.throwObj)
    },
    // 关闭所有弹框
    closeAllPopovers() {
      const closePopover = (item) => {
        if (item.popoverVisible) {
          item.popoverVisible = false;
        }
        if (item.Second) {
          item.Second.forEach(closePopover);
        }
        if (item.Third) {
          item.Third.forEach(closePopover);
        }
      };
      this.childList.forEach(closePopover);
    },
    // 添加单元
    addUnit(type) {
      const newUnit = {
        id: this.nextId++,
        name: type === 1 ? '同校单元' : '个人单元',
        Type: type,
        checked: false,
        expanded: false,
        popoverVisible: false, // 添加弹框显示状态
        Second: [],
        editing: true,
        ChapterName: type === 1 ? '同校单元' : '个人单元',
        Creator: this.userId,
        ParentId: this.ParentId,
        Level: 1,
        Year: this.throwObj.yearActive,
        _instanceId: this.instanceId // 添加实例标识
      };
      this.childList.push(newUnit);
    },
    // 切换展开/收起
    toggleExpand(chapter) {
      chapter.expanded = !chapter.expanded;
      this.$forceUpdate()
    },
    // 章节选中处理
    onChapterCheck(chapter) {
      // 处理选中逻辑
      this.updateCheckedState(chapter);
      this.emitChapterChange();
    },
    // 开始编辑章节
    startEdit(chapter) {
      chapter.editing = true;
      // chapter.ChapterName = chapter.ChapterName;
      this.$forceUpdate()
      this.$nextTick(() => {
        // 聚焦到输入框
        const inputs = this.$refs.chapterInput || this.$refs.childInput;
        if (inputs) {
          const input = Array.isArray(inputs) ? inputs.find(inp => inp) : inputs;
          if (input) {
            input.focus();
            input.select();
          }
        }
      });
    },
    // 保存章节名称
    async saveChapterName(chapter) {
      if (chapter.ChapterName && chapter.ChapterName.trim()) {
        chapter.ChapterName = chapter.ChapterName.trim();
      } else {
        // chapter.ChapterName = chapter.ChapterName;
        this.$message.warning('章节名称不能为空');
      }
      chapter.editing = false;
      this.$forceUpdate()
      const { ChapterName, Level, ParentId, Type, ChapterId } = chapter
      await this.$uwonhttp.post(`${ChapterId ? '/Chapter/TeacherChapter/EditCustomChapter' : '/Chapter/TeacherChapter/AddCustomChapter'}`, {
        ChapterName,
        Level,
        ParentId,
        Type,
        ChapterId,
        Year: this.throwObj.yearActive
      })
      this.getChild()
    },
    // 编辑章节（从操作菜单触发）
    editChapter(chapter) {
      this.startEdit(chapter);
    },
    // 添加子章节
    addChildChapter(parent, type, level) {
      if (level > 3) {
        this.$message.warning('最多支持3级章节');
        return;
      }
      const levelNames = {
        2: '章节',
        3: '课时'
      };
      const newChild = {
        id: this.nextId++,
        Type: type,
        checked: false,
        ParentId: parent.ChapterId,
        editing: true,
        popoverVisible: false, // 添加弹框显示状态
        ChapterName: `自定义${levelNames[level] || '章节'}`,
        Level: level,
        Creator: this.userId,
        _instanceId: this.instanceId // 添加实例标识
      };
      
      if (level == 2) {
        newChild.Third = [];
        newChild.expanded = false;
        parent.Second.push(newChild);
      } else if (level == 3) {
        newChild.expanded = false;
        parent.Third.push(newChild);
      } else {
        parent.Second.push(newChild);
      }
      parent.expanded = true;
      this.$forceUpdate()
    },
    // 删除章节
    deleteChapter(chapter, index) {
      this.$confirm('确定删除该单元吗？删除后不可恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.childList.splice(index, 1);
        this.$message.success('删除成功');
        this.requestDeleteChapter(chapter.ChapterId);
      }).catch(() => {});
    },
    // 删除子章节
    deleteChildChapter(parent, childIndex) {
      this.$confirm('确定删除该章节吗？删除后不可恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        parent.children.splice(childIndex, 1);
        this.$message.success('删除成功');
        this.requestDeleteChapter(parent.ChapterId);
      }).catch(() => {});
    },
    // 删除三级章节
    deleteGrandChildChapter(parent, grandChildIndex) {
      this.$confirm('确定删除该课时吗？删除后不可恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        parent.children.splice(grandChildIndex, 1);
        this.$message.success('删除成功');
        this.requestDeleteChapter(parent.ChapterId);
      }).catch(() => {});
    },
    async requestDeleteChapter(chapterId) {
      await this.$uwonhttp.get(`/Chapter/TeacherChapter/DeleteCustomChapter?chapterId=${chapterId}`)
      this.getChild()
    },
    // 更新选中状态（递归处理3级）
    updateCheckedState(chapter) {
      const childList = chapter.Second || chapter.Third
      if (childList) {
        childList.forEach(child => {
          child.checked = chapter.checked;
          // 处理三级章节
          if (child.Third) {
            child.Third.forEach(grandChild => {
              grandChild.checked = chapter.checked;
            });
          }
        });
      }
    },
    // 发送章节变化事件
    emitChapterChange() {
      const selectedChapters = this.getSelectedChapters();
      this.check(selectedChapters)
    },
    // 获取选中的章节（处理3级）
    getSelectedChapters() {
      const selected = [];
      this.childList.forEach(chapter => {
        if (chapter.checked&&!chapter.Second?.length) {
          selected.push(chapter);
        }
        if (chapter.Second) {
          chapter.Second.forEach(child => {
            if (child.checked&&!child.Third?.length) {
              selected.push(child);
            }
            if (child.Third) {
              child.Third.forEach(grandChild => {
                if (grandChild.checked) {
                  selected.push(grandChild);
                }
              });
            }
          });
        }
      });
      return selected;
    },
    // 一级章节拖拽排序
    onDragChange(event) {
      console.log('一级章节排序变化:', event);
      this.updateChapterOrder();
    },
    
    // 二级章节拖拽排序
    onSecondDragChange(parentChapter) {
      console.log('二级章节排序变化:', parentChapter);
      this.updateSecondChapterOrder(parentChapter);
    },
    
    // 三级章节拖拽排序
    onThirdDragChange(parentChapter) {
      console.log('三级章节排序变化:', parentChapter);
      this.updateThirdChapterOrder(parentChapter);
    },
    
    // 更新一级章节排序
    async updateChapterOrder() {
      try {
        const orderData = this.childList.filter(chapter => chapter.Type).map((chapter, index) => ({
          ChapterId: chapter.ChapterId,
          // OrderId: index + 1
        }));
        
        // 调用后端接口更新排序
        await this.$uwonhttp.post('/Chapter/TeacherChapter/SortCustomChapter', {
          OrderChapters: orderData
        });
        
        this.$message.success('排序更新成功');
      } catch (error) {
        console.error('更新排序失败:', error);
        this.$message.error('排序更新失败');
      }
    },
    
    // 更新二级章节排序
    async updateSecondChapterOrder(parentChapter) {
      try {
        const orderData = parentChapter.Second.filter(chapter => chapter.Type).map((chapter, index) => ({
          ChapterId: chapter.ChapterId,
          // OrderId: index + 1
        }));
        
        await this.$uwonhttp.post('/Chapter/TeacherChapter/SortCustomChapter', {
          parentId: parentChapter.ChapterId,
          OrderChapters: orderData
        });
        
        this.$message.success('二级章节排序更新成功');
      } catch (error) {
        console.error('更新二级章节排序失败:', error);
        this.$message.error('二级章节排序更新失败');
      }
    },
    
    // 更新三级章节排序
    async updateThirdChapterOrder(parentChapter) {
      try {
        const orderData = parentChapter.Third.filter(chapter => chapter.Type).map((chapter, index) => ({
          ChapterId: chapter.ChapterId,
          // OrderId: index + 1
        }));
        
        await this.$uwonhttp.post('/Chapter/TeacherChapter/SortCustomChapter', {
          parentId: parentChapter.ChapterId,
          OrderChapters: orderData
        });
        
        this.$message.success('三级章节排序更新成功');
      } catch (error) {
        console.error('更新三级章节排序失败:', error);
        this.$message.error('三级章节排序更新失败');
      }
    },
    onMoveCheck(evt) {
      const draggedElement = evt.draggedContext.element;
      const relatedElement = evt.relatedContext.element;
      // 如果拖拽的是 Type 1 或 2 的章节
      if (draggedElement.Type === 1 || draggedElement.Type === 2) {
        // 只能放到 Type 1 或 2 的章节位置，或者列表末尾
        if (relatedElement && relatedElement.Type !== 1 && relatedElement.Type !== 2) {
          return false;
        }
      }
      return true;
    },
    onSecondMoveCheck(evt) {
      const draggedElement = evt.draggedContext.element;
      const relatedElement = evt.relatedContext.element;

      if (draggedElement.Type === 1 || draggedElement.Type === 2) {
        if (relatedElement && relatedElement.Type !== 1 && relatedElement.Type !== 2) {
          return false;
        }
      }

      return true;
    },
    onThirdMoveCheck(evt) {
      const draggedElement = evt.draggedContext.element;
      const relatedElement = evt.relatedContext.element;

      if (draggedElement.Type === 1 || draggedElement.Type === 2) {
        if (relatedElement && relatedElement.Type !== 1 && relatedElement.Type !== 2) {
          return false;
        }
      }

      return true;
    },
    // 关闭弹框方法
    closePopover(refName) {
      this.$refs[refName]?.doClose();
    },
  }
}
</script>

<style lang="less" scoped>
.span-ellipsis {
  width: 100%;
  //overflow: hidden;
  //white-space: nowrap;
  //text-overflow: ellipsis;
  margin-right: 20px;
  font-size: 20px;
}
.divGal{
  height: 100%;
  position: relative;
}
.cardList{
  width: 100%;
  .academicYear,.semester,.grade{
    width: 100%;
    display: flex;
    margin: 10px 0;
    .txt{
      width: 14%;
      font-size: 20px;
    }
    .btnList{
      width: 86%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .el-button{
        color: #6e6e6e;
        font-size: 20px;
        padding: 0 0 10px 0;
      }
    }
  }
}
.headDis{
  display: flex;
  align-items: center;
  background-color: #7fb99a;
  padding: 10px 0 10px 12px;
  img{
    width: 18px;
    height: 22px;
  }
  .txtList{
    display: flex;
    align-items: center;
    margin-left: 10px;
    >div{
      font-size: 20px;
      color: #FFFFFF;
    }
    >i{
      font-size: 18px;
      //margin: 0 2px;
      color: #FFFFFF;
    }
  }
}
.active {
  color: #7fb99a !important;
}
// 章节结构
.childList {
  height: 87%;
  // padding: 0 14px;
  overflow: auto;
  .chapter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    padding: 0 14px;
    h1 {
      font-size: 18px;
      margin: 4px 0;
    }
    .add-unit-btn {
      background: #7fb99a;
      border-color: #7fb99a;
      border-radius: 4px;
      font-size: 18px;
      &:hover {
        background: #6ba085;
        border-color: #6ba085;
      }
    }
  }
}
.add-unit-options {
  // padding: 8px 0;
  .option-item {
    padding: 4px 8px;
    cursor: pointer;
    font-size: 18px;
    color: #333;
    border-radius: 4px;
    margin: 5px 0;
    text-align: center;
    &:hover {
      background: #f5f5f5;
      color: #7fb99a;
    }
  }
}
// 底部
.MenuFooter{
  width: 100%;
  color: #fff;
  padding: 13px 10px;
  display: flex;
  justify-content: right;
  position: absolute;
  bottom: 0;
  left: 0;
  i{
    color: #999;
    font-size: 30px;
    cursor: pointer;
  }
}
.custom-chapter-list {
  padding-left: 5px;
  padding-right: 8px;
  .chapter-item {
    margin: 4px 0;
    
    // 添加拖拽限制的视觉提示
    &[data-type="1"],
    &[data-type="2"] {
      .chapter-content {
        border-left: 3px solid transparent;
        
        &:hover {
          border-left-color: #7fb99a;
        }
      }
    }
    
    .chapter-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 4px;
      border-radius: 4px;
      margin-bottom: 4px;
      transition: all 0.2s ease;
      
      &:hover {
        background: #e9ecef;
      }
    }
    
    .chapter-left {
      display: flex;
      align-items: center;
      flex: 1;
      
      // 拖拽图标样式
      .drag-handle,
      .drag-handle-second,
      .drag-handle-third {
        font-size: 18px;
        color: #999;
        cursor: move;
        // margin-right: 8px;
        padding: 2px;
        
        &:hover {
          color: #7fb99a;
        }
      }
      
      .drag-placeholder {
        width: 20px;
        // margin-right: 4px;
      }
      
      .chapter-name {
        font-size: 20px;
        color: #333;
        margin-right: 8px;
        cursor: pointer;
        
        &:hover {
          color: #7fb99a;
        }
      }
      
      .chapter-edit-input {
        width: 120px;
        
        /deep/ .el-input__inner {
          height: 28px;
          line-height: 28px;
          font-size: 18px;
          padding: 0 8px;
        }
      }
    }
    
    .school-tag {
      background-color: #e8f5e8;
      color: #67c23a;
      font-size: 14px;
    }
    
    .personal-tag {
      background-color: #fdf6ec;
      color: #e6a23c;
      font-size: 14px;
    }
    
    .chapter-children {
      &.level-2 {
        margin-left: 20px;
        
        .chapter-content {
          &:hover {
            background: #e0e0e0;
          }
        }
      }
      
      &.level-3 {
        margin-left: 40px;
        
        .chapter-content {
          &:hover {
            background: #d8d8d8;
          }
        }
      }
    }
  }
  
  // 拖拽时的样式
  .sortable-ghost {
    opacity: 0.5;
    background: #f0f9ff;
    border: 2px dashed #7fb99a;
  }
  
  .sortable-chosen {
    background: #f0f9ff;
  }
  
  // 拖拽不允许时的样式
  .sortable-fallback {
    background: #ffebee !important;
    border: 2px dashed #f56c6c !important;
  }
  
  /deep/ .el-checkbox {
    .el-checkbox__inner {
      width: 18px;
      height: 18px;
      
      &::after {
        left: 6px;
        top: 2px;
        width: 4px;
        height: 8px;
      }
    }
    
    .el-checkbox__label {
      font-size: 18px;
    }
  }
  
  .chapter-tag {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 18px;
    margin-right: 6px;
    font-weight: 500;
  }
  
  .action-icon {
    font-size: 18px;
    color: #999;
    cursor: pointer;
    
    &:hover {
      color: #7fb99a;
    }
  }
  
  i.el-icon-caret-bottom,
  i.el-icon-caret-right {
    font-size: 18px;
    margin-right: 8px;
    cursor: pointer;
    color: #666;
    width: 16px;
    text-align: center;
    
    &:hover {
      color: #409eff;
    }
  }
  
  .chapter-indent {
    width: 16px;
    margin-right: 8px;
  }
}

.action-options {
  padding: 4px 0;
  
  .action-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 18px;
    color: #333;
    border-radius: 4px;
    margin: 2px 0;
    
    &:hover {
      background: #f5f5f5;
      color: #7fb99a;
    }
    
    &.delete-item {
      color: #f56c6c;
      
      &:hover {
        background: #fef0f0;
        color: #f56c6c;
      }
    }
  }
}
</style>
