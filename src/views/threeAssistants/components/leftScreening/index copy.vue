<template>
  <div class="divGal">
    <el-popover
      placement="bottom"
      width="420"
      :disabled="popoverDis"
      trigger="click">
      <!-- 弹窗选择区 -->
      <div class="cardList">
        <div class="academicYear">
          <div class="txt">学年：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in yearList" :key="item" :class="{active: item == throwObj.yearActive}" @click="allClick('year',item)">{{ item }}</el-button>
          </div>
        </div>
        <div class="semester">
          <div class="txt">学期：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in semesterList" :key="item.id" :class="{active: item.id == throwObj.semesterActive}" @click="allClick('seme',item)">{{ item.label }}</el-button>
          </div>
        </div>
        <div class="grade">
          <div class="txt">年级：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in gradeList" :key="item.Id" :class="{active: item.Id == throwObj.gradeActive}" @click="allClick('grade',item)">{{ item.Gradename }}</el-button>
          </div>
        </div>
      </div>
      <!-- 展示区 -->
      <div slot="reference" class="headDis">
        <img src="@/assets/teacher/tikuChapter.png" alt="" />
        <div class="txtList" v-show="leftWidth >= 16">
          <div>{{throwObj.yearActive}}学年</div>
          <i class="el-icon-arrow-right"></i>
          <div>{{throwObj.semesterActive == 1 ? '上' : '下'}}学期</div>
          <i class="el-icon-arrow-right"></i>
          <div>{{ getGradeName() }}</div>
        </div>
      </div>
    </el-popover>
    <!--  章节目录  -->
    <div class="childList" v-show="leftWidth >= 16">
      <h1>章节目录</h1>
      <div>
        <el-tree
          ref="ELtree"
          :data="childList"
          :props="defaultProps"
          :default-expanded-keys="defaultExpandedKeys"
          :default-checked-keys="defaultCheckedKeys"
          node-key="ChapterId"
          show-checkbox
          @check="check">
          <el-tooltip
            :disabled="showTitle"
            effect="dark"
            :content="tooltipTitle"
            placement="top"
            slot-scope="{ node, data }"
            >
          <span class="span-ellipsis" @mouseover="onShowNameTipsMouseenter">{{ node.label }}</span>
          </el-tooltip>
        </el-tree>
      </div>
    </div>
    <div class="MenuFooter" v-show="!openInDialog">
      <i class="el-icon-s-unfold" v-if="leftWidth < 16" @click="$emit('changeWidth')"></i>
      <i class="el-icon-s-fold" v-else @click="$emit('changeWidth')"></i>
    </div>
  </div>
</template>

<script>
import { Session } from '@/utils/storage.js';
import eventBus from '@/views/Teacher/eventBus/eventBus'

export default {
  name: 'index',
  props:{
    leftWidth: {
      type: Number,
      default: 16,
    },
    openInDialog:{
      type: Boolean,
      default: false,
    },
    popoverDis:{
      type: Boolean,
      default: false,
    },
    // 资源模块下，试题资源显示
    tabActive:{
      type: String,
      default: ''
    },
    // 教学页面新增资源弹窗
    givelessons:{
      type: String,
      default: '',
    }
  },
  data() {
    return {
      throwObj: {
        yearActive: null,
        semesterActive: null,
        gradeActive: 0,
        // gradeActive: getSessionValue('ChapterGradeId', ''),
        chapterList: [],
        level: 2, // 市区校级别
      },
      yearList:[],
      semesterList:[
        {label: '上学期', id: 1},
        {label: '下学期', id: 2}
      ],
      gradeList:[],
      geadeName: '',
      // 树形结构
      childList: [],
      defaultProps: {
        children: 'Second',
        label: 'ChapterName',
      },
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      showTitle: false,
      tooltipTitle: '',
    }
  },
  created() {
    this.getYear()
  },
  computed: {
    childParentLs() {
      return this.$store.state.chapterStorage.ChapterObj.ParentId
    },
    childListIds() {
      return this.$store.state.chapterStorage.ChapterObj.Ids
    },
    year() {
      return this.$store.state.chapterStorage.ChapterObj.Year
    },
    term() {
      return this.$store.state.chapterStorage.ChapterObj.Term
    },
    grade() {
      return this.$store.state.chapterStorage.ChapterObj.GradeId
    },
    GradeList() {
      return this.$store.state.chapterStorage.ChapterObj.gradeList
    },
    ChildList() {
      return this.$store.state.chapterStorage.ChapterObj.childList
    },
    setActivename() {
      return this.$store.state.chapterStorage.ChapterObj.activeName
    }
  },
  // 使用watch监听childListIds的变化
  watch: {
    // 监听章节父级展开
    childParentLs(newValue, oldValue) {
      this.defaultExpandedKeys = newValue
    },
    // 监听章节
    childListIds(newValue, oldValue) {
      this.$refs.ELtree.setCheckedKeys(newValue)
    },
    year(newValue, oldValue) {
      this.throwObj.yearActive = newValue
    },
    term(newValue, oldValue) {
      this.throwObj.semesterActive = newValue
    },
    grade(newValue, oldValue) {
      this.throwObj.gradeActive = newValue
    },
    GradeList(newValue, oldValue) {
      this.gradeList = newValue
    },
    ChildList(newValue, oldValue) {
      this.childList = newValue
    },
    setActivename(newValue, oldValue) {
      if(newValue) {
        this.getChild()
      }
    },
  },
  methods:{
    // check(ar1, ar2){
    //   if(ar2.checkedNodes.length > 0){
    //     const data = ar2.checkedNodes.filter(item => !item.Second)
     check(checkedNodes, { checkedNodes: nodes }) {
      if(nodes.length > 0) {
        const data = nodes.filter(item => !item.Second)
        this.throwObj.chapterList = data
        if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)) {
          // 处理章节树选择值做存储
          const parentIds = [...new Set(data.map(item => item.ParentId))];
          const chapterIds = data.map(item => item.ChapterId);
          this.$store.dispatch('chapterStorage/setIds', chapterIds)
          this.$store.dispatch('chapterStorage/setParentId', parentIds)
        }
      } else {
        if(this.$route.path !== '/threeAssistants/JobDesign/NewJob'&& (this.$route.path !== '/giveLessons/index' && !this.givelessons)) {
          this.$store.dispatch('chapterStorage/resetParentIdAndIds')
        }
        this.clearAll()
      }
      // console.log(this.throwObj)
      this.$emit('getAllList',this.throwObj)
    },
    onShowNameTipsMouseenter(e) {
      // console.log(e.target)
      // return false
      const target = e.target
      let textLength = target.clientWidth;
      let containerLength = target.scrollWidth;
      if (textLength < containerLength) {
        this.tooltipTitle = e.target.innerText;
        this.showTitle = false;
      } else {
        this.showTitle = true;
      }
    },
    // 弹窗点击事件
    allClick(type,val) {
      const actions = {
        year: () => {
          if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
            this.$store.dispatch('chapterStorage/setYear', val)
            this.$store.dispatch('chapterStorage/resetParentIdAndIds')
          }
          this.throwObj.yearActive = val
          if(localStorage.getItem('LoginSource') === '1'){
            this.$emit('verification', val)
          }
          this.getGrade()
          // this.setEventBus()
          // this.$emit('getAllList',this.throwObj)
        },
        seme: () => {
          if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
            this.$store.dispatch('chapterStorage/setTerm', val.id)
            this.$store.dispatch('chapterStorage/resetParentIdAndIds')
          }
          this.throwObj.semesterActive = val.id
          // this.$emit('getAllList',this.throwObj)
          this.getChild()
        },
        grade: () => {
          if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
            this.$store.dispatch('chapterStorage/setGradeId', val.Id)
            this.$store.dispatch('chapterStorage/resetParentIdAndIds')
          }
          this.throwObj.gradeActive = val.Id
          this.geadeName = val.Gradename
          this.getChild()
          this.setEventBus()
          // this.$emit('getAllList',this.throwObj)
        }
      }
      if(actions[type]) actions[type]()
    },
    // 获取当前学年接口
    async getYear(){
      const { Year, Term } = this.$store.state.chapterStorage.ChapterObj
      const { data:res } = await this.$uwonhttp.get(`SemesterTime/SemesterTime/GetNowYearSemesterAndList`)
      if(res.Success){
        this.yearList = res.Data.Years
        // 当前学期
        Session.set('MomentSemester',res.Data.NowTerm)
        // 作业设计弹窗执行逻辑
        this.throwObj.yearActive = Year || res.Data.NowYear;
        this.throwObj.semesterActive = Term || res.Data.NowTerm;
        if (!Year || !Term) {
          this.$store.dispatch('chapterStorage/setYear', res.Data.NowYear);
          this.$store.dispatch('chapterStorage/setTerm', res.Data.NowTerm);
        }
        this.getGrade()
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取年级接口
    async getGrade() {
      // const { GradeId } = this.$route.query
      const { GradeId, Year } = this.$store.state.chapterStorage.ChapterObj
      const { yearActive } = this.throwObj
      const { data:res } = await this.$uwonhttp.get('/Class/TeacherClassManager/GetGradeList?year='+ Year || yearActive)
      if(res.Success){
        this.gradeList = res.Data
        this.$store.dispatch('chapterStorage/setGradeList', res.Data)
        if(res.Data.length == 0){
          this.throwObj.gradeActive = 0
          this.geadeName = ''
          this.childList = []
          this.$store.dispatch('chapterStorage/setGradeId', 0)
          this.$store.dispatch('chapterStorage/setChildList', [])
          this.clearAll()
          return false
        }
        // 判断ChapapterGradeId中是否有值
        if(GradeId){
          this.throwObj.gradeActive = GradeId
          // this.geadeName = res.Data.filter(item => item.Id == ChapterGradeId)[0].Gradename
          //
          const grade = res.Data.find(item => item.Id == GradeId)
          if(grade){
            this.geadeName = grade.Gradename
          } else {
            this.geadeName = res.Data[0].Gradename
            this.throwObj.gradeActive = res.Data[0].Id
            this.geadeName = res.Data[0].Gradename
            if(this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)){
              this.$store.dispatch('chapterStorage/setGradeId', res.Data[0].Id)
            }
          }
        } else {
          this.throwObj.gradeActive = res.Data[0].Id
          this.geadeName = res.Data[0].Gradename
          if (this.$route.path !== '/threeAssistants/JobDesign/NewJob' && (this.$route.path !== '/giveLessons/index' && !this.givelessons)) {
            this.$store.dispatch('chapterStorage/setGradeId', res.Data[0].Id)
          }
        }
        this.getChild()
        this.setEventBus()
        // this.$emit('getAllList',this.throwObj)
      } else {
        this.gradeList = []
        Session.remove('gradeList')
        this.$message.error(res.Msg)
      }
    },
    // 获取章节接口
    async getChild(){
      this.clearAll()
      const { Ids, ParentId, Year, Term, GradeId, activeName } = this.$store.state.chapterStorage.ChapterObj
      const { yearActive, semesterActive, gradeActive, level } = this.throwObj
      // 处理章节树试题资源添加章节下试题数量
      let urlData
      if((this.$route.path === '/threeAssistants/newJobResources/index') || (this.$route.path === '/Paper/Exam_MicroLesson/QuestionBankResourceCenter' && activeName === 'TiMuResource')){
        urlData = `Chapter/Chapter/GetChapterListByCondition?year=${Year || yearActive}&term=${Term || semesterActive}&grade=${GradeId || gradeActive}&level=${level}&roleId=${localStorage.getItem('role')}`
      }else{
        urlData = `Chapter/Chapter/GetChapterListByCondition?year=${Year || yearActive}&term=${Term || semesterActive}&grade=${GradeId || gradeActive}&roleId=${localStorage.getItem('role')}`
      }
      const { data:res } = await this.$uwonhttp.get(urlData)
      if(res.Success){
        if((this.$route.path === '/threeAssistants/newJobResources/index') || (this.$route.path === '/Paper/Exam_MicroLesson/QuestionBankResourceCenter' && activeName === 'TiMuResource')){
          res.Data.forEach(item => {
            if(item.Second.length > 0){
              item.Second.forEach(item => {
                item.ChapterName = item.ChapterName + `[${item.ResourceCount}]`
              })
            }
          })
          this.childList = res.Data
        } else {
          this.childList = res.Data
        }
        this.$store.dispatch('chapterStorage/setChildList', this.childList)
        const chapterIdMap = new Map(Ids.map((id, index) => [id, index]));
        const arr = this.childList
          .filter(child => child?.Second?.length)
          .flatMap(child => child.Second)
          .filter(item => chapterIdMap.has(item.ChapterId))
        if(arr.length > 0){
          this.throwObj.chapterList = arr
          // 展开
          this.defaultExpandedKeys = ParentId
          // 选中
          this.defaultCheckedKeys = Ids
        } else {
          this.clearAll()
        }
        this.$emit('getAllList',this.throwObj)
      } else {
        this.$message.error(res.Msg)
      }
    },
    getGradeName(){
      const { gradeList, GradeId } = this.$store.state.chapterStorage.ChapterObj
      if(gradeList.length){
        const name = gradeList.find(item => item.Id == GradeId)
        return name.Gradename
      } else {
        return ''
      }
    },
    // 章节清空逻辑
    clearAll(){
      this.throwObj.chapterList = []
      this.defaultExpandedKeys = []
      this.defaultCheckedKeys = []

    },
    // 该方法只在资源下题目资源模块中使用
    setLevel(val){
      this.throwObj.level = val
      this.getChild()
    },
    // 传参调用兄弟组件难度接口
    setEventBus(){
      eventBus.$emit('getDifficulty',this.throwObj)
    },
  }
}
</script>

<style lang="less" scoped>
.span-ellipsis {
  width: 100%;
  //overflow: hidden;
  //white-space: nowrap;
  //text-overflow: ellipsis;
  margin-right: 20px;
  font-size: 20px;
}
.divGal{
  height: 100%;
  position: relative;
}
.cardList{
  width: 100%;
  .academicYear,.semester,.grade{
    width: 100%;
    display: flex;
    margin: 10px 0;
    .txt{
      width: 14%;
      font-size: 18px;
    }
    .btnList{
      width: 86%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .el-button{
        color: #6e6e6e;
        font-size: 18px;
        padding: 0 0 10px 0;
      }
    }
  }
}
.headDis{
  display: flex;
  align-items: center;
  background-color: #7fb99a;
  padding: 10px 0 10px 12px;
  img{
    width: 18px;
    height: 22px;
  }
  .txtList{
    display: flex;
    align-items: center;
    margin-left: 10px;
    >div{
      font-size: 20px;
      color: #FFFFFF;
    }
    >i{
      font-size: 18px;
      //margin: 0 2px;
      color: #FFFFFF;
    }
  }
}
.active {
  color: #7fb99a !important;
}
// 章节结构
.childList{
  height: 87%;
  padding: 0 14px;
  overflow: auto;
  h1{
    font-size: 18px;
    margin: 4px 0;
  }
  /deep/.el-tree{
    .el-tree-node{
      margin: 4px 0;
    }
    .el-tree-node>.el-tree-node__children {
      overflow: visible;
      background-color: transparent;
    }
    .el-tree-node__label {
      font-size: 20px;
    }
    .el-checkbox{
      //zoom: 130%;  //适配谷歌   不适配火狐
      zoom: var(--zoom);  //适配谷歌   不适配火狐
    }
  }
}
// 底部
.MenuFooter{
  width: 100%;
  color: #fff;
  padding: 13px 10px;
  display: flex;
  justify-content: right;
  position: absolute;
  bottom: 0;
  left: 0;
  i{
    color: #999;
    font-size: 30px;
    cursor: pointer;
  }
}
</style>