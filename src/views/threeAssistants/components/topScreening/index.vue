<template>
  <div class="searchList" ref="searchList">
    <!-- <div v-show="showTop">
      <div class="resoStyle" v-show="(tabsActive == 3 || tabsActive == 4) || $store.state.LoginSource == 1">
        <el-button
          type="text"
          v-for="item in resource"
          :class="{active: item.value == throwObj.Level}"
          :key="item.value"
          @click="allClick('Level',item)">
          {{item.text}}
        </el-button>
      </div>
    </div> -->
    <div v-show="showBottom" class="listData">
      <div class="allStyle" v-show="(tabsActive == 3 || tabsActive == 4) || $store.state.LoginSource == 1">
        <div>类型：</div>
        <el-button
          type="text"
          v-for="item in resource"
          :class="{active: item.value == throwObj.Level}"
          :key="item.value"
          @click="allClick('Level',item)">
          {{ item.text }}
        </el-button>
      </div>
      <div class="allStyle" v-show="tabsActive == 6">
        <div>来源：</div>
        <el-button
          type="text"
          v-for="item in resource"
          :class="{active: item.value == throwObj.Level}"
          :key="item.value"
          @click="allClick('Level',item)">
          {{ item.text }}
        </el-button>
      </div>
      <!--   专科专练分类   -->
      <div class="allStyle" v-if="$store.state.LoginSource == 1 && throwObj.Level != 2">
        <div>分类：</div>
        <el-button
          type="text"
          v-for="item in newClassify"
          :class="{active: item.value == throwObj.ShareType}"
          :key="item.value"
          v-show="item.restrictions.includes(throwObj.Level)"
          @click="specialistTraining(item)">
          {{ item.label }}
        </el-button>
      </div>
      <div class="allStyle">
        <div>题型：</div>
        <el-button
          type="text"
          v-for="item in typeOfQuestion"
          :class="{active: item.QuestionType == throwObj.QuestionType}"
          :key="item.QuestionType"
          @click="allClick('QuestionType',item)">
          {{ item.QuestionName }}
        </el-button>
      </div>
      <div class="allStyle">
        <div>难度：</div>
        <el-button
          type="text"
          v-for="item in difficulty"
          :class="{active: item.value == throwObj.DifficultyLevel}"
          :key="item.code"
          @click="allClick('DifficultyLevel',item)">
          {{ item.text }}
        </el-button>
      </div>
      <!--   三个助手分类   -->
      <div class="allStyle" v-if="throwObj.Level == 3 && tabsActive != 6 && $store.state.LoginSource == 2">
        <div>分类：</div>
        <el-button
          type="text"
          v-for="item in classify"
          :class="{active: item.value == throwObj.ShareType}"
          :key="item.value"
          @click="allClick('ShareType',item)">
          {{ item.label }}
        </el-button>
      </div>
      <!-- <div class="allStyle" v-show="throwObj.Level == 2 && tabsActive != 6">
        <div>发布状态：</div>
        <el-button
          type="text"
          v-for="item in issueList"
          :class="{active: item.value == throwObj.State}"
          :key="item.value"
          @click="allClick('State',item)">
          {{ item.label }}
        </el-button>
      </div> -->
      <div class="allStyle" v-show="tabsActive == 6">
        <div>班级：</div>
        <el-button
          type="text"
          v-for="item in classData"
          :class="{active: item.ClassId == ClassId}"
          :key="item.ClassId"
          @click="classClick(item.ClassId)">
          {{ item.ClassName }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import eventBus from '@/views/Teacher/eventBus/eventBus'
export default {
  name: 'TopScreening',
  props: {
    showTop: {
      type: Boolean,
      default: false
    },
    showBottom: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabsActive: '', // 区分页面tab的切换
      difficulty: [], // 难度数据
      resource: [], // 资源数据
      newResource: [
        { text: '全部', value: 0 },
        { text: '市级资源', value: 1 },
        { text: '校本资源', value: 2 },
        { text: '我的资源', value: 3 },
      ], // 高频错题选项
      typeOfQuestion: [], // 题型数据
      classData: [], // 班级数据
      classify: [
        { label: '全部', value: 0 },
        // { label: '学校共享', value: 1 },
        { label: '他人共享', value: 3 },
        { label: '我的共享', value: 4 },
      ],
      issueList:[
        { label: '已发布', value: 1 },
        { label: '未发布', value: 0 }
      ],
      newClassify: [
        { label: '全部', value: 0, restrictions: [3, 4, 5] },
        { label: '校建', value: 1, restrictions: [3] },
        { label: '教师共享', value: 2, restrictions: [3] },
        { label: '我的共享', value: 4, restrictions: [3] },
        { label: '市建', value: 5, restrictions: [5] },
        { label: '市共享', value: 6, restrictions: [5] },
        { label: '区建', value: 7, restrictions: [4] },
        { label: '区共享', value: 8, restrictions: [4] },
        { label: '教材资源', value: 9, restrictions: [3] },
      ],
      throwObj:{
        Level: sessionStorage.getItem('Level') * 1 || 2, // 资源类型
        DifficultyLevel: '-1', // 难度
        ShareType: 0, // 分类
        State: 0, // 发布
        QuestionType: -1 // 题型
      },
      ClassId: '',
    }
  },
  async created() {
    await this.getQuestionType()
    await this.getResourceList()
    this.initBoxHeight()
    this.tabsActive = sessionStorage.getItem('tabsActive')
  },
  mounted() {
    eventBus.$on('getDifficulty',(res) => {
      // const { gradeActive, yearActive, semesterActive } = res
      const { Year, GradeId } = this.$store.state.chapterStorage.ChapterObj
      if(sessionStorage.getItem('tabsActive') != 5){
        this.getDifficulty(Year, GradeId)
      }
      if(sessionStorage.getItem('tabsActive') == 6){
        this.GetTeachClass()
      }
    })
  },
  methods:{
    initBoxHeight(){
      this.$nextTick(()=>{
        this.$emit('updateHeight',this.$refs.searchList.offsetHeight)
      })
    },
    // 点击事件汇总
    allClick(type,val){
      switch (type) {
        case 'Level':
          this.throwObj.Level = val.value
          this.throwObj.ShareType = 0
          this.initBoxHeight()
          // 只做章节的切换
          this.$emit('updateLeftChapters',this.throwObj)
          break;
        case 'QuestionType':
          this.throwObj.QuestionType = val.QuestionType
          break;
        case 'DifficultyLevel':
          this.throwObj.DifficultyLevel = val.value
          break;
        case 'ShareType':
          this.throwObj.ShareType = val.value
          break;
        case 'State':
          this.throwObj.State = val.value
          break;
      }
      this.$emit('screeningEmit',this.throwObj)
    },
    // 专科专练分类事件
    specialistTraining(val){
      if(val.value == 9 && sessionStorage.getItem('IsJCLXC') == 0){
        this.$alert('您的学校暂未申请开通教材资源，详情请咨询客服人员:021-54486816', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            return false
          }
        });
        return
      } else {
        this.throwObj.ShareType = val.value
        this.$emit('screeningEmit',this.throwObj)
      }
    },
    // 班级点击事件
    classClick(val){
      this.ClassId = val
      this.$emit('classClick',val)
    },
    // 获取资源列表接口
    async getResourceList(){
      const {data:res} = await this.$uwonhttp.get('/Question/Question/GetShowMenu?roleId='+localStorage.getItem('role'))
      if(res.Success){
        // 高频错题列表
        // console.log(this.tabsActive)
        // console.log(sessionStorage.getItem('tabsActive'))
        if(sessionStorage.getItem('tabsActive') == 6){
          this.resource = this.newResource
          this.throwObj.Level = 0
        } else {
          this.resource = res.Data
          // this.throwObj.Level = sessionStorage.getItem('Level') * 1 || res.Data[0].value
          this.throwObj.Level = sessionStorage.getItem('Level') * 1 || 2
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取题型接口
    async getQuestionType(){
      const {data:res} = await this.$uwonhttp.get('/Question/Question/GetQuestionType')
      if(res.Success){
        const newList = [{ QuestionType: -1, QuestionName: '全部'},...res.Data]
        this.typeOfQuestion = newList
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取难度接口
    async getDifficulty(year, grade){
      const {data:res} = await this.$uwonhttp.get('/Question/Question/GetDifficulty?year='+year+'&grade='+grade)
      if(res.Success){
        const newList = [{ value: '-1', text: '全部'},...res.Data]
        this.difficulty = newList
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取教师班级
    async GetTeachClass(){
      const { Year, GradeId } = this.$store.state.chapterStorage.ChapterObj
      const { data:res } = await this.$uwonhttp.get(`/Class/TeacherClassManager/GetTeachClass?UserId=${localStorage.getItem('UserId')}&grade=${GradeId}&year=${Year}`)
      // console.log(res)
      if(res.Success){
        this.classData = res.Data
        this.ClassId = res.Data[0].ClassId
        this.$emit('classClick',this.ClassId)
      } else {
        this.$message.error(res.Msg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.searchList{
  background-color: #FFFFFF;
  margin-bottom: 6px;
  .resoStyle{
    display: flex;
    align-items: center;
    padding: 13px 20px 13px;
    border-bottom: 1px solid #DCDCDCFF;
    .el-button{
      padding: 0;
      font-size: 20px;
      font-weight: bold;
      color: #6e6e6e;
    }
  }
  .listData{
    padding: 8px 20px 8px;
    .allStyle{
      display: flex;
      align-items: center;
      margin: 4px 0;
      >div{
        font-size: 20px;
      }
      .el-button{
        padding: 4px 0;
        font-size: 18px;
        color: #6e6e6e;
      }
    }
  }
}
.active{
  color: #7fb99a !important;
}
</style>