<template>
  <div class="LineStemEdit">
    <ul class="edit_item">
      <!-- <li class="font_size_16 flex flex_align_center"><b class="before_icon font_color_333"></b><b>连线项设置</b></li> -->
      <li class="flex show_li">
        <p>
          <el-radio v-model="axis" label="y">竖向排版</el-radio>
          <el-radio v-model="axis" label="x">横向排版</el-radio>
        </p>
        <p>
          <el-radio v-model="orderNumShow" :label="1">显示序号</el-radio>
          <el-radio v-model="orderNumShow" :label="0">不显示序号</el-radio>
        </p>
        <p>
          <el-radio v-model="Mode" :label="1">单项连线</el-radio>
          <el-radio v-model="Mode" :label="2">多项连线</el-radio>
        </p>
      </li>
      <!-- 内容设置区域 -->
      <li class="flex">
        <ul class="line_col flex flex_align_center">
          <li class="col_name font_size_20 margin_r_15"><b>母列</b></li>
          <li style="margin-left: 15px">
            <el-select v-model="mrOrn" @change="setOrderNum('mr')" placeholder="请选择母列序号类型" size="small">
              <el-option
                class="font_size_20"
                v-for="(orn, index) in orderNumType"
                :key="index + 'orn'"
                :label="orn.text"
                :value="orn.val"
              ></el-option>
            </el-select>
          </li>
          <li><el-button size="small" @click="addCol('mr')" type="primary">+添加母列</el-button></li>
        </ul>
        <ul class="line_col flex flex_align_center">
          <li class="col_name font_size_20"><b>子列</b></li>
          <li style="margin-left: 15px">
            <el-select v-model="crOrn" @change="setOrderNum('cr')" placeholder="请选择子列序号类型" size="small">
              <el-option
                class="font_size_20"
                v-for="(orn, index) in orderNumType"
                :key="index + 'orn'"
                :label="orn.text"
                :value="orn.val"
              ></el-option>
            </el-select>
          </li>
          <li><el-button size="small" @click="addCol('cr')" type="primary">+添加子列</el-button></li>
        </ul>
      </li>
      <li class="flex">
        <!-- 母列 -->
        <div class="line_col">
          <ul class="flex margin_r_15" v-for="(mr, index) in motherRows" :key="index + 'mr'">
            <li class="edit_box flex_item_grow font_size_20" style="width: 93%">
              <div class="flex flex_space_between">
                <div>
                  <b>{{ mr.label }}</b>
                  <span style="color: #61bb96">
                    连接项：{{ mr.answerLabel.length > 0 ? mr.answerLabel.join('|') : '未设置' }}
                  </span>
                </div>
                <div class="flex edit_btn">
                  <span @click="moveUp('mr', index)">上移</span>
                  <span @click="moveDown('mr', index)">下移</span>
                  <span @click="deleteCol('mr', index)">删除</span>
                </div>
              </div>
              <UWEditor
                class="edit_content"
                :height="105"
                :idx="'mr' + ',' + index"
                :content="mr.content"
                @updateEditItem="updateEditItem"
                @editorStateChange="getFocus"></UWEditor>
              <!-- <div class="edit_content" @click="getFocus('mr', mr, index)">
                <span v-html="mr.content" v-katex></span>
              </div> -->
            </li>
            <li class="edit_target font_color_white">
              <el-popover trigger="click" placement="bottom" width="120">
                <el-select
                  multiple
                  :multiple-limit="Mode == 1 ? 1 : 0"
                  v-model="mr.answer"
                  @change="changeLineItem($event, index)"
                  clearable
                >
                  <!-- :value="cr.label" -->
                  <el-option
                    class="font_size_20"
                    style="width: 100px"
                    v-for="(cr, index) in childRows"
                    :key="index + 'or_a'"
                    :value="cr.timeStamp"
                    :label="cr.label + '项'"
                  ></el-option>
                </el-select>
                <span slot="reference" class="cursor_pointer">连接项设置</span>
              </el-popover>
            </li>
          </ul>
        </div>
        <!-- 子列 -->
        <div class="line_col">
          <ul class="flex" v-for="(cr, index) in childRows" :key="index + 'cr'">
            <li class="edit_box flex_item_grow font_size_20">
              <div class="flex flex_space_between">
                <div>
                  <b>{{ cr.label }}</b>
                </div>
                <div class="flex edit_btn">
                  <span @click="moveUp('cr', index)">上移</span>
                  <span @click="moveDown('cr', index)">下移</span>
                  <span @click="deleteCol('cr', index)">删除</span>
                </div>
              </div>
              <UWEditor
                class="edit_content"
                :height="105"
                :idx="'cr' + ',' + index"
                :content="cr.content"
                @updateEditItem="updateEditItem"
                @editorStateChange="getFocus"></UWEditor>
              <!-- <div class="edit_content" @click="getFocus('cr', cr, index)">
                <span v-html="cr.content" v-katex></span>
              </div> -->
            </li>
          </ul>
        </div>
        <!-- 删除|添加一项，包含母列和子列 -->
        <div>
          <div style="width: 70px; height: 141px; padding: 35px 30px" v-for="len in rowsBtnLen" :key="len">
            <p style="margin-bottom: 6px"><el-button size="small" @click="deleteRow(len)">删 除</el-button></p>
            <p><el-button type="primary" size="small" @click="addRow(len)">添 加</el-button></p>
          </div>
        </div>
      </li>
    </ul>
    <!-- <EditDialog ref="EditDialog" @updateEditItem="updateEditItem" @clearLocal="clearLocal"></EditDialog> -->
  </div>
</template>
<script>
//编辑项最多16个连线项
import UWEditor from '@/views/threeAssistants/UWEditor'
import EditDialog from './EditDialog.vue'
import orderNum from './orderNum'

export default {
  name: 'LineStemEdit',
  components: {
    EditDialog,
    UWEditor
  },
  data() {
    return {
      axis: 'y', //排版
      orderNumShow: 1, //是否显示序号
      Mode: 1, //1-单项连线 2-多项连线
      rowTotal: 0, //总行数,取母列或者子列最多的
      motherRows: [
        //母列
        {
          label: '（1）',
          content: '',
          answer: [],
          answerLabel: [],
        },
      ],
      childRows: [
        //子列
        {
          label: 'A',
          content: '',
          timeStamp: new Date().getTime(),
        },
      ],
      currentEdit: {
        // 当前编辑项
        name: '',
        index: 0,
      },
      orderNumType: orderNum.orderNumType, //序号类型
      mrOrn: 'bracketNum', //母列选中的序号类型
      mrOrnList: orderNum.bracketNum, //母列序号值
      crOrn: 'letterUpper', //子列选中的序号类型
      crOrnList: orderNum.letterUpper, //子列序号值
      answer: [], //答案
    }
  },
  computed: {
    rowsBtnLen() {
      return Math.min(this.motherRows.length, this.childRows.length)
    },
  },
  watch: {
    Mode: {
      handler(nv, ov) {
        if (ov == 2 && nv == 1) {
          //清空答案设置:当从多项连线更改为单项连线时
          this.motherRows.forEach((item) => {
            item.answer = []
            item.answerLabel = []
          })
        }
      },
    },
  },
methods: {
    /**
     * @description: 题库中获取连线题信息
     * @return {*}
     */  
     newInit({Answer,SpecialItemConfig}){
      console.log(Answer,SpecialItemConfig)
      Object.assign(this.$data, this.$options.data())
      let info = JSON.parse(SpecialItemConfig).linkExamData
        let keys = Object.keys(info)
        keys.forEach((item) => {
          this[item] = info[item]
        })
        if (keys.indexOf('Mode') == -1) {
          if (info.motherRows.find((item) => item.answer.length > 1)) return (this.Mode = 2)
          this.Mode = 1
        }
        this.mrOrnList = orderNum[this.mrOrn]
        this.crOrnList = orderNum[this.crOrn]
    },
    //组件初始化
    init(itemId) {
      Object.assign(this.$data, this.$options.data())
      if (itemId) this.GetLigatureItem(itemId)
    },
    //上交数据
    handInData() {
      this.organizeAnswer()
      return {
        axis: this.axis,
        orderNumShow: this.orderNumShow,
        rowTotal: this.rowTotal,
        motherRows: this.motherRows,
        childRows: this.childRows,
        answer: this.answer,
        mrOrn: this.mrOrn,
        crOrn: this.crOrn,
      }
    },
    // 组织上交的数据
    organizeAnswer() {
      this.rowTotal = Math.max(this.motherRows.length, this.childRows.length)
      this.answer = this.motherRows
        .filter((item) => item.answerLabel.length)
        .map((item) => {
          return {
            [item.label]: item.answerLabel.join('|'),
          }
        })
    },
    //获取连线题信息
    async GetLigatureItem(ItemId) {
      const { Data } = await this.$http.post('/Paper/Exam_Item/GetLigatureItem', { ItemId })
      if (Data !== null) {
        let info = JSON.parse(Data.Config).linkExamData
        let keys = Object.keys(info)
        keys.forEach((item) => {
          this[item] = info[item]
        })
        if (keys.indexOf('Mode') == -1) {
          if (info.motherRows.find((item) => item.answer.length > 1)) return (this.Mode = 2)
          this.Mode = 1
        }
        this.mrOrnList = orderNum[this.mrOrn]
        this.crOrnList = orderNum[this.crOrn]
      }
    },
    //母列每一项对应连接项调整
    changeLineAll() {
      this.motherRows.forEach((item, index) => {
        this.getLabel(item.answer, index)
      })
    },
    //根据子列项的时间戳获取对应的label
    getLabel(time, index) {
      let result = []
      let result_answer = []
      time.forEach((item, i) => {
        let cr = this.childRows.find((row) => row.timeStamp == item)
        if (cr) {
          result.push(cr.label)
          result_answer.push(cr.timeStamp)
        }
      })
      this.motherRows[index].answerLabel = result
      this.motherRows[index].answer = result_answer
    },
    //设置连接项
    changeLineItem(e, index) {
      this.getLabel(e, index)
    },
    //设置连接项时根据选项返回子列对应索引
    returnCrIndex(params) {
      let result = []
      params.forEach((item) => {
        let index = this.childRows.findIndex((row) => row.timeStamp == item)
        index != -1 ? result.push(index) : ''
      })
      return result
    },
    // 设置序号
    setOrderNum(type) {
      let o_type = type == 'mr' ? 'mrOrn' : 'crOrn'
      if (type == 'mr') this.mrOrnList = orderNum[this[o_type]]
      if (type == 'cr') this.crOrnList = orderNum[this[o_type]]
      this.reOrder(type)
    },
    //序号重排
    reOrder(type) {
      let rows = type == 'mr' ? this.motherRows : this.childRows
      let ornList = type == 'mr' ? this.mrOrnList : this.crOrnList
      rows.forEach((row, index) => {
        row.label = ornList[index]
      })
      this.changeLineAll()
    },
    //赋值连线项
    updateEditItem(data) {
      let c_edit = this.currentEdit
      let rows = c_edit.name == 'mr' ? this.motherRows : this.childRows
      rows[c_edit.index].content = data
    },
    //打开编辑弹窗
    getFocus(item, index) {
      // this.currentEdit.name = type
      // this.currentEdit.index = index
      // let title = type == 'mr' ? '母列' : '子列'
      // title += item.label
      // this.$refs.EditDialog.init(title, item.content)

      const ind = index.split(',')
      this.currentEdit.name = parseInt(ind[0]) // 母列或子列
      this.currentEdit.index = parseInt(ind[1]) // 第几空
    },
    //添加母列或子列某一项
    addCol(type, i) {
      let rows = type == 'mr' ? this.motherRows : this.childRows
      let p = i ? i + 1 : rows.length
      let obj = {
        label: `（${p + 1}）`,
        content: '',
      }
      if (type == 'mr') Object.assign(obj, { answer: [], answerLabel: [] })
      if (type == 'cr') Object.assign(obj, { timeStamp: new Date().getTime() }) //设置子列的唯一性
      rows.splice(p, 0, obj)
      this.reOrder(type)
    },
    // 删除母列或子列某一项
    deleteCol(type, i) {
      let rows = type == 'mr' ? this.motherRows : this.childRows
      rows.splice(i, 1)
      this.reOrder(type)
      // 序号要重排
    },
    //删除整项
    deleteRow(i) {
      this.motherRows.splice(i - 1, 1)
      this.childRows.splice(i - 1, 1)
      this.reOrder('mr')
      this.reOrder('cr')
      //序号重排
    },
    // 增加整项
    addRow(i) {
      this.addCol('mr', i)
      this.addCol('cr', i)
    },
    // 上移
    moveUp(type, i) {
      let rows = type == 'mr' ? this.motherRows : this.childRows
      let item = rows[i]
      rows.splice(i, 1)
      rows.splice(i - 1, 0, item)
      this.reOrder(type)
    },
    //下移
    moveDown(type, i) {
      let rows = type == 'mr' ? this.motherRows : this.childRows
      let item = rows[i]
      rows.splice(i, 1)
      rows.splice(i + 1, 0, item)
      this.reOrder(type)
    },
    //清除数据
    clearLocal() {
      this.currentEdit = {
        name: '',
        index: 0,
      }
    },
  },
}
</script>
<style lang="less" scoped>
@import './common';
.before_icon {
  width: 5px;
  height: 20px;
  background: #61bb96;
  border-radius: 2px;
  margin-right: 5px;
}
.edit_item {
  margin-bottom: 25px;
}
.show_li {
  margin: 15px 0;
  p {
    margin-right: 70px;
  }
}
.line_col {
  width: 45%;
  li {
    margin: 0px 5px 10px 0px;
  }
  .edit_target {
    width: 6%;
    //padding: 5px;
    font-size: 20px;
    text-align: center;
    background: #61bb96;
    border-radius: 4px;
    //margin-top: 25px;
    //cursor: pointer;
  }
}
.edit_content {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 105px;
  margin-top: 5px;
  padding: 5px;
  overflow: scroll;
}
.edit_btn {
  span {
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>
