<template>
  <div class="topic">
    <div class="topicHeader topicDisFlex" v-show="headerState">
      <i class="el-icon-arrow-left" style="cursor: pointer" @click="goBack"></i>
      <p>{{ questionId ? '编辑' : '新增' }}题目</p>
    </div>
    <div class="topicCont topicDisFlex">
      <!--   题型选择区   -->
      <div class="left">
        <left-radio ref="leftRadio" :radio="Public.QuestionTypeId" @radioBtn="leftRadioBtn"></left-radio>
      </div>
      <!--   题干信息录入区   -->
      <div class="right">
        <div class="top topicDisFlex">
          <div class="_left topicMr">
            <public-textarea txt="题干" :must-have="true" :content.sync="tiganJiexi.Title" :itemId="Public.QuestionTypeId"></public-textarea>
            <!--            <public-textarea txt="题目解析" :must-have="true" :content.sync="tiganJiexi.Analysis" class="topicMt"></public-textarea>-->
          </div>
          <div class="_right">
            <tit v-show="![36,26].includes(Public.QuestionTypeId)" txt="答案" :MustHave="true"></tit>
            <!-- 连线题 -->
            <line-stem ref="lineStem" v-if="Public.QuestionTypeId === 50"></line-stem>
            <!-- 填空 -->
            <fill-spaces
              v-if="Public.QuestionTypeId === 5"
              ref="fillSpaces"
              :title="tiganJiexi.Title"
              :RelevanceErrors="RelevanceErrors"
              :Answer="solution.Answer"
              :OtherAnswer="solution.OtherAnswer"
              :CuoYinList="CuoYinList"
            ></fill-spaces>
            <!-- 单选、多选 -->
            <three-basics-item
              v-if="[2, 10].includes(Public.QuestionTypeId)"
              ref="threeBasicsItem"
              :answer="solution.Answer"
              :errors="solution.Errors"
              :type-id="Public.QuestionTypeId"
              :CuoYinList="CuoYinList"
            ></three-basics-item>
            <!-- 判断 -->
            <pan-duan
              v-if="Public.QuestionTypeId === 11"
              :answer="solution.Answer"
              :CuoYinList="CuoYinList"
              :errors="solution.Errors"
              ref="panDuan"
            ></pan-duan>
            <!-- 主观 -->
            <public-textarea
              v-if="Public.QuestionTypeId === 36"
              txt="参考答案"
              :MustHave="false"
              :content.sync="Public.ReferenceAnswer"
            ></public-textarea>
            <public-subjective
              v-if="Public.QuestionTypeId === 36"
              ref="publicSubjective"
              :answer-form.sync="Public.ResponseFormat"
            ></public-subjective>
            <!--  错因汇总 -->
            <!--            <public-error v-if="[5,10].includes(Public.QuestionTypeId)" title-name="错因汇总" class="topicMt" :err.sync="ErrorCollect"></public-error>-->
            <!--   作文题   -->
            <zuo-wen
              v-if="Public.QuestionTypeId === 26"
              :ItemTypeId="Public.QuestionTypeId"
              ref="zuoWen"></zuo-wen>
          </div>
        </div>
        <!--    中部信息录入部分    -->
        <div class="cont">
          <encoding-system1
            class="encodingSystem1"
            :course-versions-id="centralAll.CourseVersionsId"
            :difficulty-id="centralAll.DifficultyId"
            :learning-level-id="centralAll.LearningLevelId"
            :theme-id="centralAll.ThemeId"
            :LearningGuidance="centralAll.LearningGuidance"
            v-on:changeInput="setCentralAll"
          ></encoding-system1>
          <div class="topicDisFlex topicMt" style="width: 50%">
            <!--            <public-textarea txt="学习指导" style="width: 50%" :content.sync="centralAll.LearningGuidance" class="topicMr"></public-textarea>-->
            <!--            <public-textarea txt="错误归因" style="width: 50%" :content.sync="centralAll.Misattribution"></public-textarea>-->
            <upload-audio-video
              ref="uploadAudioVideo"
              :show-obj="centralAll.GuidanceFile"
              title="辅导"
              @getAudioVadeo="getAudioVadeo"
            ></upload-audio-video>
          </div>
        </div>
        <!--    编码部分    -->
        <encoding-system2
          ref="encodingSystem2"
          class="encodingSystem2"
          v-show="showHiden"
          :QuestionTypeId="Public.QuestionTypeId"
          :unit-target-id="botmAll.UnitTargetId"
          :chapter-target-id="botmAll.ChapterTargetId"
          :question-target-id="botmAll.QuestionTargetId"
          :core-literacy-id="botmAll.CoreLiteracyId"
          :question-source="botmAll.QuestionSource"
          :finish-time="botmAll.FinishTime"
          :photograph-set="botmAll.PhotographSet"
          :scene-attribute-id="botmAll.SceneAttributeId"
          :analysis="tiganJiexi.Analysis"
          :misattribution="centralAll.Misattribution"
          :question-code="botmAll.QuestionCode"
          :content-domain-id="botmAll.ContentDomainId"
          :theme-id="botmAll.ThemeId"
          :AnalysisFile="centralAll.AnalysisFile"
          @setAnalysisFile="
          (val) => {
            centralAll.AnalysisFile = val
          }"
          @centralAllMisattribution="
            (val) => {
              centralAll.Misattribution = val
            }
          "
          @tiganAnalysis="
            (val) => {
              tiganJiexi.Analysis = val
            }
          "
          @setTime="
            (val) => {
              botmAll.FinishTime = val
            }
          "
          @setUnit="
            (val) => {
              botmAll.UnitTargetId = val
            }
          "
          v-on:changeInput="setBotmAll"
        ></encoding-system2>
        <el-divider content-position="left">
          <el-button class="OpenAndClose" type="text" @click="encodingShowHide"
            >{{ showHiden ? '收起' : '展开' }}题目属性<i
              :class="[showHiden ? 'el-icon-arrow-up' : 'el-icon-arrow-down', 'el-icon--right']"
            ></i
          ></el-button>
        </el-divider>
      </div>
    </div>
    <!--  底部事件操作区  -->
    <div style="display: flex; justify-content: center">
      <el-button type="primary" v-show="headerState" @click="inputItem">录入题目</el-button>
    </div>
    <!--  教师针对题库分享  -->
    <shared-settings ref="sharedSettings" :QuestionTypeId="Public.QuestionTypeId" @requestInterface="setSharedSettings"></shared-settings>
    <!--  市级管理员针对试题的分享  -->
    <dialog-com ref="dialogCom" title="授权范围" :timeShow="false" @authorization="goNewJobResources"></dialog-com>
  </div>
</template>

<script>
import tit from '@/views/threeAssistants/AddANewTopic/components/module/tit'
import leftRadio from '@/views/threeAssistants/AddANewTopic/components/leftRadio'
import panDuan from '@/views/threeAssistants/AddANewTopic/components/module/panDuan'
import fillSpaces from '@/views/threeAssistants/components/edit/editAnswer/fillSpaces'
import threeBasicsItem from '@/views/threeAssistants/components/edit/editAnswer/threeBasicsItem'
import publicTextarea from '@/views/threeAssistants/AddANewTopic/components/module/publicTextarea'
import encodingSystem2 from '@/views/threeAssistants/AddANewTopic/components/rightCom/encodingSystem2'
import publicSubjective from '@/views/threeAssistants/AddANewTopic/components/module/publicSubjective'
import encodingSystem1 from '@/views/threeAssistants/AddANewTopic/components/rightCom/encodingSystem1'
import uploadAudioVideo from '@/views/threeAssistants/AddANewTopic/components/rightCom/uploadAudioVideo'
import publicError from '@/views/threeAssistants/AddANewTopic/components/module/publicError'
import sharedSettings from '@/views/threeAssistants/AddANewTopic/components/module/sharedSettings'
import lineStem from '@/views/threeAssistants/components/lineStem'
import dialogCom from '@/views/threeAssistants/components/authorization/dialogCom'
import zuoWen from '@/views/threeAssistants/AddANewTopic/components/module/zuoWen.vue'
export default {
  name: 'index',
  props: {
    headerState: {
      type: Boolean,
      default() {
        return true
      },
    },
    genjin: {
      type: Boolean,
      default:false
    },
  },
  data() {
    return {
      onLineAnswer:'',// 连线题的答案
      /*
       * 提交信息*/
      Public: {
        QuestionTypeId: 0, // 题目类型Id
        Year: this.$route.query.Year, //学年
        Semester: this.$route.query.Semester, // 学期
        GradeId: this.$route.query.GradeId, // 年级Id
        SubjectId: localStorage.getItem('UW_SUBJECT'), // 学科Id
        TextbookId: this.$route.query.TextbookId, // 教材Id
        ChapterId: this.$route.query.ChapterId, // 章节Id
        UserId: localStorage.getItem('UserId'), // 创建人Id
        UserRoleId: localStorage.getItem('role'),
        ResponseFormat: 1, // 答题形式
        ShareSet: 1, // 共享设置
        ShareAreaCity: 1, // 共享范围
        Hint: '', // 作答提示
        // SpecialItemConfig: '', // 特殊提醒
        SpecialItemConfig: {
          // 特殊题型传参
          Titles: [],
          IsSNum: 1,
          MaxRow: 8,
          Rows: [],
          AnswerGroups: [],
          Conclusion: '',
          YesConclusion: '',
          ItemConfigOptionAnswer: null,
          SelectOptions: '',
          TableItemInfo: [],
        },
        ReferenceAnswer: '', // 参考答案
      },
      /*错因汇总*/
      ErrorCollect: [],
      /*填空题错因*/
      RelevanceErrors: [
        // {
        //   Answer: '', // 答案
        //   ErrorInfo: [
        //     {
        //       ErrorAnswer: '', // 错误答案
        //       ErrorId: '', // 多错因逗号隔开
        //     }
        //   ], // 错因Id
        // }
      ],
      /* 答案 */
      solution: {
        Answer: '', // 答案
        OtherAnswer: [], // 多答案
        Options: [
          {
            Content: '', // 内容
            Option: '', // 选项
          },
        ], // 选项
        Errors: [
          {
            Content: '', // 内容
            ErrorId: '', // 错因Id
          },
        ], // 错因
      },
      newSolution: {},
      /*题干/解析*/
      tiganJiexi: {
        Id: '', // 编辑时传值，新增传空
        Title: '', // 题干
        Analysis: '略', // 题目解析
      },
      /* 课程/编号/难度/水平 */
      centralAll: {
        CourseVersionsId: '1811293692187680768', // 课程版本Id
        DifficultyId: '', // 题目难度Id
        LearningLevelId: '', // 学习水平Id
        LearningGuidance: '', // 学习指导
        Misattribution: '', // 错误归因
        GuidanceFile: null, // 学习指导
        AnalysisFile: null, // 解析文件
        //   {
        //   FileName: '', // 文件名称
        //   FileUrl: '', // 文件地址
        //   Lecturer: '', // 讲师名称
        //   CoverUrl: '', // 封面URL
        //   Duration: 0, // 时长
        //   FileType: '', // 文件类型，1视频，2音频
        //   UploadingWay: null, // 上传方式，1本地上传，2网址
        // }
      },
      /*单元目标*/
      botmAll: {
        UnitTargetId: '', // 单元目标Id
        ChapterTargetId: '', // 章节目标Id
        QuestionTargetId: '', // 题目目标Id
        CoreLiteracyId: '', // 核心素养Id
        SceneAttributeId: null, // 情景属性Id
        QuestionSource: '', // 题目来源
        PhotographSet: '', // 拍照设置
        FinishTime: 1, // 预计完成时间分钟
        QuestionCode: '', // 题目编号
        ThemeId: '', // 主题Id
        ContentDomainId: '', // 内容领域Id
      },
      questionId: this.$route.query.questionId, //试题Id
      showHiden: false, // 题目编码显示和隐藏
      CuoYinList: [], // 错因数据
    }
  },
  components: {
    tit,
    panDuan,
    lineStem,
    leftRadio,
    fillSpaces,
    publicError,
    sharedSettings,
    publicTextarea,
    encodingSystem1,
    threeBasicsItem,
    encodingSystem2,
    uploadAudioVideo,
    publicSubjective,
    dialogCom,
    zuoWen,
  },
  activated() {
    this.resetObj()
    this.GetWrongCause()
    this.$route.query.questionId && this.getHistoryData()
    if (this.$route.query.QuestionTypeId) {
      this.Public.QuestionTypeId = Number(this.$route.query.QuestionTypeId)
    } else {
      this.leftRadioBtn(2)
    }
  },
  methods: {
    // 编码信息展开与收起
    encodingShowHide() {
      this.showHiden = !this.showHiden
    },
    // 弹窗重新加载组件内试题类型
    getItemType(){
      this.$refs.leftRadio.GetQuestionType()
    },
    leftRadioBtn(val) {
      this.Public.QuestionTypeId = val
      if ([2, 10].includes(val)) {
        const time = setTimeout(() => {
          this.$refs.threeBasicsItem.addEditList()
          clearTimeout(time)
        }, 200)
      }
      this.$refs.uploadAudioVideo.reset()
    },
    goBack() {
      this.$router.go(-1)
    },
    setCentralAll(obj) {
      this.centralAll[obj.key] = obj.value
    },
    setBotmAll(obj) {
      this.botmAll[obj.key] = obj.value
    },
    getAudioVadeo(obj) {
      // this.centralAll.AnalysisFile = obj
      // 处理试题解析音视频
      this.centralAll.GuidanceFile = obj
    },
    // 录入题目事件
    inputItem() {
      const type = this.Public.QuestionTypeId
      if (type === 2 || type === 10) {
        // 单选/多选
        const { Answer, Options, Errors } = this.$refs.threeBasicsItem.getlist()
        this.newSolution = {
          Options,
          Errors,
          Answer: type === 10 ? Answer.join('|') : Answer,
        }
      } else if (type === 11) {
        // 判断题
        const { Answer, Options, Errors } = this.$refs.panDuan.getlist()
        this.newSolution = {
          Answer,
          Options,
          Errors,
        }
      } else if (type === 5) {
        // 填空题
        // 数组转字符串
        this.newSolution = {
          Answer: this.$refs.fillSpaces.getAnswer().join('|'),
          OtherAnswer: this.$refs.fillSpaces.getOtherAnswer(),
          Option: [],
          Errors: [],
        }
        this.RelevanceErrors = this.$refs.fillSpaces.getRelevanceErrors()
      } else if (type === 26) {
        // 作文题
        if(!this.$refs.zuoWen.getDataValue()) return false
        this.newSolution = {
          Answer: '',
          ...this.$refs.zuoWen.getDataValue()
        }
      }
      // 错因汇总数据库处理
      this.ErrorCollect = this.setError(this.ErrorCollect)
      // 判定四项基础题型答案区域是否按后端定义规定完成。
      if (this.checkQuestionCompleteness()) {
        this.$message.error('请完善必填信息')
        return
      }
      if (!this.$route.query.paperId) {
        // 市区校管理员新增资源逻辑
        const data = {
          ShareSet: 1,
          ShareAreaCity: 1,
          PaperName: '',
          PaperType: 1
        }
        if(this.$store.state.Level == '5'){
          this.setSharedSettings(data)
        } else {
          this.$refs.sharedSettings.init()
        }
        // this.$store.state.Level == '5' ? this.$refs.dialogCom.init() : this.$refs.sharedSettings.init()
      } else {
        this.addQuestion()
      }
    },
    // 作业设计窗口的添加试题
    async addQuestion() {
      const { paperId, TextbookId, Semester } = this.$route.query
      // this.Public.SpecialItemConfig = JSON.stringify(this.Public.SpecialItemConfig)
      const Data = {
        ...this.Public,
        ...this.tiganJiexi,
        ...this.centralAll,
        ...this.newSolution,
        ...this.botmAll,
        ErrorCollect: this.ErrorCollect,
        RelevanceErrors: this.RelevanceErrors,

      }
      // if(this.Public.QuestionTypeId !=50) delete Data.Answer
      if(this.Public.QuestionTypeId ==50) Data.Answer = JSON.stringify(this.onLineAnswer)
      const data = {
        PaperId: paperId,
        ItemInfos: [Data],
        UserId: localStorage.getItem('UserId'),
        UserRoleId: localStorage.getItem('role'),
        Semester,
        TextbookId,
      }
      // console.log(data, '：提交data')
      if(this.genjin) return this.$emit("handleData",data)
      const { data: res } = await this.$uwonhttp.post('/Question/Question/HomeworkAddItemInfos', data)
      // console.log(res)
      if (res.Success) {
        this.$emit('itemList', res.Data)
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 弹窗确认事件
    setSharedSettings(obj) {
      const { ShareSet, ShareAreaCity } = obj
      this.Public.ShareSet = ShareSet
      this.Public.ShareAreaCity = ShareAreaCity
      this.Public.UserRoleId = localStorage.getItem('role')
      this.Public.UserId = localStorage.getItem('UserId')
      // this.Public.SpecialItemConfig = JSON.stringify(this.Public.SpecialItemConfig)
      const Data = {
        ...this.Public,
        ...this.tiganJiexi,
        ...this.centralAll,
        ...this.newSolution,
        ...this.botmAll,
        ErrorCollect: this.ErrorCollect,
        RelevanceErrors: this.RelevanceErrors,
      }
      // console.log(Data)
      this.saveQuestion(Data, obj)
    },
    // 接口
    async saveQuestion(Data, obj) {
      if (this.Public.QuestionTypeId == 50) {
        // 连线题会提交answer
        const lineStem = this.$refs.lineStem.handInData()
        const { answer } = lineStem
        Object.assign(Data, {
          answer: JSON.stringify(answer),
        })
      }
      const { data: res } = await this.$uwonhttp.post('/Question/Question/SaveQuestion', Data)
      if (res.Success) {
        this.$refs.sharedSettings.handleClose()
        // return false
        /* 生成作业部分 */
        if (obj.PaperName !== '') {
          // 跳转作业列表页
          const { PaperName, PaperType, IsComposition } = obj
          const { ChapterId, GradeId, Semester, TextbookId, Year } = this.$route.query
          const Parameters = {
            ChapterId,
            GradeId,
            Semester,
            TextbookId,
            Year,
            UserId: localStorage.getItem('UserId'),
            UserRoleId: localStorage.getItem('role'),
            SubjectId: localStorage.getItem('UW_SUBJECT'),
            PaperName,
            PaperType,
            IsComposition, // 是否为作文专项提高
          }
          // 生成试卷
          const { data: resPonse } = await this.$uwonhttp.post('/Question/Question/AssignHomework', Parameters)
          if (resPonse.Success) {
            this.$router.push({
              path: '/threeAssistants/JobDesign/NewJob',
              query: {
                paperId: resPonse.Data,
                id: 100,
                ChapterId,
                GradeId,
                Semester,
                TextbookId,
                Year,
                testQuestionBankIds: res.Data,
              },
            })
          } else {
            this.$message.error(resPonse.Msg)
          }
        } else {
          // 市区校管理员新增资源逻辑
          if(this.$store.state.Level == '5'){
            this.$refs.dialogCom.init(res.Data)
          } else {
            this.goNewJobResources()
          }
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 跳转作业资源页面
    goNewJobResources(){
      this.$router.push({
        path: localStorage.getItem('LoginSource') == 2 ? '/threeAssistants/newJobResources/index' : '/Paper/Exam_MicroLesson/QuestionBankResourceCenter',
      })
      this.$message({
        message: '新增题目成功！',
        type: 'success',
      })
      // 手动清除组件值
      this.clearObj()
    },
    // 检查题目信息是否完整
    checkQuestionCompleteness() {
      const requiredFields = [
        this.tiganJiexi.Title,
        // this.tiganJiexi.Analysis,
        // this.centralAll.CourseVersionsId,
        // this.centralAll.LearningLevelId,
        // this.centralAll.DifficultyId,
        // this.centralAll.ContentDomainId
      ]

      // 根据 QuestionTypeId 决定是否检查答案
      const checkAnswer = [2, 10, 11, 5].includes(this.Public.QuestionTypeId)

      // 如果需要检查答案，将答案添加到必填字段列表中
      const fieldsToCheck = checkAnswer ? [...requiredFields, this.newSolution.Answer] : requiredFields
      // console.log(fieldsToCheck)
      if (!fieldsToCheck.includes('')) {
        if (this.Public.QuestionTypeId == 50) {
          const lineStem = this.$refs.lineStem.handInData()
          const { answer, childRows, motherRows } = lineStem
          const answerLen = answer.length
          const childRowsLen = childRows.length
          const motherRowsLen = motherRows.length
          const flag = answerLen && childRowsLen && motherRowsLen && answerLen == childRowsLen && childRowsLen == motherRowsLen
          // console.log('连线题',lineStem)
          this.onLineAnswer = answer;
          if(!flag) return true
          Object.assign(this.Public.SpecialItemConfig, {
            linkExamData: lineStem,
          })
        }
        this.Public.SpecialItemConfig = JSON.stringify(this.Public.SpecialItemConfig)
        return false
      }
      return true
      // 检查是否有空字符串
      // return fieldsToCheck.includes('');
    },
    setError(arr) {
      return arr.map((item) => {
        return {
          Content: '',
          ErrorId: item,
        }
      })
    },
    // 获取历史数据
    async getHistoryData() {
      let response
      const reg = /[\(]/g;
      const reg2 = /[\)]/g;
      if (!this.$route.query.paperId) {
        // 题库试题历史接口
        response = await this.$uwonhttp.post('/Question/Question/GetQuestionDetails', {
          questionId: this.$route.query.questionId,
        })
      }
      if (this.$route.query.paperId) {
        // 作业试题历史接口
        response = await this.$uwonhttp.post('/Question/Question/HomeworkItemInfoDetails', {
          paperId: this.$route.query.paperId,
          itemId: this.$route.query.questionId,
        })
      }
      // const { data: res } = await this.$uwonhttp.post('/Question/Question/GetQuestionDetails', {
      //   questionId: this.$route.query.questionId
      // })
      if (response.data.Success) {
        const {
          QuestionTypeId,
          Year, //学年
          Semester, // 学期
          GradeId, // 年级Id
          SubjectId, // 学科Id
          TextbookId, // 教材Id
          ChapterId, // 章节Id
          UserId, // 创建人Id
          UserRoleId,
          ResponseFormat, // 答题形式
          ShareSet, // 共享设置
          ShareAreaCity, // 共享范围
          Hint, // 作答提示
          SpecialItemConfig, // 特殊提醒
          ReferenceAnswer,
          Answer, // 答案
          OtherAnswer, // 多答案
          Options, // 选项
          Errors,
          Id, // 编辑时传值，新增传空
          Title, // 题干
          Analysis, // 题目解析
          CourseVersionsId, // 课程版本Id
          QuestionCode, // 题目编号
          DifficultyId, // 题目难度Id
          LearningLevelId, // 学习水平Id
          LearningGuidance, // 学习指导
          Misattribution, // 错误归因
          AnalysisFile, // 解析文件
          GuidanceFile, // 教学指导
          UnitTargetId, // 单元目标Id
          ChapterTargetId, // 章节目标Id
          QuestionTargetId, // 题目目标Id
          ContentDomainId, // 内容领域Id
          ThemeId, // 主题Id
          CoreLiteracyId, // 核心素养Id
          SceneAttributeId, // 情景属性Id
          QuestionSource, // 题目来源
          PhotographSet, // 拍照设置
          FinishTime, // 预计完成时间分钟
          ErrorCollect, // 错因汇总
          RelevanceErrors, // 填空题错因与答案的绑定
        } = response.data.Data
        this.Public = {
          QuestionTypeId, // 题目类型Id
          Year, //学年
          Semester, // 学期
          GradeId, // 年级Id
          SubjectId, // 学科Id
          TextbookId, // 教材Id
          ChapterId, // 章节Id
          UserId, // 创建人Id
          UserRoleId,
          ResponseFormat, // 答题形式
          ShareSet, // 共享设置
          ShareAreaCity, // 共享范围
          Hint, // 作答提示
          SpecialItemConfig: JSON.parse(SpecialItemConfig), // 特殊提醒
          ReferenceAnswer, // 参考答案
        }
        this.tiganJiexi = {
          Id, // 编辑时传值，新增传空
          Title: Title.replace(reg, "（").replace(reg2, "）"), // 题干
          Analysis, // 题目解析
        }
        this.centralAll = {
          CourseVersionsId, // 课程版本Id
          DifficultyId, // 题目难度Id
          LearningLevelId, // 学习水平Id
          LearningGuidance, // 学习指导
          Misattribution, // 错误归因
          GuidanceFile, // 教学指导
          AnalysisFile, // 解析文件
        }
        /*单元目标*/
        this.botmAll = {
          ContentDomainId, // 内容领域Id
          ThemeId, // 主题Id
          QuestionCode, // 题目编号
          UnitTargetId, // 单元目标Id
          ChapterTargetId, // 章节目标Id
          QuestionTargetId, // 题目目标Id
          CoreLiteracyId, // 核心素养Id
          SceneAttributeId: SceneAttributeId === null ? null : Number(SceneAttributeId), // 情景属性Id
          QuestionSource, // 题目来源
          PhotographSet: PhotographSet === null ? '' : PhotographSet + '', // 拍照设置
          FinishTime, // 预计完成时间分钟
        }
        this.solution = {
          Answer, // 答案
          OtherAnswer, // 多答案
          Options, // 选项
          Errors,
        }
        if (QuestionTypeId == 50) {
          this.$nextTick(() => {
            this.$refs.lineStem.newInit({ Answer, SpecialItemConfig })
          })
        }
        this.ErrorCollect = ErrorCollect.map((res) => res.ErrorId)
        this.RelevanceErrors = RelevanceErrors
        if ([2, 10].includes(QuestionTypeId)) {
          const time = setTimeout(() => {
            this.$refs.threeBasicsItem.addEditList(Options)
            clearTimeout(time)
          }, 200)
        }
      } else {
        this.$message.error(response.data.Msg)
      }
    },
    // 对象重置
    resetObj() {
      Object.assign(this.$data, this.$options.data.call(this))
    },
    // 手动清空组件内的值
    clearObj() {
      this.$refs.encodingSystem2.clear()
    },
    // 获取错因
    async GetWrongCause(){
      const { data: res } = await this.$uwonhttp.get('/Question/Question/GetWrongCause?grade='+this.$route.query.GradeId)
      res.Success ? this.CuoYinList = res.Data : this.$message.error(res.Msg)
    }
  },
}
</script>

<style lang="less" scoped>
.OpenAndClose {
  color: #7fb99a;
  font-size: 24px;
}
.topicDisFlex {
  display: flex;
}
.topicMr {
  margin-right: 30px;
}
.topicMt {
  margin-top: 24px;
}
.topic {
  background-color: #ffffff;
  padding-bottom: 30px;
  .topicHeader {
    align-items: center;
    padding: 14px 26px;
    border-bottom: 1px solid #e6e6e6;
    i {
      font-size: 26px;
      margin-right: 20px;
      cursor: pointer;
    }
    p {
      font-weight: normal;
      font-size: 24px;
      color: #333333;
    }
  }
  .topicCont {
    // width: 78%;
    width: 100%;
    padding: 24px 0;
    .left {
      margin-right: 54px;
    }
    .right {
      width: 85%;
      ._left {
        width: 50%;
      }
      ._right {
        width: 50%;
      }
      /deep/.el-textarea {
        //width: 612px;
        width: 100%;
      }
      .cont {
        width: 100%;
        .encodingSystem1 {
          width: 100%;
        }
      }
      .encodingSystem2 {
        width: 100%;
      }
    }
  }
}
/deep/.el-radio{
  .el-radio__label{
    //font-size: 22px !important;
    font-size: var(--font16-22) !important;
  }
}
/deep/.el-button{
  //font-size: 22px !important;
  font-size: var(--font16-22) !important;
}
/deep/.devInput{
  //font-size: 22px !important;
  font-size: var(--font16-22) !important;
}
/deep/.el-input{
  //font-size: 22px !important;
  font-size: var(--font16-22) !important;
}
</style>