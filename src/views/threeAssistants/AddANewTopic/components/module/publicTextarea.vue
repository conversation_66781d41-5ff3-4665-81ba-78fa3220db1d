<template>
  <div class="public-textarea">
    <div style="display: flex;justify-content: space-between;">
      <tit :txt="txt" :MustHave="MustHave"></tit>
      <div v-show="itemId == 5" style="color:#FD9A5B;font-size: 14px;"><i class="el-icon-warning" style="color:#FD9A5B;"></i>填空题通过中文括号，识别答案框</div>
    </div>
    <UWEditor class="uweditor-view" :height="160" :content="newContent" @updateEditItem="getEditData"></UWEditor>
    <!-- <div class="divTextArea" v-html="newContent" v-katex @click="openingRichText(txt,newContent)"></div>
    <EditDialog class="font_size_22" ref="editDialog" @updateEditItem="getEditData"></EditDialog> -->
  </div>
</template>

<script>
import EditDialog from '@/views/Paper/Exam_Paper/Components/EditDialog'
import tit from '@/views/threeAssistants/AddANewTopic/components/module/tit'
import UWEditor from '../../../UWEditor'

export default {
  name: 'questionStem',
  model:{
    prop: 'content',
  },
  props:{
    txt:{
      type:String,
      default:''
    },
    MustHave:{
      type:Boolean,
      default:false
    },
    content:{
      type:String,
      default:''
    },
    itemId:{
      type:Number,
      default:0
    }
  },
  components: {
    tit,
    EditDialog,
    UWEditor
  },
  watch: {
    content(val) {
      this.newContent = val?.replace(/\$\$([^$]*)\$\$/g, (match, p1) => `<span class="math-tex">\\(${p1}\\)</span>`);
      this.textHtml = val
    }
  },
  data() {
    return {
      newContent: this.content,
    }
  },
  created() {
    
  },
  mounted() {
    
  },
  methods:{
    openingRichText(type,content){
      this.$refs.editDialog.init(type,content)
    },
    getEditData(data) {
      this.newContent = data
      this.$emit('update:content', data)
    },
  }
}
</script>

<style lang="less" scoped>
.public-textarea{
  position: relative;
}
.divTextArea{
  width: 100%;
  height: 160px;
  overflow-y: auto;
  border: 1px solid #DCDCDCFF;
  border-radius: 5px;
  padding: 10px;
  font-size: var(--font16-22);
}
.textArea{
  font-size: var(--font16-20);
}
.uweditor-view{
  min-height: 200px;
}
</style>
<style src="@wangeditor/editor/dist/css/style.css" />