<template>
  <div>
    <div style="display: flex;justify-content: space-between;">
      <tit :txt="txt" :MustHave="MustHave"></tit>
      <div v-show="itemId == 5" style="color:#FD9A5B;font-size: 14px;"><i class="el-icon-warning" style="color:#FD9A5B;"></i>填空题通过中文括号，识别答案框</div>
    </div>
    <div class="editor-container" style="height: 180px;">
      <Toolbar
        :editor="editor"
        :default-config="toolbarConfig"
        :mode="mode"
        style="border-bottom: 1px solid #ccc"
        v-bind="$attrs">
      </Toolbar>
      <Editor
        v-model="textHtml"
        :default-config="editorConfig"
        :mode="mode"
        style="flex: 1; height: 0; overflow: auto"
        @onCreated="onCreated"
      />
    </div>
    <el-button
      size="mini"
      icon="el-icon-edit"
      style="color:#7fb99a;font-size:18px;margin-right:8px;"
      @click="$refs.formulaEditor.open()"
    >公式</el-button>
    <FormulaEditor ref="formulaEditor" @insert="handleInsertFormula" />
    <!-- <div class="divTextArea" v-html="newContent" v-katex @click="openingRichText(txt,newContent)"></div>
    <EditDialog class="font_size_22" ref="editDialog" @updateEditItem="getEditData"></EditDialog> -->
  </div>
</template>

<script>
import FormulaEditor from '../../../UWEditor/FormulaEditor.vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import EditDialog from '@/views/Paper/Exam_Paper/Components/EditDialog'
import tit from '@/views/threeAssistants/AddANewTopic/components/module/tit'
import katex from 'katex'
import 'katex/dist/katex.min.css'

export default {
  name: 'questionStem',
  model:{
    prop: 'content',
  },
  props:{
    txt:{
      type:String,
      default:''
    },
    MustHave:{
      type:Boolean,
      default:false
    },
    content:{
      type:String,
      default:''
    },
    itemId:{
      type:Number,
      default:0
    }
  },
  components: {
    tit,
    Editor,
    Toolbar,
    EditDialog,
    FormulaEditor
  },
  watch:{
    content(val){
      this.newContent = val
    }
  },
  data() {
    return {
      newContent: this.content,
      // 富文本
      editor: null,
      textHtml: '',
      toolbarConfig: {
        excludeKeys: ['insertVideo', 'insertImage', 'bgColor', 'fontFamily', 'lineHeight', 'bulletedList', 'numberedList', 'todo', 'group-justify', 'group-indent', 'insertLink', 'insertTable', 'codeBlock', 'divider', 'group-video']
      },
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {
            customUpload: this.onUploadImg
          }
        }
      },
      mode: 'default', // or 'simple'
      showFormulaDialog: false
    }
  },
  mounted() {
    this.renderMathTex();
  },
  updated() {
    this.renderMathTex();
  },
  methods:{
    openingRichText(type,content){
      this.$refs.editDialog.init(type,content)
    },
    getEditData(data) {
      this.newContent = data
      this.$emit('update:content', data)
    },
    renderMathTex() {
      const elements = this.$el.querySelectorAll('span[data-math-tex="1"]');
      elements.forEach(el => {
        const latex = el.textContent.replace(/^\\\(|\\\)$/g, '');
        try {
          katex.render(latex, el, { throwOnError: false });
        } catch (e) {
          el.innerHTML = '<span style="color:#f56c6c;">公式有误</span>';
        }
      });
    },
    /**
     * 富文本编辑
     */
    onCreated(editor) {
      this.editor = Object.seal(editor);
    },
    onEditorChange(html) {
      // 修复被转换的数学公式标签 @onChange="onEditorChange"
        const fixedHtml = html.replace(
          /<span data-slate-string="true">\\((.+?)\\)<\/span>/g,
          '<span data-math-tex="1">\\($1\\)</span>'
        );
        // 更新内容
        this.editor.txt.html(fixedHtml);
        // 触发自定义事件
        this.$emit('update:value', fixedHtml);
    },
    async onUploadImg(file, insertFn) {
      const forData = new FormData()
      forData.append('file', file)
      let resData = await this.$uwonhttp.post('/file/Upload/UploadFileByForm', forData)
      if (resData.status === 200) {
        let image = new Image()
        image.src = resData.data.thumbUrl
        setTimeout(() => {
          this.editor.blur()
        }, 500)
        image.onload = () => {
          insertFn(resData.data.thumbUrl, 'filename', resData.data.thumbUrl)
          this.$nextTick(() => {
            this.editor.focus()
          })
        }
      }
    },
    handleInsertFormula(latex) {
      // this.textHtml = `<span data-math-tex="1">\\(${latex}\\)</span>`
      if (this.editor) {
        console.log('111111', this.editor.getHtml())
        this.editor.focus();
        // const html = `<span class="math-tex">\\(${latex}\\)</span>`;
        const html = `<span data-math-tex="1">\\(${latex}\\)</span>`;
        setTimeout(() => {
          this.editor.dangerouslyInsertHtml(html);
        }, 0);
        console.log('222222', this.editor.getHtml())
        console.log('333333', this.textHtml)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.divTextArea{
  width: 100%;
  height: 160px;
  overflow-y: auto;
  border: 1px solid #DCDCDCFF;
  border-radius: 5px;
  padding: 10px;
  font-size: var(--font16-22);
}
.textArea{
  font-size: var(--font16-20);
}
.editor-container {
    height: 400px;
    border: 1px solid rgb(204, 204, 204);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    &.none {
      border: none;
      flex: auto;
      height: auto;
    }
    // ::v-deep .w-e-scroll {
    //   overflow-y: inherit !important;
    // }
  }
</style>
<style src="@wangeditor/editor/dist/css/style.css" />