<template>
  <el-dialog
    title="共享设置"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose">
    <el-form class="form" :model="form" :rules="rules">
      <el-form-item label="共享" :label-width="formLabelWidth">
        <el-select v-model="form.ShareSet" placeholder="请选择">
          <el-option label="不共享" :value="1"></el-option>
          <el-option label="同校共享" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="form.ShareSet === 2" label=" " :label-width="formLabelWidth">
        <el-radio-group v-model="form.ShareAreaCity">
          <el-radio :label="2">愿意全区共享</el-radio>
          <el-radio :label="3">愿意全市共享</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="!showMakeWork" label=" " :label-width="formLabelWidth">
        <el-checkbox v-model="checked">生成作业</el-checkbox>
      </el-form-item>
      <el-form-item v-show="checked" label="作业名称" prop="PaperName" :label-width="formLabelWidth">
        <el-input v-model="form.PaperName" autocomplete="off"></el-input>
      </el-form-item>
<!--      <el-form-item v-show="checked" label="练习类型" :label-width="formLabelWidth">-->
<!--        <el-select v-model="form.PaperType" disabled placeholder="请选择">-->
<!--          <el-option label="电子作业" :value="1"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item v-show="checked && subject && QuestionType" label="类型" :label-width="formLabelWidth">
        <el-radio-group v-model="form.IsComposition">
          <el-radio :label="0">日常作业</el-radio>
          <el-radio :label="1">作文专项提高</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleClose">取 消</el-button>
      <el-button size="medium" type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'sharedSettings',
  data(){
    return {
      dialogVisible: false,
      checked: false,
      form:{
        ShareSet: 1,
        ShareAreaCity: 1,
        PaperName: '',
        PaperType: 1,
        IsComposition: 0,
      },
      formLabelWidth: '120px',
      rules: {
        PaperName: [
          { required: true, message: '请输入作业名称', trigger: 'blur' }
        ]
      }
    }
  },
  props:{
    QuestionTypeId:{
      type: Number,
      default: 0
    },
    queryInfo: {
      type: Object,
      default: null
    }
  },
  computed:{
    // 判定学科是语文和英语
    subject(){
      return ['1','21','3','23','37','100'].includes(localStorage.getItem('UW_SUBJECT'))
    },
    // 作文题型
    QuestionType(){
      return this.QuestionTypeId === 26
    },
    showMakeWork() {
      return this.$route.query.Redirection || this.queryInfo
    }
  },
  watch:{
    QuestionTypeId(val){
      if(val === 26){
        this.form.IsComposition = 1
      }else{
        this.form.IsComposition = 0
      }
    }
  },
  methods:{
    init(){
      this.form.ShareSet = 1
      this.form.ShareAreaCity = 1
      this.form.PaperName = ''
      this.form.PaperType = 1
      this.dialogVisible = true
    },
    handleClose(){
      this.checked = false
      this.dialogVisible = false
    },
    submit(){
      if(this.checked && this.form.PaperName === '') {
        this.$message.error('请输入作业名称');
        return
      }
      this.$emit('requestInterface',this.form)
      this.handleClose()
    }
  }
}
</script>

<style lang="less" scoped>
.form{
  /deep/.el-input{
    width: 300px;
  }
  /deep/.el-select{
    width: 300px;
  }
}
</style>