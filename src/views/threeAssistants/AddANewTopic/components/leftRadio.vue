<template>
  <div class="topicRdio">
    <h1>题目类型：</h1>
    <el-radio-group v-model="newRadio" :disabled="questionId ? true : false" @input="radioBtn">
      <el-radio-button v-for="(item, ind) in radioList" :key="ind" :label="item.QuestionType">{{ item.QuestionName }}</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
export default {
  name: 'leftRadio',
  props:{
    radio:{
      type: Number,
      default: 2
    }
  },
  watch:{
    radio(val){
      this.newRadio = val == 0 ? 2 : val
      if(val == 0) this.$emit('radioBtn',2)
    }
  },
  data() {
    return {
      radioList: [],
      newRadio: this.radio,
      questionId: '', //试题Id
    }
  },
  activated() {
    this.questionId = this.$route.query.questionId
    this.GetQuestionType()
  },
  methods:{
    radioBtn(e) {
      this.$emit('radioBtn',e)
    },
    // 获取试题类型
    async GetQuestionType() {
      const { data: res } = await this.$uwonhttp.get('/Question/Question/GetQuestionType')
      res.Success ? this.radioList = res.Data : this.$message.error(res.Msg)
      if(res.Success){
        this.radioList = res.Data
        // this.$emit('radioBtn',res.Data[0].QuestionType)
      } else {
        this.$message.error(res.Msg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.topicRdio{
  display: flex;
  align-items: center;
  h1{
    font-weight: normal;
    //font-size: 24px;
    font-size: var(--font18-24);
    color: #333333;
    margin-left: 30px;
    margin-right: 20px;
  }
  /deep/.el-radio-group {
    display: flex;
    gap: 32px;
    .el-radio-button {
      margin: 0;
      .el-radio-button__inner {
        border-radius: 10px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
        background: #fff;
        border: 1px solid #e0e0e0;
        padding: 0 20px;
        height: 40px;
        line-height: 40px;
        transition: all 0.2s;
      }
      &.is-active .el-radio-button__inner,
      .el-radio-button__inner.is-active {
        background: #7FB99A !important;
        color: #fff !important;
        border-color: #7FB99A !important;
      }
    }
  }
  .radioTxt{
    font-size: var(--font16-22);
  }
}

</style>