<!--activate
 * @Author: 周恩波 <EMAIL>
 * @Date: 2024-06-28 16:31:03
 * @LastEditors: 周恩波
 * @LastEditTime: 2024-07-29 15:08:08
 * @Description:
-->
<template>
  <div class="d-flex h-100">
    <div class="margin_r_5 back_white h-100" style="transition: all ease 0.3s" :style="{ width: leftWidth + '%' }">
      <left-screening :pageId="$route.path" :leftWidth="leftWidth" @changeWidth="leftWidth = leftWidth == 18 ? 2.3 : 18" @getAllList="getLeftScreening"></left-screening>
    </div>
    <div class="container">
<!--      <high-wrong ref="highWrong" :threeAssistants="false" style="width: 100%; height: 100%" @parentPage="parentPage">-->
      <old-page-high-wrong ref="highWrong" :threeAssistants="false" style="width: 100%; height: 100%" @parentPage="parentPage">
        <template v-slot:threeAssistants>
          <!-- 右上搜索部分 -->
<!--          <div class="back_white border_radius_4 margin_b_5 padding_tb_10" ref="searchBarsView">-->
<!--            <div class="flex flex_align_center padding_lr_20 font_size_17 margin_b_10">-->
<!--              <span style="width:5%" >来源：</span>-->
<!--              <div class="flex flex_align_center  IndexSearch  color_6e6e6e">-->
<!--                <span-->
<!--                  class="margin_r_25  cursor_pointer"-->
<!--                  :class="{ activeColor: commonData.Level.value == item.value }"-->
<!--                  @click="checkoutLabel(item)"-->
<!--                  v-for="(item, index) in commonData.Level.options"-->
<!--                  :key="index"-->
<!--                  >{{ item.label }}</span-->
<!--                >-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="padding_lr_20">-->
<!--              <SearchBars-->
<!--                :labelWidth="5"-->
<!--                :year="commonData.Year.value"-->
<!--                :searchData="IndexSearchData"-->
<!--                ref="SearchBars"-->
<!--                @changeSearchData="changeSearchData"-->
<!--              ></SearchBars>-->
<!--            </div>-->
<!--          </div>-->
          <top-screening ref="topScreening" @screeningEmit="screeningEmit" @classClick="classClick"></top-screening>
        </template>
        <template v-slot:threeSearch>
          <el-input placeholder="请输入内容" clearable v-model="QueryKey" style="width: 50%">
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="searchBtn"
            ></el-button>
          </el-input>
        </template>
      </old-page-high-wrong>
    </div>
  </div>
</template>

<script>
// import ChapterMenus from './newJobResources/ChapterMenus.vue'
// import SearchBars from './newJobResources/SearchBars.vue'

import HighWrong from '@/views/Teacher/My_Task/HighWrong/HighWrong.vue'
import OldPageHighWrong from '@/views/Teacher/My_Task/HighWrong/OldPageHighWrong'
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import topScreening from '@/views/threeAssistants/components/topScreening'
export default {
  name: 'highFrequencyErrors',
  components: {
    // SearchBars,
    // HighWrong,
    OldPageHighWrong,
    // ChapterMenus,
    leftScreening,
    topScreening
  },
  data() {
    return {
      input3: '',
      leftWidth: 18, //动态控制布局
      ClassId: {
        label: '班级',
        value: '',
        initValue: '',
        options: [],
      },
      errorsForm:{
        // PageNo: 1,
        // ChapterId: [],
        // Year: null,
        // Semester: null,
        // ClassId: '',
        // Level: 0,
        // QuestionType: '',
        // DifficultyLevel: '',
      },
      QueryKey: ''
    }
  },
  activated() {
    this.$refs.topScreening.GetTeachClass()
  },
  computed: {
    // monitorChapterId() {
    //   return this.$store.state.ChapterId
    // },
    // monitorClassId() {
    //   return this.$store.state.ClassId
    // },
    // monitorTerm() {
    //   return this.$store.state.Term
    // },
    // commonData() {
    //   return Object.assign(this.$store.state.tikuQueryParams, { ClassId: this.ClassId })
    // },
    // IndexSearchData() {
    //   const { TextbookId, QuestionType, DifficultyLevel, ClassId, State, ShareType } = this.commonData
    //   return { TextbookId, QuestionType, DifficultyLevel, ClassId }
    // },
    // queryParams() {
    //   const obj = {}
    //   let keys = [
    //     'ChapterId',
    //     'Year',
    //     'Semester',
    //     'Level',
    //     'QuestionType',
    //     'DifficultyLevel',
    //     'PageNo',
    //     'PageSize',
    //     'ClassId',
    //   ]
    //   keys.forEach((k) => {
    //     obj[k] = this.commonData[k].value
    //   })
    //   obj['QueryKey'] = this.commonData['QueryKey']
    //   return obj
    // },
  },
  watch: {
    // monitorChapterId: {
    //   handler(newVal, val) {
    //     if (newVal && this.$route.path === '/threeAssistants/highFrequencyErrors')
    //       this.$refs.highWrong.threeHelperCalls()
    //   },
    // },
    // monitorClassId: {
    //   handler(newVal, val) {
    //     if (newVal && this.$route.path === '/threeAssistants/highFrequencyErrors')
    //       this.$refs.highWrong.threeHelperCalls()
    //   },
    // },
    // monitorTerm: {
    //   handler(newval, val) {
    //     if (newval && this.$route.path === '/threeAssistants/highFrequencyErrors')
    //       this.$refs.highWrong.threeHelperCalls()
    //   },
    // },
  },
  //路由守卫
  // beforeRouteEnter(to, from, next) {
  //   if (to.path == '/threeAssistants/highFrequencyErrors') {
  //     next((vm) => {
  //       vm.$store.commit('SET_INIT_VALUE')
  //     })
  //   }
  // },
  // created() {
  //   this.GetShowMenu()
  //   // this.changeNotQuery()
  // },
  methods: {
    // 左侧筛查组件方法
    getLeftScreening(obj){
      // const { yearActive, semesterActive, chapterList } = obj
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      const { Level, QuestionType, DifficultyLevel } = this.$refs.topScreening.throwObj
      const chiled = Ids
      this.errorsForm = {
        PageNo: 1,
        Year: Year,
        Semester: Term,
        ChapterId: chiled,
        Level,
        QuestionType,
        DifficultyLevel,
        ClassId: this.$refs.topScreening.ClassId,
        QueryKey: this.QueryKey
      }
      // console.log(this.errorsForm)
      if(this.errorsForm.ClassId == '') return
      this.$refs.highWrong.threeHelperCalls(this.errorsForm)
    },
    // 右上事件操作
    screeningEmit(obj){
      const { Level, QuestionType, DifficultyLevel, ShareType, State } = obj
      this.errorsForm.Level = Level
      this.errorsForm.QuestionType = QuestionType
      this.errorsForm.DifficultyLevel = DifficultyLevel
      this.errorsForm.ShareType = ShareType
      this.errorsForm.State = State
      this.$refs.highWrong.threeHelperCalls(this.errorsForm)
    },
    // 班级切换事件
    classClick(val){
      this.errorsForm.ClassId = val
      this.$refs.highWrong.threeHelperCalls(this.errorsForm)
    },
    // 分页
    parentPage(val){
      this.errorsForm.PageNo = val
      this.$refs.highWrong.threeHelperCalls(this.errorsForm)
    },
    // 查询搜索
    searchBtn(){
      this.errorsForm.QueryKey = this.QueryKey
      this.$refs.highWrong.threeHelperCalls(this.errorsForm)
    }
    // checkoutLabel(item) {
    //   this.commonData.Level.value = item.value
    //   this.$nextTick(() => {
    //     this.$refs.highWrong.threeHelperCalls(this.queryParams)
    //   })
    // },
    // changeSearchData(search) {
    //   const { item, key } = search
    //   this.commonData[key].value = item.value
    //   this.$nextTick(() => {
    //     this.$refs.highWrong.threeHelperCalls(this.queryParams)
    //   })
    // },
    //更换章节
    // changeChapterId(ChapterId) {
    //   if (this.commonData.ChapterId.value == ChapterId) return
    //   this.commonData.ChapterId.value = ChapterId
    //   this.$nextTick(() => {
    //     this.$refs.highWrong.threeHelperCalls(this.queryParams)
    //   })
    // },
    // 切换学年、学期、年级,可选班级也要切换
    // async changeNotQuery(obj) {
    //   await this.GetTeachClass()
    //   this.$refs.highWrong.threeHelperCalls(this.queryParams)
    // },
    // 获取班级接口
    // async GetTeachClass() {
    //   const { Year, GradeId } = this.commonData
    //   const { data } = await this.$uwonhttp.get(
    //     `/Class/TeacherClassManager/GetTeachClass?UserId=${localStorage.getItem('UserId')}&grade=${
    //       GradeId.value
    //     }&year=${Year.value}`
    //   )
    //   if (!data.Success) return this.$message.error(data.Msg)
    //   data.Data.forEach((item) => {
    //     item.label = item.ClassName
    //     item.value = item.ClassId
    //   })
    //   this.ClassId.options = data.Data
    //   this.ClassId.value = data.Data[0].ClassId
    // },
    //根据角色获取试题类型 市 区 我的 ...
    // async GetShowMenu() {
    //   if (this.$store.state.tikuQueryParams['Level'].options.length > 0) return
    //   const { data } = await this.$uwonhttp.get(`/Question/Question/GetShowMenu?roleId=${localStorage.getItem('role')}`)
    //   if (!data.Success) return this.$message.error(data.Msg)
    //   let Data = data.Data
    //   Data.forEach((item) => {
    //     item.label = item.text
    //   })
    //   this.$store.commit('SET_PARAM_OPTIONS', { key: 'Level', options: [{ label: '全部', value: -1 }, ...Data] })
    //   setTimeout(() => {
    //     this.boxHeight = this.$refs.searchBarsView.offsetHeight
    //   }, 100)
    // },
  },
}
</script>

<style scoped lang="less">
.h-100 {
  height: 100%;
}
.d-flex {
  display: flex;
  align-items: center;
}
.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}
.activeColor {
  color: #7fb99a;
}
</style>
