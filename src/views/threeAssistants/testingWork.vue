<template>
  <div class="h-100 work-container">
    <div
      class="margin_r_5 back_white h-100"
      style="transition: all ease 0.3s"
      :style="{ width: leftWidth + '%' }"
    >
      <left-screening :pageId="$route.path" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 18 ? 2.3 : 18" @getAllList="getLeftScreening"></left-screening>
    </div>
    <div class="box-container">
      <div class="_dis">
        <el-button
          type="text"
          v-for="item in paperTypeList"
          :class="{active: item.value == PaperType}"
          :key="item.value"
          @click="typesOf(item.value)">
          {{ item.label }}
        </el-button>
      </div>
      <div class="search-title">
        <div class="btmBtn">
          班级：
          <el-button
            type="text"
            v-for="item in classArray"
            :key="item.ClassId"
            :style="{ color: item.ClassId === classId ? '#7FB99A' : '#6E6E6E'}"
            @click="levelClass(item.ClassId)">{{item.ClassName}}</el-button>
        </div>
      </div>

      <div class="_search">
        <el-input v-model="search" suffix-icon="el-icon-search" placeholder="请输入作业名称" @input="inputBtn" clearable></el-input>
      </div>
      <div class="testing"
        v-loading="isLoading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="#fff">
        <div class="listEntirety" v-if="workList.length > 0">
          <list-index
            v-for="item in workList"
            :key="item.PaId"
            :paperItem="item"
            :calssId="classId"
            @getPaperView="getExerciseData"
            @previewPaper="previewPaper"
            @ClickPaper="ClickPaper"
            @testClick="testClick"></list-index>
        </div>
        <div v-else class="noData">
          <div>
            <img src="@/assets/lack/暂无搜索记录.png" alt="" />
            <p>暂无班级数据</p>
          </div>
        </div>
          <a-modal
            title="撤销发布"
            :visible="rvisible"
            :confirm-loading="confirmLoading"
            @ok="handleOk()"
            @cancel="handleCancel"
            centered>
            <p>{{ ModalText }}</p>
          </a-modal>
      </div>
      <div class="fenYe" v-show="total > 6">
        <!--   分页   -->
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>
    <!--  预览  -->
    <preview-paper ref="previewPaper"></preview-paper>
  </div>
  </div>
</template>

<script>
// import ChapterMenus from './newJobResources/ChapterMenus.vue'
import {debounce} from 'lodash'
import eventBus from '@/views/Teacher/eventBus/eventBus'
import { Session } from '@/utils/storage.js'
import ChartDom from '@/views/threeAssistants/components/ringChart/chartDom'
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import listIndex from '@/views/Teacher/My_Task/components/newHomepage/listIndex.vue'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper.vue'
const yearDate = new Date().getFullYear();

export default {
  name: 'testingWork',
  components: {
    previewPaper, listIndex, ChartDom , leftScreening
  },
  data() {
    return {
      leftWidth: 18, //动态控制布局
      textBook:[],// 教材
      classArray:[],// 班级列表
      TextbookVal: '',// 教材ID
      search:'',// 模糊查询字段
      classId:'',// 选中的班级ID
      PaperName:'',// 作业名称模糊查询
      isLoading:true,// loading加载
      total: 0,
      workList: [],
      paperId: '',
      currentPage: 1,
      rvisible: false,
      confirmLoading: false,
      ModalText: '撤销发布后，该练习将被删除！',
      // 左侧组件缓存值
      // leftSearch: {},
      yearDefault:`${this.$store.state.user.yearDefault}`,
      paperTypeList:[
        { value: 0, label: '全部' },
        { value: 1, label: '市级作业' },
        // { value: 2, label: '区级作业' },
        { value: 3, label: '校本作业' },
        { value: 4, label: '我的作业' },
      ],
      PaperType: 0,
    }
  },
  watch:{
    $route: {
      handler(nv, ov) {
        if (nv.path == '/Home/Introduce') {
          this.getExerciseData()
        }
      },
    },
  },
  async created() {
    await this.GetTextbookVersion()
    // await this.getClassList()
    // localStorage.setItem('classPage','Introduce')
    // this.getExerciseData()
  },
  async activated() {
    // this.getExerciseData()
    await this.GetTextbookVersion()
    this.getClassList()
  },
  methods:{
    // 市区校班
    // 练习类型事件
    typesOf(val){
      this.currentPage = 1
      this.PaperType = val
      this.getExerciseData()
    },
    // 左侧章节组件
    getLeftScreening(val){
      this.currentPage = 1;
      // this.leftSearch = val
      this.getClassList()
    },
    // 预览
    previewPaper(id){
      this.$refs.previewPaper.init(id)
    },
    levelClass(val){
      this.currentPage = 1;
      this.classId = val
      this.getExerciseData()
    },
    testClick(item) {
      const { PaId, IsDZB, ChapterId, ReviewStatus, HomeworkType, ClassId, ClassName, Grade ,SubjectId, PaperTag, PaperTitle} = item
      const { Year, GradeId } = this.$store.state.chapterStorage.ChapterObj
      const query = {
        PaperTag,//作文
        PaperTitle,
        ClassName,
        // Grade: this.grade,
        Grade: GradeId,
        firstChapter:ChapterId,
        SourceId: PaId,
        HomeworkType:HomeworkType || 1,
        ClassId: this.classId,
        State: 0,//0 未授课；1已授课；2 授课中
        // year:  this.year,
        year:  Year,
        isKatex: [3,23,37,100].includes(SubjectId*1),// 学科ID 如果是true代表当前是英语，不走公式识别
        isCurrentYear: this.$store.state.user.yearDefault == Year ? 1 : 0, // 选中的是否当前学年 1是0否
      }
      // const { PaId, IsDZB, ChapterId, ReviewStatus, HomeworkType } = item
      // const query = {
      //   paperId: PaId,
      //   classId: this.classId,
      //   chapterNum: ChapterId,
      //   area: 'area',
      //   ReviewStatus: ReviewStatus,
      //   IsDZB,
      //   HomeworkType:HomeworkType || 1,
      //   isCurrentYear: yearDate == this.yearDefault ? 1 : 0, // 选中的是否当前学年 1是0否
      //   isShowLeftView:true,// 是否显示左侧菜单 三个助手中不展示左侧的菜单
      // }
      Session.remove('isRefresh')
      Session.remove('questionsInfo')
      Session.remove('sessionInfo')
      const GoPge = this.$router.resolve({
        // path: '/answer-module',
        path: '/teaching-modules',
        query,
      })
      window.open(GoPge.href, '_blank')
    },
      // 获取班级数据
    getClassList() {
      // const { yearActive, gradeActive } = this.leftSearch
      const { Year, GradeId } = this.$store.state.chapterStorage.ChapterObj
      const params = {
        // year: yearActive,
        year: Year,
        UserId: localStorage.getItem('UserId'),
        // grade: gradeActive
        grade: GradeId
      }
      // console.log(params)
      this.$uwonhttp.get('/Class/TeacherClassManager/GetTeachClass', { params })
        .then((res) => {
          if(res.data.Success){
            this.classArray = res.data.Data;
            this.classId = res.data.Data[0].ClassId
            this.getExerciseData()
          } else {
            this.$message.error(res.data.Msg)
          }
        })
    },

    levelBook(type, val){
      if(val == this.TextbookVal) return false
      this.currentPage = 1;
      this.TextbookVal = val
      this.getExerciseData()
    },
    inputBtn: debounce(function(){
      this.currentPage = 1;
      this.getExerciseData()
    },300) ,
     // 获取教材
     async GetTextbookVersion(){
      const { data:res } = await this.$uwonhttp.get('/Question/Question/GetTextbookVersion')
      if(res.Success){
        // 往数组头部添加对象
        res.Data.unshift({ value: '', text: '全部' })
        this.textBook = res.Data
        if(this.textBook.length <3) this.TextbookVal = this.textBook[1].value
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 撤销发布按钮
    showRevokeModal(id) {
      this.paperId = id
      this.rvisible = true
    },
    // 撤销发布弹窗确认按钮
    handleOk() {
      this.ModalText = '正在撤销...'
      this.confirmLoading = true
      setTimeout(() => {
        this.rvisible = false
        this.confirmLoading = false
      }, 2000)
      this.$http
        .post('/Paper/Exam_Paper/RevokePublishPaper', {
          paperId: this.paperId,
          classId: this.$store.state.ClassId,
        })
        .then((resJson) => {
          this.load = true
          this.getExerciseData()
        })
    },
    // 撤销发布弹窗取消按钮
    handleCancel() {
      this.rvisible = false
    },
    // 检查作业事件
    ClickPaper({title, PaId, ChapterName, ReviewStatus, area, ClassName, PaperTitle, IsDZB, IsCoreLiteracy, PaperItemType, HomeworkType, PaperNum}) {
      const query = {
        paperId: PaId,
        classId: this.$store.state.ClassId || this.classId,
        chapterNum: ChapterName,
        area: 'area',
        ReviewStatus,
        IsCoreLiteracy,
        IsDZB,
      }
      if (title === 'info') {
        const storage = window.localStorage
        storage['at_className'] = ClassName
        storage['at_paperTitle'] = PaperTitle
        if (PaperItemType == 53) {
          Object.assign(query, { color: 7 })
        }
        if (IsDZB == 1) {
          Object.assign(query, { color: 6, PaperNum: PaperNum, HomeworkType: HomeworkType })
        }
        // const GoPge = this.$router.resolve({
        this.$router.push({
          path: '/Teacher/My_Task/InspectionWorkData',
          query,
        })
        // window.open(GoPge.href, '_blank')
      }
    },
    // 翻页
    handleCurrentChange(val) {
      this.currentPage = val
      this.getExerciseData()
    },
    // 获取练习数据
    getExerciseData(){
      // if(this.$store.state.ClassId !== ''){
        this.isLoading = true
        const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
        const data = {
          // ChapterId: this.$store.state.ChapterId,
          ChapterId: '', // 单元字段
          // ChapterIds:chiled,
          ChapterIds:Ids,
          PaperType: this.PaperType,
          paperTag:"",
          ReviewStatus:0,
          ReleaseStates:1,
          PracticeStates:0,
          OrderById: 5,
          ClassId: this.classId,
          UserId: localStorage.getItem('UserId'),
          PageNo: this.currentPage,
          PageIndex: 10,
          IsDZB:-1,
          Platform: parseInt(localStorage.getItem('PL_newP')),
          // Term: this.$store.state.Term || this.$store.state.tikuQueryParams.Semester.value,
          Term,
          TextbookId:this.TextbookVal,//1813083434977894400 上教版//'1813083321643606016' 人教版
          // Year: yearActive,
          Year,
          PaperTitle:this.search
        }
        // console.log(this.classId)
        this.$uwonhttp.post('/Paper/TeacherPaper/GetTeacherPaperListPractice',data).then(res => {
          // console.log(res)
          this.isLoading = false
          if(res.data.Success){
            this.total = res.data.Data.TotalItems
            this.workList = res.data.Data.Items
          } else {
            this.$message.error(res.data.Msg)
          }
        })
      // }
    }
  }
}
</script>

<style lang="less" scoped>
  ._dis{
    padding: 0 16px 0 24px;
    height: 50px;
    align-items: center;
    display: flex;
    //justify-content: space-between;
    background-color: #FFFFFF;
    border-bottom: 1px solid #0000001A;
    .el-button{
      font-size: 22px;
      color: #6E6E6E;
    }
    //.title{
    //  font-size: 20px;
    //  color: #333333;
    //}
  }
  .search-title{
    margin-bottom: 8px;
  }
  .btmBtn{
    height: 40px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    font-weight: normal;
    font-size: 17px;
    padding-left: 24px;
    .el-button{
      font-weight: normal;
      font-size: 17px;
    }
  }
  ._search{
    margin-bottom: 8px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    background-color: #fff;
    .el-input{
      width: 450px;
    }
  }
.h-100{
  height: 100%
}
.work-container{
  display: flex;
}
.box-container{
  flex: 1;
  //overflow: auto;
}
.testing{
  background-color: #fff;
  height: 61vh;
  overflow: auto;
  padding-bottom: 20px;
  .listEntirety{
    //display: grid;
    width: 100%;
    grid-template-columns:repeat(auto-fill,33.33%);
    //.mainBody{
    //  box-sizing: border-box;
    //  min-height: 240px;
    //  background: #FFFFFF;
    //  border-radius: 8px;
    //  padding: 28px 20px 22px;
    //  margin: 0 6px 8px 0;
    //  position: relative;
    //  .release{
    //    width: 64px;
    //    height: 70px;
    //    position: absolute;
    //    right: 0;
    //    top: 0;
    //  }
    //  .list_top{
    //    display: flex;
    //    align-items: center;
    //    .paperTitle{
    //      font-family: PingFangSC, PingFang SC;
    //      font-weight: 500;
    //      font-size: 20px;
    //      color: #434343;
    //      font-style: normal;
    //      margin-right: 13px;
    //    }
    //    span{
    //      font-family: PingFangSC, PingFang SC;
    //      font-weight: 500;
    //      font-size: 14px;
    //      color: #61BB96;
    //      padding: 5px 20px;
    //      background: rgba(97, 187, 150, 0.2);
    //      border-radius: 15px;
    //    }
    //    .subject{
    //      margin-right: 5px;
    //    }
    //    .grade{
    //      margin-right: 5px;
    //    }
    //    .keyCompetence{
    //      background: #1890ff;
    //      color: white;
    //      border-radius: 4px !important;
    //      margin-right: 10px;
    //      padding: 2px 10px !important;
    //    }
    //    p{
    //      font-family: PingFangSC, PingFang SC;
    //      font-weight: 500;
    //      font-size: 14px;
    //      padding: 5px 16px;
    //      border-radius: 15px;
    //    }
    //    img{
    //      width: 24px;
    //    }
    //  }
    //  .list_cont{
    //    display: flex;
    //    margin-top: 12px;
    //    ._left{
    //      // margin-right: 120px;
    //      div{
    //        font-family: PingFangSC, PingFang SC;
    //        font-weight: 500;
    //        font-size: 16px;
    //        color: #707070;
    //      }
    //      .margin{
    //        margin: 12px 0;
    //      }
    //      p{
    //        font-family: PingFangSC, PingFang SC;
    //        font-weight: 400;
    //        font-size: 15px;
    //        color: #B1B1B1;
    //      }
    //    }
    //    ._right{
    //      display: flex;
    //      >div:nth-child(1){
    //        margin-right: 40px;
    //      }
    //      .numberOfPeople{
    //        position: relative;
    //        width: 60px !important;
    //        height: 60px !important;
    //        /deep/.el-progress-circle{
    //          width: 60px !important;
    //          height: 60px !important;
    //        }
    //        .circleCenter{
    //          width: 60px;
    //          position: absolute;
    //          top: 18px;
    //          left: 0;
    //          font-weight: 500;
    //          font-size: 13px !important;
    //          text-align: center;
    //        }
    //      }
    //      .accuracy{
    //        width: 60px !important;
    //        height: 60px !important;
    //        /deep/.el-progress-circle{
    //          width: 60px !important;
    //          height: 60px !important;
    //        }
    //        /deep/.el-progress__text{
    //          font-size: 13px !important;
    //          color: #606266;
    //          display: inline-block;
    //          vertical-align: middle;
    //          margin-left: 0;
    //        }
    //      }
    //      .txt{
    //        text-align: center;
    //        font-size: 14px;
    //        color: #989898;
    //      }
    //    }
    //  }
    //  .list_btm{
    //    display: flex;
    //    justify-content: flex-end;
    //    margin-top: 22px;
    //    .el-button{
    //      width: 140px;
    //      font-size: 16px;
    //      padding-top: 10px;
    //      padding-bottom: 10px;
    //    }
    //    .revocation{
    //      margin-right: 10px;
    //      color: #61BB96;
    //    }
    //  }
    //}
    //.mainBody:nth-child(3n){
    //  margin-right: 0 !important;
    //}
  }
  .noData{
    width: 100%;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    p{
      margin-top: 20px;
      font-size: 18px;
      color: grey;
      text-align: center;
    }
  }
}
  .fenYe{
    // margin-top: 14px;
    padding: 5px 0;
    display: flex;
    background-color: #fff;
    justify-content: center;
    /deep/.el-pager li {
      font-size:18px;
    }
    /deep/.el-pagination__total{
      font-size: 18px;
    }
  }
  .active{
    color: #7fb99a !important;
  }
</style>