<template>
  <div class="knowledgePoints">
    <div class="form-view">
      <el-form ref="form" :inline="true" :model="form">
        <el-form-item label="编码名称：">
          <el-input v-model="form.CodeContent"></el-input>
        </el-form-item>
        <el-form-item label="学段类型：">
          <el-select v-model="form.StageType" placeholder="请选择" clearable @change="gradeTypeChange">
            <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学科：">
          <el-select v-model="form.SubjectId" placeholder="请选择" clearable>
            <el-option v-for="option in subjectOption" :label="option.Name" :value="option.Id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="编码类型：">
          <el-input v-model="form.CodeType"></el-input>
        </el-form-item>
        <el-form-item label="学年（届）：">
          <el-input v-model="form.Year"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="medium" @click="onSubmit">查询</el-button>
          <el-button size="medium" @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-view">
      <div class="table-view-header">
        <el-button type="primary" size="medium" @click="addKnowledge">新增知识点</el-button>
        <el-button size="medium" @click="batchImport">批量导入</el-button>
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f5f5', textAlign: 'center' }"
        :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="CodeContent" label="编码名称" width="180"></el-table-column>
        <el-table-column prop="SubjectName" label="学科" width="180"></el-table-column>
        <el-table-column prop="CodeType" label="编码类型" width="180"></el-table-column>
        <el-table-column prop="Year" label="学年（届）" width="180"></el-table-column>
        <el-table-column prop="Type" label="类型" width="180"></el-table-column>
        <el-table-column prop="StageType" label="学段类型" width="180"></el-table-column>
        <el-table-column prop="Code" label="编码" width="180"></el-table-column>
      </el-table>
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="pageInfo.PageIndex"
        :page-size="pageInfo.PageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      form: {
        CodeContent: '',
        SubjectId: '',
        Type: '',
        StageType: '',
        CodeType: '',
        Year: '',
      },
      pageInfo: {
        PageIndex: 1,
        PageSize: 20,
      },
      gradeTypeOption,
      subjectOption: [],
      total: 0,
      tableData: [],
    }
  },
  components:{
    
  },
  created() {
    this.getKnowledgeList()
  },
  methods: {
    gradeTypeChange() {
      this.getSubjectList()
    },
    /**
     * 通过学段获取学科
     */
    getSubjectList() {
      this.$http.post('/School/Exam_School/GetSubjects', { SchoolType: this.form.StageType }).then((res) => {
        this.subjectOption = res.Data
      })
    },
    resetForm() {
      this.$refs.form.resetFields()
    },
    onSubmit() {
      this.getKnowledgeList()
    },
    handleCurrentChange(page) {
      this.pageInfo.PageIndex = page
      this.getKnowledgeList()
    },
    async getKnowledgeList() {
      const { data } = await this.$uwonhttp.post('/Chapter/Chapter/GetKnowledgePointsDictList', {
        ...this.form,
        ...this.pageInfo,
      })
      if (!data.Success) return this.$message.error(data.Msg)
      // this.tableData = data.Data.List
      // this.total = data.Data.Total
    },
    addKnowledge() {
      this.$router.push({
        path: '/Base_Manage/DictInformation/knowledgePoints/addKnowledge/index'})
    },
    batchImport() {
      this.$router.push({
        path: '/Base_Manage/DictInformation/knowledgePoints/batchImportKnowledge/index'})
    }
  }
}
</script>

<style lang="less" scoped>
.knowledgePoints{
  .form-view{
    margin-bottom: 20px;
    padding: 20px;
    background-color: #FFFFFF;
  }
  .table-view{
    padding: 20px;
    background-color: #FFFFFF;
  }
  .table-view-header{
    padding-bottom: 20px;
  }
}
</style>