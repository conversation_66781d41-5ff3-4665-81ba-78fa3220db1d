<template>
  <div class="knowledge-node" :class="'level-' + level">
    <div class="node-content">
      <span class="expand-icon" v-if="node.Subset && node.Subset.length > 0" @click="toggleExpand">
        {{ expanded ? '▼' : '▶' }}
      </span>
      <span class="node-text">{{ node.CodeContent }}</span>
      <span class="node-code">({{ node.CodeId }})</span>
    </div>
    <div v-if="expanded && node.Subset && node.Subset.length > 0" class="children">
      <knowledge-node 
        v-for="child in node.Subset" 
        :key="child.CodeId" 
        :node="child" 
        :level="level + 1">
      </knowledge-node>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      expanded: true
    }
  },
  methods: {
    toggleExpand() {
      this.expanded = !this.expanded
    }
  }
}
</script>

<style lang="less" scoped>
.knowledge-node {
  margin-bottom: 5px;
  
  .node-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    background: white;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    
    &:hover {
      background: #f0f9ff;
      border-color: #409eff;
    }
    
    .expand-icon {
      margin-right: 8px;
      width: 16px;
      color: #666;
      cursor: pointer;
      user-select: none;
    }
    
    .node-text {
      flex: 1;
      font-weight: 500;
      color: #333;
    }
    
    .node-code {
      font-size: 12px;
      color: #999;
      margin-left: 8px;
    }
  }
  
  .children {
    margin-left: 24px;
    margin-top: 5px;
  }
  
  &.level-1 .node-content {
    background: #e8f4fd;
    border-color: #409eff;
    
    .node-text {
      color: #409eff;
      font-weight: 600;
    }
  }
  
  &.level-2 .node-content {
    background: #f0f9ff;
    border-color: #67c23a;
    
    .node-text {
      color: #67c23a;
      font-weight: 500;
    }
  }
  
  &.level-3 .node-content {
    background: #f6ffed;
    border-color: #e6a23c;
    
    .node-text {
      color: #e6a23c;
    }
  }
  
  &.level-4 .node-content {
    background: #fff7e6;
    border-color: #f56c6c;
    
    .node-text {
      color: #f56c6c;
    }
  }
}
</style>
