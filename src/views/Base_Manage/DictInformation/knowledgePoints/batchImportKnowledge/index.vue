<template>
  <div class="addKnowledge">
    <div class="header-view">
      <i class="el-icon-arrow-left" @click="goBack"></i>
      <span>批量导入知识点</span>
    </div>
    <div class="form-view">
      <!-- 添加 :label-position="labelPosition" :label-suffix="labelSuffix" 属性 -->
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="demo-form-inline" :label-position="labelPosition" :label-suffix="labelSuffix">
        <div class="form-item-view">
          <el-form-item label="学段：" prop="StageType" >
            <el-select v-model="form.StageType" placeholder="请选择" clearable @change="gradeTypeChange">
              <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学科：" prop="SubjectId" >
            <el-select v-model="form.SubjectId" placeholder="请选择" clearable>
              <el-option v-for="option in subjectOption" :label="option.Name" :value="option.Id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="版本：" prop="Version" >
            <el-input v-model="form.Version" placeholder="请输入版本"></el-input>
          </el-form-item>
          <el-form-item label="父级：" prop="Parentid" >
            <el-input v-model="form.Parentid" placeholder="请输入父级知识点名称"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="upload-view">
        <el-button type="primary" @click="batchAdd">选择文件</el-button>
        <span class="margin-right">只支持xlsx格式，</span>
        <el-link class="elLink" type="primary" href="https://eduwpsfile.obs.cn-east-2.myhuaweicloud.com/A4-three-template.docx" target="_blank">下载模版</el-link>
      </div>

      <!-- 知识点预览区域 -->
      <div class="knowledge-preview" v-if="form.Subset && form.Subset.length > 0">
        <h3>导入的知识点预览：</h3>
        <div class="knowledge-tree">
          <div v-for="knowledge in form.Subset" :key="knowledge.CodeId" class="knowledge-item">
            <knowledge-node :node="knowledge" :level="1"></knowledge-node>
          </div>
        </div>
      </div>

      <div class="bottom-view">
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import KnowledgeNode from './KnowledgeNode.vue'
const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      gradeTypeOption,
      subjectOption: [],
      form: {
        Parentid: '',
        SubjectId: '',
        StageType: '',
        Version: '',
        Subset: [{
          CodeContent: '',
          Sort: 1,
          CodeId: '1',
          Level: 1,
          Subset: []
        }]
      },
      labelPosition: 'right', // 标签位置
      labelSuffix: ' ', // 标签后缀
      // 配置验证规则
      rules: {
        StageType: [
          { required: true, message: '请输入学段', trigger: 'blur' }
        ],
        SubjectId: [
          { required: true, message: '请输入学科', trigger: 'blur' }
        ],
        Version: [
          { required: true, message: '请输入版本', trigger: 'blur' }
        ]
      },
      total: 0,
      tableData: [],
    }
  },
  components: {
    KnowledgeNode
  },
  activated() {
    
  },
  methods: {
    gradeTypeChange() {
      this.getSubjectList()
    },
    /**
     * 通过学段获取学科
     */
    getSubjectList() {
      this.$http.post('/School/Exam_School/GetSubjects', { SchoolType: this.form.StageType }).then((res) => {
        this.subjectOption = res.Data
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('表单提交', this.form);
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    batchAdd() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.xlsx,.xls'
      input.style.display = 'none'

      input.onchange = (event) => {
        const file = event.target.files[0]
        if (!file) return

        // 验证文件类型
        const fileName = file.name.toLowerCase()
        if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
          this.$message.error('请选择Excel文件（.xlsx或.xls格式）')
          return
        }

        this.parseExcelFile(file)
      }

      // 触发文件选择
      document.body.appendChild(input)
      input.click()
      document.body.removeChild(input)
    },

    // 解析Excel文件
    parseExcelFile(file) {
      const reader = new FileReader()

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result)
          const workbook = XLSX.read(data, { type: 'array' })

          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]

          // 将工作表转换为JSON数组
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1, // 使用数组格式而不是对象格式
            defval: '' // 空单元格的默认值
          })

          console.log('Excel原始数据:', jsonData)

          // 解析数据并构建知识点树
          this.buildKnowledgeTree(jsonData)

        } catch (error) {
          console.error('解析Excel文件失败:', error)
          this.$message.error('解析Excel文件失败，请检查文件格式')
        }
      }

      reader.onerror = () => {
        this.$message.error('读取文件失败')
      }

      reader.readAsArrayBuffer(file)
    },

    // 构建知识点树结构
    buildKnowledgeTree(excelData) {
      if (!excelData || excelData.length === 0) {
        this.$message.warning('Excel文件为空')
        return
      }

      // 过滤掉空行
      const validRows = excelData.filter(row =>
        row && row.some(cell => cell && cell.toString().trim() !== '')
      )

      if (validRows.length === 0) {
        this.$message.warning('Excel文件中没有有效数据')
        return
      }

      console.log('有效数据行:', validRows)

      const newSubset = []
      let sortCounter = 1

      // 用于跟踪各级别的父节点和编码
      const levelParents = {}
      const levelCodes = {}

      validRows.forEach((row, rowIndex) => {
        // 处理每一行数据
        for (let colIndex = 0; colIndex < row.length; colIndex++) {
          const cellValue = row[colIndex]
          if (!cellValue || cellValue.toString().trim() === '') {
            continue
          }

          const knowledgeName = cellValue.toString().trim()
          const level = colIndex + 1 // 第一列为第一级，第二列为第二级

          // 生成编码
          let codeId = ''
          if (level === 1) {
            // 第一级编码：1, 2, 3...
            if (!levelCodes[1]) {
              levelCodes[1] = 0
            }
            levelCodes[1]++
            codeId = levelCodes[1].toString()

            // 重置下级编码
            for (let i = 2; i <= 10; i++) {
              levelCodes[i] = 0
            }
          } else {
            // 子级编码：1.1, 1.1.1...
            const parentLevel = level - 1
            const parent = levelParents[parentLevel]

            if (!parent) {
              console.warn(`第${rowIndex + 1}行第${colIndex + 1}列: 找不到第${parentLevel}级父节点，跳过"${knowledgeName}"`)
              continue
            }

            if (!levelCodes[level]) {
              levelCodes[level] = 0
            }
            levelCodes[level]++

            // 重置下级编码
            for (let i = level + 1; i <= 10; i++) {
              levelCodes[i] = 0
            }

            codeId = parent.CodeId + '.' + levelCodes[level]
          }

          // 创建知识点对象
          const knowledge = {
            CodeContent: knowledgeName,
            Sort: sortCounter++,
            CodeId: codeId,
            Level: level,
            Subset: []
          }

          if (level === 1) {
            // 第一级知识点，直接添加到根级别
            // 检查是否已存在同名知识点
            const existingKnowledge = newSubset.find(k => k.CodeContent === knowledgeName)
            if (!existingKnowledge) {
              newSubset.push(knowledge)
              levelParents[1] = knowledge
            } else {
              levelParents[1] = existingKnowledge
            }
          } else {
            // 子级知识点，需要找到父节点
            const parentLevel = level - 1
            const parent = levelParents[parentLevel]

            if (parent) {
              // 检查父节点下是否已存在同名子知识点
              const existingChild = parent.Subset.find(k => k.CodeContent === knowledgeName)
              if (!existingChild) {
                parent.Subset.push(knowledge)
                levelParents[level] = knowledge
              } else {
                levelParents[level] = existingChild
              }
            }
          }
        }
      })

      // 更新知识点列表
      this.form.Subset = newSubset

      console.log('构建的知识点树:', this.form.Subset)

      this.$message.success(`成功导入 ${validRows.length} 行数据，共生成 ${this.countTotalKnowledge(newSubset)} 个知识点`)
    },

    // 统计知识点总数（递归）
    countTotalKnowledge(knowledgeList) {
      let count = 0
      knowledgeList.forEach(knowledge => {
        count += 1
        if (knowledge.Subset && knowledge.Subset.length > 0) {
          count += this.countTotalKnowledge(knowledge.Subset)
        }
      })
      return count
    },
  }
}
</script>

<style lang="less" scoped>
.addKnowledge{
  padding: 20px;
  background-color: #FFFFFF;
  height: 80vh;
  .header-view{
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #E4E7ED;
  }
  .form-item-view{
    width: 70%;
    display: flex;
    align-items: center;
  }
  .form-view{
    padding-top: 20px;
  }
  /* 为必填项的星号添加红色样式 */
  .el-form-item.is-required .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  .upload-view{
    padding: 20px 0;
    display: flex;
    align-items: center;
  }
  .bottom-view{
    display: flex;
    justify-content: center;
  }

  .knowledge-preview {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
  }

  .knowledge-tree {
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>