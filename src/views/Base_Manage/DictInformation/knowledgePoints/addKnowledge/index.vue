<template>
  <div class="addKnowledge">
    <div class="header-view">
      <i class="el-icon-arrow-left" @click="goBack"></i>
      <span>添加知识点</span>
    </div>
    <div class="form-view">
      <!-- 添加 :label-position="labelPosition" :label-suffix="labelSuffix" 属性 -->
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="demo-form-inline" :label-position="labelPosition" :label-suffix="labelSuffix">
        <div class="form-item-view">
          <el-form-item label="父级：" prop="Parentid" >
            <el-input v-model="form.Parentid" placeholder="请输入父级知识点名称"></el-input>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="知识点名称：" prop="CodeContent" >
            <el-input v-model="form.CodeContent" placeholder="请输入知识点名称"></el-input>
          </el-form-item>
          <el-form-item label="学段：" prop="StageType" >
            <el-select v-model="form.StageType" placeholder="请选择" clearable @change="gradeTypeChange">
              <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学科：" prop="SubjectId" >
            <el-select v-model="form.SubjectId" placeholder="请选择" clearable>
              <el-option v-for="option in subjectOption" :label="option.Name" :value="option.Id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="版本：" prop="Version" >
            <el-select v-model="form.Version" placeholder="请选择" clearable>
              <el-option v-for="option in versionOption" :label="option" :value="option"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序：" prop="sortOrder" >
            <el-input v-model="form.sortOrder" placeholder="请输入排序"></el-input>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="知识点编号：" prop="knowledgeCode" >
            <el-input v-model="form.knowledgeCode" placeholder="请输入知识点编号" disabled></el-input>
          </el-form-item>
          <el-form-item label="层级：" prop="level" >
            <el-input v-model="form.level" placeholder="请输入层级" disabled></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="bottom-view">
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { version } from '@antv/data-set'

const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      gradeTypeOption,
      subjectOption: [],
      versionOption: [],
      form: {
        Parentid: '',
        StageType: '',
        SubjectId: '',
        Version: '',
        Type: '',
        CodeContent: '',
      },
      labelPosition: 'right', // 标签位置
      labelSuffix: ' ', // 标签后缀
      // 配置验证规则
      rules: {
        CodeContent: [
          { required: true, message: '请输入知识点名称', trigger: 'blur' }
        ],
        StageType: [
          { required: true, message: '请输入学段', trigger: 'blur' }
        ],
        SubjectId: [
          { required: true, message: '请输入学科', trigger: 'blur' }
        ],
        Version: [
          { required: true, message: '请输入版本', trigger: 'blur' }
        ]
      },
      total: 0,
      tableData: [],
    }
  },
  components:{
    
  },
  created() {
    this.getKnowledgeVerson()
  },
  methods: {
    gradeTypeChange() {
      this.getSubjectList()
    },
    /**
     * 通过学段获取学科
     */
    getSubjectList() {
      this.$http.post('/School/Exam_School/GetSubjects', { SchoolType: this.form.StageType }).then((res) => {
        this.subjectOption = res.Data
      })
    },
    getKnowledgeVerson() {
      this.$uwonhttp.post('/Chapter/Chapter/GetKnowledgePointsVersion').then((res) => {
        this.versionOption = res.data.Data
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('表单提交', this.form);
          this.addKnowledge()
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    addKnowledge() {
      this.form.Subset = [{CodeContent: this.form.CodeContent}]
      delete this.form.CodeContent
      this.$uwonhttp.post('/Chapter/Chapter/AddKnowledgePointsDict', this.form).then((res) => {
        if (res.data.Success) {
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.addKnowledge{
  padding: 20px;
  background-color: #FFFFFF;
  height: 80vh;
  .header-view{
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #E4E7ED;
  }
  .form-item-view{
    width: 70%;
    display: flex;
    align-items: center;
  }
  .form-view{
    padding-top: 20px;
  }
  /* 为必填项的星号添加红色样式 */
  .el-form-item.is-required .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  .bottom-view{
    display: flex;
    justify-content: center;
  }
}
</style>