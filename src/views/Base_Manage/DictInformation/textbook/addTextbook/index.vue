<template>
  <div class="addKnowledge">
    <div class="header-view">
      <div class="header-left" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>新增教材</span>
      </div>
    </div>
    <div class="steps-view">
      <el-steps :active="activeStep" finish-status="success">
        <el-step title="基础信息"></el-step>
        <el-step title="添加单元章节"></el-step>
      </el-steps>
    </div>
    <div v-show="activeStep === 0" class="form-view">
      <!-- 添加 :label-position="labelPosition" :label-suffix="labelSuffix" 属性 -->
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="demo-form-inline" :label-position="labelPosition" :label-suffix="labelSuffix">
        <div class="form-item-view">
          <el-form-item label="教材名称：" prop="Name" >
            <el-input v-model="form.Name" placeholder="请输入教材名称"></el-input>
          </el-form-item>
          <el-form-item label="学段：" prop="StageType" >
            <el-select v-model="form.StageType" placeholder="请选择" clearable @change="gradeTypeChange">
              <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学科：" prop="SubjectId" >
            <el-select v-model="form.SubjectId" placeholder="请选择" clearable>
              <el-option v-for="option in subjectOption" :label="option.Name" :value="option.Id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="年级：" prop="GradeId" >
            <el-select v-model="form.GradeId" placeholder="请选择" clearable>
              <el-option v-for="option in gradeOption" :label="option.GradeName" :value="option.GradeId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学期：" prop="Term" >
            <el-select v-model="form.Term" placeholder="请选择" clearable>
              <el-option v-for="option in termOption" :label="option.name" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="教材年份：" prop="Year" >
            <el-input v-model="form.Year" placeholder="请输入排序"></el-input>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="版本：" prop="Version" >
            <el-input v-model="form.Version" placeholder="请输入版本"></el-input>
          </el-form-item>
          <el-form-item label="课程类型：" >
            <el-input v-model="form.CourseType" placeholder="请输入层级" disabled></el-input>
          </el-form-item>
          <el-form-item label="教材类型：" >
            <el-input v-model="form.TextbookType" placeholder="请输入层级" disabled></el-input>
          </el-form-item>
        </div>
        <el-form-item label="教材封面：" prop="TextbookCover">
          <el-upload
            :on-change="hideUploadMicro"
            :limit="1"
            :action="uploadUrl"
            :headers="headers"
            :on-success="uploadSuccess"
            :on-remove="handleRemove"
            :file-list="CoverfileList"
            list-type="picture-card"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="bottom-view">
        <el-button type="primary" @click="nextStepClick">下一步</el-button>
      </div>
    </div>
    <div v-show="activeStep === 1" class="form-view">
      <div class="add-chapter-view">
        <div class="chapter-toolbar">
          <el-button type="text" icon="el-icon-plus" @click="addUnit(null)">新增单元</el-button>
          <el-button type="text" icon="el-icon-edit" @click="batchAdd">批量添加</el-button>
        </div>
        <div class="chapter-tree">
          <template v-for="(item, idx) in chapterList">
            <ChapterNode
              :key="item.id"
              :node="item"
              :parent="null"
              @add="addUnit"
              @addChild="addChildUnit"
              @remove="removeUnit"
              @edit="editUnit"
            />
          </template>
        </div>
      </div>
      <div class="bottom-view">
        <el-button type="primary" @click="activeStep = 0">上一步</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ChapterNode from '@/views/Base_Manage/DictInformation/textbook/component/chapterNode.vue'
const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      activeStep: 0,
      gradeTypeOption,
      termOption: [ {name: '上学期', value: 1}, {name: '下学期', value: 2} ],
      form: {
        Name: '',
        StageType: '',
        SubjectId: '',
        GradeId: '',
        Term: '',
        Year: '',
        Version: '',
        CourseType: '',
        TextbookType: '',
        TextbookCover: ''
      },
      labelPosition: 'right', // 标签位置
      labelSuffix: ' ', // 标签后缀
      // 配置验证规则
      rules: {
        Name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }],
        StageType: [{ required: true, message: '请输入学段', trigger: 'blur' }],
        SubjectId: [{ required: true, message: '请输入学科', trigger: 'blur' }],
        GradeId: [{ required: true, message: '请输入年级', trigger: 'blur' }],
        Term: [{ required: true, message: '请输入学期', trigger: 'blur' }],
        Year: [{ required: true, message: '请输入教材年份', trigger: 'blur' }],
        Version: [{ required: true, message: '请输入版本', trigger: 'blur' }],
      },
      subjectOption: [],
      gradeOption: [],
      total: 0,
      tableData: [],
      chapterList: [
        // 示例数据
        { id: 1, name: '复习与提高', editing: false, expand: true, children: [] }
      ],
      chapterId: 2, // 用于生成唯一id
    }
  },
  computed: {
    headers() {
      return {
        authorization: 'authorization-text'
      }
    },
    uploadUrl() {
      return `${this.$rootUrl1}/Exam_MicroLesson/MicroLesson/UploadPic`
    },
    CoverfileList() {
      if (this.form.TextbookCover) {
        return [
          {
            name: '',
            url: this.form.TextbookCover
          }
        ]
      }
      return []
    }
  },
  components: {
    ChapterNode
  },
  created() {
    this.getGradeList()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    gradeTypeChange() {
      this.getSubjectList()
    },
    /**
     * 通过学段获取学科
     */
    getSubjectList() {
      this.$http.post('/School/Exam_School/GetSubjects', { SchoolType: this.form.StageType }).then((res) => {
        this.subjectOption = res.Data
      })
    },
    // 获取年级
    getGradeList() {
      this.$uwonhttp.post('/ManagerPersonalsController/ManagerPersonals/GetGrade').then((res) => {
        this.gradeOption = res.data.Data
      })
    },
    // 封面-上传成功
    uploadSuccess(file, fileList) {
      this.form.TextbookCover = file.Data
    },
    // 封面上传
    hideUploadMicro(file, fileList) {
      this.hideUpload = fileList.length >= 1
    },
    // 封面-删除
    handleRemove(file, fileList) {
      this.form.TextbookCover = ''
      this.hideUpload = fileList.length >= 1
    },
    nextStepClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.activeStep = 1
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    addUnit(parent, refNode) {
      const newNode = {
        id: this.chapterId++,
        name: '',
        editing: true,
        expand: true,
        children: []
      }
      if (!parent) {
        // 新增一级
        if (refNode) {
          const idx = this.chapterList.indexOf(refNode)
          this.chapterList.splice(idx + 1, 0, newNode)
        } else {
          this.chapterList.push(newNode)
        }
      } else {
        // 新增同级
        const idx = parent.children.indexOf(refNode)
        parent.children.splice(idx + 1, 0, newNode)
      }
    },
    addChildUnit(node) {
      if (!node.children) this.$set(node, 'children', [])
      node.children.push({
        id: this.chapterId++,
        name: '',
        editing: true,
        expand: true,
        children: []
      })
      node.expand = true
    },
    removeUnit(parent, node) {
      if (!parent) {
        const idx = this.chapterList.indexOf(node)
        if (idx > -1) this.chapterList.splice(idx, 1)
      } else {
        const idx = parent.children.indexOf(node)
        if (idx > -1) parent.children.splice(idx, 1)
      }
    },
    editUnit(node) {
      node.editing = true
      this.$nextTick(() => {
        // 自动聚焦
      })
    },
    batchAdd() {
      console.log('批量添加', this.chapterList);
      
      // 可扩展批量添加逻辑
      this.$message('批量添加功能待实现')
    }
  }
}
</script>

<style lang="less" scoped>
.addKnowledge{
  padding: 20px;
  background-color: #FFFFFF;
  height: 80vh;
  .header-view{
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #E4E7ED;
    .header-left{
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
  .steps-view{
    padding: 20px 60px;
  }
  .form-item-view{
    width: 70%;
    display: flex;
    align-items: center;
  }
  .form-view{
    padding-top: 20px;
  }
  /* 为必填项的星号添加红色样式 */
  .el-form-item.is-required .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  .bottom-view{
    display: flex;
    justify-content: center;
  }
  .add-chapter-view {
    padding: 20px;
    .chapter-toolbar {
      margin-bottom: 10px;
      .el-button {
        color: #5cb488;
        font-size: 16px;
        margin-right: 16px;
      }
    }
    .chapter-tree {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      min-height: 200px;
      width: 40%;
    }
  }
}
</style>