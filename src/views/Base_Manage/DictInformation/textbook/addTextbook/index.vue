<template>
  <div class="addKnowledge">
    <div class="header-view">
      <div class="header-left" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>{{ $route.query.id ? '编辑教材' : '新增教材' }}</span>
      </div>
    </div>
    <div class="steps-view">
      <el-steps :active="activeStep" finish-status="success">
        <el-step title="基础信息"></el-step>
        <el-step title="添加单元章节"></el-step>
      </el-steps>
    </div>
    <div v-show="activeStep === 0" class="form-view">
      <!-- 添加 :label-position="labelPosition" :label-suffix="labelSuffix" 属性 -->
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="demo-form-inline" :label-position="labelPosition" :label-suffix="labelSuffix">
        <div class="form-item-view">
          <el-form-item label="教材名称：" prop="Name" >
            <el-input v-model="form.Name" placeholder="请输入教材名称"></el-input>
          </el-form-item>
          <el-form-item label="学段：" prop="StageType" >
            <el-select v-model="form.StageType" placeholder="请选择" clearable @change="gradeTypeChange">
              <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学科：" prop="SubjectId" >
            <el-select v-model="form.SubjectId" placeholder="请选择" clearable>
              <el-option v-for="option in subjectOption" :label="option.Name" :value="option.Id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="年级：" prop="GradeId" >
            <el-select v-model="form.GradeId" placeholder="请选择" clearable>
              <el-option v-for="option in gradeOption" :label="option.label" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学期：" prop="Term" >
            <el-select v-model="form.Term" placeholder="请选择" clearable>
              <el-option v-for="option in termOption" :label="option.name" :value="option.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="教材年份：" prop="Year" >
            <el-input v-model="form.Year" placeholder="请输入排序"></el-input>
          </el-form-item>
        </div>
        <div class="form-item-view">
          <el-form-item label="版本：" prop="Version" >
            <el-input v-model="form.Version" placeholder="请输入版本"></el-input>
          </el-form-item>
          <!-- <el-form-item label="课程类型：" >
            <el-input v-model="form.CourseType" placeholder="请输入层级" disabled></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="教材类型：" >
            <el-input v-model="form.TextbookType" placeholder="请输入层级" disabled></el-input>
          </el-form-item> -->
        </div>
        <el-form-item label="教材封面：" prop="TextbookCover">
          <el-upload
            :on-change="hideUploadMicro"
            :limit="1"
            :action="uploadUrl"
            :headers="headers"
            :on-success="uploadSuccess"
            :on-remove="handleRemove"
            :file-list="CoverfileList"
            :show-file-list="false"
            list-type="picture-card"
          >
            <img v-if="form.TextbookCover" :src="form.TextbookCover" style="width: 100%;">
            <i v-else class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="bottom-view">
        <el-button type="primary" @click="nextStepClick">下一步</el-button>
      </div>
    </div>
    <div v-show="activeStep === 1" class="form-view">
      <div class="add-chapter-view">
        <div class="chapter-toolbar">
          <el-button type="text" icon="el-icon-plus" @click="addUnit(null)">新增单元</el-button>
          <el-button type="text" icon="el-icon-edit" @click="batchAdd">批量添加</el-button>
        </div>
        <div class="chapter-tree">
          <template v-for="(item, idx) in chapterList">
            <ChapterNode
              :key="item.id"
              :node="item"
              :parent="null"
              @add="addUnit"
              @addChild="addChildUnit"
              @remove="removeUnit"
              @edit="editUnit"
            />
          </template>
        </div>
      </div>
      <div class="bottom-view">
        <el-button type="primary" @click="activeStep = 0">上一步</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { grade } from '@/utils/ywPlatformParams.js'
import * as XLSX from 'xlsx'
import ChapterNode from '@/views/Base_Manage/DictInformation/textbook/component/chapterNode.vue'
const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      activeStep: 0,
      gradeTypeOption,
      termOption: [ {name: '上学期', value: 1}, {name: '下学期', value: 2} ],
      form: {
        Name: '',
        StageType: '',
        SubjectId: '',
        GradeId: '',
        Term: '',
        Year: '',
        Version: '',
        CourseType: '',
        TextbookType: '',
        TextbookCover: ''
      },
      labelPosition: 'right', // 标签位置
      labelSuffix: ' ', // 标签后缀
      // 配置验证规则
      rules: {
        Name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }],
        StageType: [{ required: true, message: '请输入学段', trigger: 'blur' }],
        SubjectId: [{ required: true, message: '请输入学科', trigger: 'blur' }],
        GradeId: [{ required: true, message: '请输入年级', trigger: 'blur' }],
        Term: [{ required: true, message: '请输入学期', trigger: 'blur' }],
        Year: [{ required: true, message: '请输入教材年份', trigger: 'blur' }],
        Version: [{ required: true, message: '请输入版本', trigger: 'blur' }],
      },
      subjectOption: [],
      gradeOption: grade(),
      total: 0,
      tableData: [],
      chapterList: [
        // 示例数据
        { id: 1, name: '复习与提高', editing: false, expand: true, children: [] }
      ],
      chapterId: 2, // 用于生成唯一id
    }
  },
  computed: {
    headers() {
      return {
        authorization: 'authorization-text'
      }
    },
    uploadUrl() {
      return `${this.$rootUrl1}/Exam_MicroLesson/MicroLesson/UploadPic`
    },
    CoverfileList() {
      if (this.form.TextbookCover) {
        return [
          {
            name: '',
            url: this.form.TextbookCover
          }
        ]
      }
      return []
    }
  },
  components: {
    ChapterNode
  },
  activated() {
    this.activeStep = 0
    if (this.$route.query.id) {
      this.getTextbookInfo()
    }
  },
  methods: {
    getTextbookInfo() {
      this.$uwonhttp.get('/Chapter/Chapter/GetTextbookDetail', { params: { textbookId: this.$route.query.id } }).then((res) => {
        const { Data } = res.data
        this.form = Data
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    gradeTypeChange() {
      this.getSubjectList()
    },
    /**
     * 通过学段获取学科
     */
    getSubjectList() {
      this.$http.post('/School/Exam_School/GetSubjects', { SchoolType: this.form.StageType }).then((res) => {
        this.subjectOption = res.Data
      })
    },
    // 获取年级
    getGradeList() {
      this.$uwonhttp.post('/ManagerPersonalsController/ManagerPersonals/GetGrade').then((res) => {
        this.gradeOption = res.data.Data
      })
    },
    // 封面-上传成功
    uploadSuccess(file, fileList) {
      this.form.TextbookCover = file.Data
    },
    // 封面上传
    hideUploadMicro(file, fileList) {
      this.hideUpload = fileList.length >= 1
    },
    // 封面-删除
    handleRemove(file, fileList) {
      this.form.TextbookCover = ''
      this.hideUpload = fileList.length >= 1
    },
    nextStepClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.activeStep = 1
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    addUnit(parent, refNode) {
      const newNode = {
        id: this.chapterId++,
        name: '',
        editing: true,
        expand: true,
        children: []
      }
      if (!parent) {
        // 新增一级
        if (refNode) {
          const idx = this.chapterList.indexOf(refNode)
          this.chapterList.splice(idx + 1, 0, newNode)
        } else {
          this.chapterList.push(newNode)
        }
      } else {
        // 新增同级
        const idx = parent.children.indexOf(refNode)
        parent.children.splice(idx + 1, 0, newNode)
      }
    },
    addChildUnit(node) {
      if (!node.children) this.$set(node, 'children', [])
      node.children.push({
        id: this.chapterId++,
        name: '',
        editing: true,
        expand: true,
        children: []
      })
      node.expand = true
    },
    removeUnit(parent, node) {
      console.log('删除节点:', { parent: parent?.name, node: node?.name });

      // 确认删除操作
      this.$confirm(`确定要删除"${node.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 只删除指定的节点，不影响父节点
        if (!parent) {
          // 删除顶级节点
          const idx = this.chapterList.indexOf(node)
          if (idx > -1) {
            this.chapterList.splice(idx, 1)
            this.$message.success(`已删除顶级节点"${node.name}"`)
            console.log('删除顶级节点成功:', node.name);
          } else {
            this.$message.error('未找到要删除的节点')
          }
        } else {
          // 删除子节点
          if (!parent.children) {
            parent.children = []
          }
          const idx = parent.children.indexOf(node)
          if (idx > -1) {
            parent.children.splice(idx, 1)
            this.$message.success(`已从"${parent.name}"中删除子节点"${node.name}"`)
            console.log('删除子节点成功:', { parent: parent.name, child: node.name });
          } else {
            this.$message.error('未找到要删除的子节点')
          }
        }
      }).catch(() => {
        console.log('用户取消删除操作');
      })
    },
    editUnit(node) {
      node.editing = true
      this.$nextTick(() => {
        // 自动聚焦
      })
    },
    // 批量添加
    batchAdd() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.xlsx,.xls'
      input.style.display = 'none'

      input.onchange = (event) => {
        const file = event.target.files[0]
        if (!file) return

        // 验证文件类型
        const fileName = file.name.toLowerCase()
        if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
          this.$message.error('请选择Excel文件（.xlsx或.xls格式）')
          return
        }

        this.parseExcelFile(file)
      }

      // 触发文件选择
      document.body.appendChild(input)
      input.click()
      document.body.removeChild(input)
    },

    // 解析Excel文件
    parseExcelFile(file) {
      const reader = new FileReader()

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result)
          const workbook = XLSX.read(data, { type: 'array' })

          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]

          // 将工作表转换为JSON数组
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1, // 使用数组格式而不是对象格式
            defval: '' // 空单元格的默认值
          })

          console.log('Excel原始数据:', jsonData)

          // 解析数据并构建章节树
          this.buildChapterTree(jsonData)

        } catch (error) {
          console.error('解析Excel文件失败:', error)
          this.$message.error('解析Excel文件失败，请检查文件格式')
        }
      }

      reader.onerror = () => {
        this.$message.error('读取文件失败')
      }

      reader.readAsArrayBuffer(file)
    },

    // 构建章节树结构
    buildChapterTree(excelData) {
      if (!excelData || excelData.length === 0) {
        this.$message.warning('Excel文件为空')
        return
      }

      // 过滤掉空行
      const validRows = excelData.filter(row =>
        row && row.some(cell => cell && cell.toString().trim() !== '')
      )

      if (validRows.length === 0) {
        this.$message.warning('Excel文件中没有有效数据')
        return
      }

      console.log('有效数据行:', validRows)

      const newChapterList = []
      let currentId = this.chapterId

      // 用于跟踪各级别的父节点
      const levelParents = {}

      validRows.forEach((row, rowIndex) => {
        // 处理每一行数据
        for (let colIndex = 0; colIndex < row.length; colIndex++) {
          const cellValue = row[colIndex]
          if (!cellValue || cellValue.toString().trim() === '') {
            continue
          }

          const chapterName = cellValue.toString().trim()
          const level = colIndex + 1 // 第一列为第一级，第二列为第二级

          // 创建章节对象
          const chapter = {
            id: currentId++,
            name: chapterName,
            editing: false,
            expand: true,
            children: []
          }

          if (level === 1) {
            // 第一级章节，直接添加到根级别
            // 检查是否已存在同名章节
            const existingChapter = newChapterList.find(c => c.name === chapterName)
            if (!existingChapter) {
              newChapterList.push(chapter)
              levelParents[1] = chapter
            } else {
              levelParents[1] = existingChapter
            }
          } else {
            // 子级章节，需要找到父节点
            const parentLevel = level - 1
            const parent = levelParents[parentLevel]

            if (parent) {
              // 检查父节点下是否已存在同名子章节
              const existingChild = parent.children.find(c => c.name === chapterName)
              if (!existingChild) {
                parent.children.push(chapter)
                levelParents[level] = chapter
              } else {
                levelParents[level] = existingChild
              }
            } else {
              console.warn(`第${rowIndex + 1}行第${colIndex + 1}列: 找不到第${parentLevel}级父节点，跳过"${chapterName}"`)
            }
          }
        }
      })

      // 更新章节列表和ID计数器
      this.chapterList = newChapterList
      this.chapterId = currentId

      console.log('构建的章节树:', this.chapterList)

      this.$message.success(`成功导入 ${validRows.length} 行数据，共生成 ${this.countTotalChapters(newChapterList)} 个章节`)
    },

    // 统计章节总数（递归）
    countTotalChapters(chapters) {
      let count = 0
      chapters.forEach(chapter => {
        count += 1
        if (chapter.children && chapter.children.length > 0) {
          count += this.countTotalChapters(chapter.children)
        }
      })
      return count
    },
    submitForm() {
      // 递归转换章节数据格式
      const formatChapterData = (chapters) => {
        return chapters.map((chapter, index) => {
          const formattedChapter = {
            ChapterName: chapter.name,
            chapter: chapter.children && chapter.children.length > 0 ? formatChapterData(chapter.children) : [],
            Orderid: index + 1
          }
          return formattedChapter
        })
      }

      const formattedChapterInfo = formatChapterData(this.chapterList)

      const params = {
        Id: this.$route.query.id,
        ...this.form,
        ChapterInfo: formattedChapterInfo
      }

      if (this.$route.query.id) {
        this.$uwonhttp.post('/Chapter/Chapter/UpdateTextbook', params).then((res) => {
          if (res.data.Success) {
            this.$message.success('教材更新成功')
            // 可以添加跳转逻辑或其他成功处理
            this.goBack()
          } else {
            this.$message.error(res.data.Msg || '更新失败')
          }
        }).catch((error) => {
          this.$message.error(error.message || '更新失败')
        })
      } else {
        this.$uwonhttp.get('/Chapter/Chapter/AddBaseTextbookDict', { params }).then((res) => {
          if (res.data.Success) {
            this.$message.success('教材创建成功')
            // 可以添加跳转逻辑或其他成功处理
            this.goBack()
          } else {
            this.$message.error(res.data.Msg || '创建失败')
          }
        }).catch((error) => {
          this.$message.error(error.message || '创建失败')
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.addKnowledge{
  padding: 20px;
  background-color: #FFFFFF;
  height: 80vh;
  .header-view{
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #E4E7ED;
    .header-left{
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
  .steps-view{
    padding: 20px 60px;
  }
  .form-item-view{
    width: 70%;
    display: flex;
    align-items: center;
  }
  .form-view{
    padding-top: 20px;
  }
  /* 为必填项的星号添加红色样式 */
  .el-form-item.is-required .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  .bottom-view{
    display: flex;
    justify-content: center;
  }
  .add-chapter-view {
    padding: 20px;
    .chapter-toolbar {
      margin-bottom: 10px;
      .el-button {
        color: #5cb488;
        font-size: 16px;
        margin-right: 16px;
      }
    }
    .chapter-tree {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      min-height: 200px;
      width: 40%;
    }
  }
}
</style>