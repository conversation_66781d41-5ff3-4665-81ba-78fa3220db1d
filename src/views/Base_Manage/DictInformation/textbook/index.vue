<template>
  <div class="textbook">
    <div class="form-view">
      <el-form ref="form" :inline="true" :model="form">
        <el-form-item label="教材名称：">
          <el-input v-model="form.TextbookName"></el-input>
        </el-form-item>
        <el-form-item label="学段：">
          <el-select v-model="form.StageType" placeholder="请选择" clearable @change="gradeTypeChange">
            <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学科：">
          <el-select v-model="form.Platform" placeholder="请选择">
            <el-option label="全部" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级：">
          <el-input v-model="form.VersionCode"></el-input>
        </el-form-item>
        <el-form-item label="学期：">
          <el-input v-model="form.VersionCode"></el-input>
        </el-form-item>
        <el-form-item label="教材年份：">
          <el-input v-model="form.VersionCode"></el-input>
        </el-form-item>
        <el-form-item label="版本：">
          <el-input v-model="form.VersionCode"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="medium" @click="onSubmit">查询</el-button>
          <el-button size="medium" @click="onSubmit">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-view">
      <div class="table-view-header">
        <el-button type="primary" size="medium" @click="addTextbook">新增教材</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="date" label="日期" width="180"></el-table-column>
        <el-table-column prop="name" label="姓名" width="180"></el-table-column>
        <el-table-column prop="address" label="地址"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      gradeTypeOption,
      form: {
        
      },
      total: 0,
      tableData: [],
    }
  },
  components:{
    
  },
  created() {
    
  },
  methods: {
    addTextbook() {
      this.$router.push({path: '/Base_Manage/DictInformation/textbook/addTextbook/index'})
    }
  }
}
</script>

<style lang="less" scoped>
.textbook{
  .form-view{
    margin-bottom: 20px;
    padding: 20px;
    background-color: #FFFFFF;
  }
  .table-view{
    padding: 20px;
    background-color: #FFFFFF;
  }
  .table-view-header{
    padding-bottom: 20px;
  }
}
</style>