<template>
  <div class="textbook">
    <div class="form-view">
      <el-form ref="form" :inline="true" :model="form">
        <el-form-item label="教材名称：">
          <el-input v-model="form.TextbookName"></el-input>
        </el-form-item>
        <el-form-item label="学段：">
          <el-select v-model="StageType" placeholder="请选择" clearable @change="gradeTypeChange">
            <el-option v-for="option in gradeTypeOption" :label="option.name" :value="option.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学科：">
          <el-select v-model="form.id" placeholder="请选择" clearable>
            <el-option v-for="option in subjectOption" :label="option.Name" :value="option.Id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年级：" prop="GradeId" >
          <el-select v-model="form.Gradeid" placeholder="请选择" clearable>
            <el-option v-for="option in gradeOption" :label="option.label" :value="option.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学期：" prop="Term" >
          <el-select v-model="form.Term" placeholder="请选择" clearable>
            <el-option v-for="option in termOption" :label="option.name" :value="option.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="教材年份：">
          <el-input v-model="form.TextbookYear"></el-input>
        </el-form-item>
        <el-form-item label="版本：">
          <el-input v-model="form.PublishName"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="medium" @click="getTextbookList">查询</el-button>
          <el-button size="medium" @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-view">
      <div class="table-view-header">
        <el-button type="primary" size="medium" @click="addTextbook">新增教材</el-button>
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f5f5', textAlign: 'center' }"
        :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="TextbookName" label="教材名称" width="180"></el-table-column>
        <el-table-column prop="SubjectName" label="学科" width="180"></el-table-column>
        <el-table-column prop="Grade" label="年级" width="180"></el-table-column>
        <el-table-column prop="TermName" label="学期" width="180"></el-table-column>
        <el-table-column prop="Year" label="教材年份" width="180"></el-table-column>
        <el-table-column prop="PublishName" label="版本" width="180"></el-table-column>
        <el-table-column prop="ModifyTime" label="修改时间" width="180"></el-table-column>
        <el-table-column prop="Enable" label="状态" width="180">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.Enable" :active-value="1" :inactive-value="0" @change="switchIslock(scope.row.Enable,scope.row.Id,scope.$index)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" center>
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="editTextbook(scope.row)">编辑</el-button>
            <el-button type="text" size="mini" @click="deleteTextbook(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pageFlex">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="form.PageIndex"
        :page-size="form.PageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { grade } from '@/utils/ywPlatformParams.js'
const gradeTypeOption = [{name: '小学', value: 2},{name: '初中', value: 3},{name: '高中', value: 5}]
export default {
  name: 'Textbook',
  data () {
    return {
      gradeTypeOption,
      gradeOption: grade,
      termOption: [ {name: '上学期', value: 1}, {name: '下学期', value: 2} ],
      StageType: '',
      form: {
        PageIndex: 1,
        PageSize: 20,
        TextbookName: '',
        id: '',
        Gradeid: '',
        Term: '',
        TextbookYear: '',
        PublishName: '',
      },
      total: 0,
      tableData: [],
      subjectOption: [],
    }
  },
  components:{
    
  },
  activated() {
    this.getTextbookList()
    
  },
  methods: {
    gradeTypeChange() {
      this.getSubjectList()
    },
    /**
     * 通过学段获取学科
     */
    getSubjectList() {
      this.$http.post('/School/Exam_School/GetSubjects', { SchoolType: this.StageType }).then((res) => {
        this.subjectOption = res.Data
      })
    },
    resetForm() {
      this.$refs.form.resetFields()
    },
    getTextbookList() {
      this.$uwonhttp.post('/Chapter/Chapter/GetBaseTextbookDictList', this.form).then((res) => {
        this.tableData = res.data.Data.Items
        this.total = res.data.Data.TotalItems
      })
    },
    handleCurrentChange(page) {
      this.form.PageIndex = page
      this.getTextbookList()
    },
    // 启用禁用
    switchIslock(Enable,Id,index) {
      this.$uwonhttp.post('/Chapter/Chapter/UpdateStatus', { TextbookId: Id, Status: Enable==1 ? true : false }).then((res) => {
        if (res.data.Success) {
          this.$message.success('操作成功')
        } else {
          this.$message.error(res.data.Msg)
          this.tableData[index].Enable = !Enable
        }
      })
    },
    // 编辑
    editTextbook(row) {
      this.$router.push({path: '/Base_Manage/DictInformation/textbook/addTextbook/index', query: {id: row.Id}})
    },
    // 删除
    deleteTextbook(row) {
      this.$confirm('确认删除该教材吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$uwonhttp.post('/Chapter/Chapter/DeleteBaseTextbookDict', { id: row.Id }).then((res) => {
          if (res.data.Success) {
            this.$message.success('删除成功')
            this.getTextbookList()
          } else {
            this.$message.error(res.data.Msg)
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    addTextbook() {
      this.$router.push({path: '/Base_Manage/DictInformation/textbook/addTextbook/index'})
    }
  }
}
</script>

<style lang="less" scoped>
.textbook{
  .form-view{
    margin-bottom: 20px;
    padding: 20px;
    background-color: #FFFFFF;
  }
  .table-view{
    padding: 20px;
    background-color: #FFFFFF;
  }
  .table-view-header{
    padding-bottom: 20px;
  }
}
.pageFlex{
  display: flex;
  justify-content: flex-end;
}
</style>