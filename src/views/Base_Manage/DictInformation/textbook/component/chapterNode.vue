<template>
  <div class="chapter-node" :class="{ 'is-editing': node.editing }">
    <div class="node-row" :style="{ background: node.editing ? '#f6faf7' : '#f6faf7' }">
      <span v-if="node.children && node.children.length" @click="node.expand = !node.expand" class="expand-arrow">
        <i :class="node.expand ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
      </span>
      <span v-else style="display:inline-block;width:16px"></span>
      <el-input
        v-if="node.editing"
        v-model="node.name"
        size="mini"
        @blur="node.editing = false"
        @keyup.enter.native="node.editing = false"
        style="width:180px"
        autofocus
      />
      <span v-else @dblclick="edit" style="width:180px;display:inline-block;">{{ node.name }}</span>
      <span class="node-actions">
        <el-dropdown trigger="click">
          <span class="el-dropdown-link">
            <i class="el-icon-plus"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="addSameLevel">同级</el-dropdown-item>
            <el-dropdown-item @click.native="addChild">子级</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <i class="el-icon-edit" @click="edit"></i>
        <i class="el-icon-delete" @click="remove"></i>
      </span>
    </div>
    <div v-show="node.expand" class="children" v-if="node.children && node.children.length">
      <chapter-node
        v-for="(child, i) in node.children"
        :key="child.id"
        :node="child"
        :parent="node"
        @add="$emit('add', node, child)"
        @addChild="$emit('addChild', child)"
        @remove="$emit('remove', node, child)"
        @edit="$emit('edit', child)"
      />
    </div>
  </div>
</template>

<script>

export default {
  name: 'ChapterNode',
  props: {
    node: {
      type: Object,
      default: () => ({})
    },
    parent: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      
    }
  },
  components:{
    
  },
  created() {
    
  },
  methods: {
    addSameLevel() {
      this.$emit('add', this.parent, this.node)
    },
    addChild() {
      this.$emit('addChild', this.node)
    },
    remove() {
      this.$emit('remove', this.parent, this.node)
    },
    edit() {
      this.$emit('edit', this.node)
    }
  }
}
</script>

<style lang="less" scoped>
.chapter-node {
  margin-left: 20px;
  .node-row {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 4px;
    background: #f6faf7;
    &:hover {
      background: #eaf7f0;
    }
    .expand-arrow {
      cursor: pointer;
      margin-right: 4px;
    }
    .node-actions {
      margin-left: auto;
      display: flex;
      align-items: center;
      i, .el-dropdown-link {
        margin-left: 8px;
        cursor: pointer;
        color: #5cb488;
      }
    }
  }
  .children {
    margin-left: 24px;
  }
}
</style>