<template>
  <div class="versions">
    
  </div>
</template>

<script>

export default {
  name: 'Textbook',
  data () {
    return {
      form: {
        
      },
      total: 0,
      tableData: [],
    }
  },
  components:{
    
  },
  created() {
    
  },
  methods: {
    
  }
}
</script>

<style lang="less" scoped>
.versions{
  padding: 20px;
  background-color: #FFFFFF;
  .form_button{
    display: flex;
    justify-content: space-between;
  }
  .pageFlex{
    display: flex;
    justify-content: right;
    padding-top: 10px;
  }
}
</style>