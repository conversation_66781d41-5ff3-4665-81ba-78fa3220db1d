<template>
  <div style="height: 100%;">
    <!-- 1 学生 2 教师 -->
    <!-- v-if="id === '-1' || id === '9999' || id === '2' || isTeacher" -->
    <Student v-if="isStudent"></Student>
<!--    <newTeacher v-if="role === '教师' && $store.state.LoginSource == 1"></newTeacher>-->
    <newHomepage v-if="role === '教师'"></newHomepage>
    <!--  三个助手教师  -->
    <!-- <testingWork v-if="role === '教师' && $store.state.LoginSource == 2" ></testingWork> -->
    <!-- <teacher v-if="role === '教师'"></teacher> -->
<!--    <schoolhead v-if="role === '校领导' || role === '学校管理员' || id === 9999 || id === 9 || id === 8"></schoolhead>-->
    <new-schoolhead v-if="role === '校领导' || role === '学校管理员' || id === 9999 || id === 9 || id === 8"></new-schoolhead>
    <!--  产品于定甲提出  -->
    <newMainPage v-if="role === '教研组长'"></newMainPage>
    <!-- <teacherStaff v-if="role === '教研员'"></teacherStaff> -->
    <!-- <Mandishome v-if="role === '教研员'"></Mandishome> -->
     <!-- NewMand组件是新页面，data-analysis是黄埔验收专用，此功能只在test环境上用 -->
    <!-- <NewMand v-if="role === '教研员'"></NewMand> --> 
    <data-analysis v-if="role === '教研员'"></data-analysis>
    <CityLeaders v-if="role === '市领导'"></CityLeaders>
    <TaskCode v-if="role === '初中教师'"></TaskCode>
    <TaskStaff v-if="role === '初中教研员'"></TaskStaff>
    <!-- <a-card :bordered="false">
      <div style="text-align:center">
        <img src="@/assets/default.png" />  
      </div>
    </a-card> -->
  </div>
</template>
<script>
// import schoolhead from '@/views/SchoolManager/HomePage'
import Student from '@/components/StudentIndex/StudentIndex'
import teacher from '@/components/teacherIndex/teacherIndex'
import teacherStaff from '@/views/TeachingFellow/TrialPaper'
import Mandishome from '@/components/MandisHome/Mandishome'
import CityLeaders from '@/views/CityLeadersPgae/TeachingCenter/index'
import TaskCode from '@/views/TaskCode/index'
import TaskStaff from '@/views/TaskStaff/index'
// 教师端新版首页
import newTeacher from '@/views/Teacher/My_Task/AllPractice'
// import newHomepage from '@/views/Teacher/My_Task/newHomepage'
import newHomepage from '@/views/threeAssistants/newHome/index'
// 教研首页
import NewMand from '@/views/RegionManage/MandisHome'
// 新黄浦验收数据统计组件
import dataAnalysis from '@/views/RegionManage/data-analysis/index'
// 三个助手首页
import testingWork from '@/views/threeAssistants/testingWork'
// 于定甲最新版数据概况管理端
import newSchoolhead from '@/views/SchoolManager/newHomePage'
// 教研组长首页(于定甲)
import newMainPage from '@/views/SchoolManager/LearningEvaluation/newMainPage'
export default {
  components: {
    Student,
    teacher,
    // schoolhead,
    teacherStaff,
    Mandishome,
    newTeacher,
    NewMand,
    CityLeaders,
    TaskCode,
    TaskStaff,
    testingWork,
    newSchoolhead,
    newHomepage,
    newMainPage,
    dataAnalysis

  },
  created() {
    // this.id = this.$route.query.id
    this.id = localStorage.getItem('Id')
    this.mobilephone = localStorage.getItem('mobilePhone')
    // this.isTeacher = localStorage.getItem('isTeacher')
    // this.isStudent = localStorage.getItem('isStudent')
    this.isTeachingStaff = localStorage.getItem('isTeachingStaff')
    this.getUserInfo()
  },
  mounted() {
    // this.getRoles()
  },
  watch: {
    $route: {
      handler: function (nv, ov) {
        if (nv.path == '/Home/Introduce') {
          this.id = localStorage.getItem('Id')
          this.mobilephone = localStorage.getItem('mobilePhone')
          this.isTeachingStaff = localStorage.getItem('isTeachingStaff')
          this.getUserInfo()
        }
      },
    },
  },
  data() {
    return {
      id: '',
      mobilephone: '',
      isTeacher: false,
      isStudent: false,
      isSchoolHeader: false,
      isTeachingStaff: '0',
      role: '',
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.mobilephone }).then((res) => {
        const identityId = localStorage.getItem('role')
        const roles = res.Data.Roles
        localStorage.setItem('SCHOOLNAME', res.Data.SchoolName) //true-隐藏0 false-不隐藏 1
        localStorage.setItem('IsTY', res.Data.IsTY ? '0' : '1') //true-隐藏0 false-不隐藏 1 是否三个助手学校 true-是 false-不是
        localStorage.setItem('IsZKZL', res.Data.IsZKZL) //专课专练学校 1-是 0-不是
        localStorage.setItem('IsDZBQX', res.Data.IsDZBQX)
        localStorage.setItem('LoginSource', res.Data.LoginSource) // 1:专科专练登录,2:三个助手登录
        this.$store.commit('set_LoginSource', res.Data.LoginSource)
        this.$store.commit('user/SET_YEAR_NUMS', res.Data.Year || new Date().getFullYear())
        this.$store.commit('user/SET_YEAR_DEFAULT', res.Data.Year || new Date().getFullYear())
        if (!res.Data.Roles) {
          this.isStudent = true
          localStorage.setItem('T_S_CL_P0', 'list')
        } else {
          roles.forEach((value) => {
            if (value.Id === identityId) {
              this.role = value.RoleName
            }
          })
        }
        this.isTeacher = true
      })
    },
  },
}
</script>
