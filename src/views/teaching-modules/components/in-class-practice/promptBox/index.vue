<template>
  <div class="prompt-box-container position-header" style="top: 0">
    <div class="prompt-box">
      <h1 class="title">{{ title }}</h1>
      <h3 class="subhead">请同学们在<span style="color: #90BBA0;">{{ tipText }}</span>上进行作答</h3>
      <span class="triangle"></span>
    </div>
    <img class="ai_image" src="@/assets/ai_people.png" alt="" srcset="">
  </div>
</template>

<script>
export default {
  name: 'PromptBox',
  props: {
    title: {
      type: String,
      default: ''
    },
    tipText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  methods: {},
  mounted() {
    // Any initialization logic can go here
  }
};
</script>

<style lang="less" scoped>
.prompt-box-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  // margin: 20px auto;
}
.position-header{
  position: absolute;
  transform: translateX(-50%);
  left: 50%;
  // top: 40px;
  .title,.subhead{
    color: #454545;
    text-align: center;
    font-size: 24px;
  }
  .title{
    color: #3D3D3D;
    margin-bottom: 5px;
    font-size: 28px;
  }
}
.prompt-box {
  position: relative;
  display: inline-block;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  // min-width: 120px;
  width: 360px;
  font-size: 14px;
  color: #333;
}

.ai_image {
  margin-top: 40px;
  margin-left: 30px;
  width: 120px;
  height: 158px;
}
.triangle {
  position: absolute;
  right: -24px;
  top: 60px;
  width: 0;
  height: 0;
  border-top: 18px solid transparent;
  border-bottom: 0 solid transparent;
  border-left: 24px solid #fff;
  /* filter: drop-shadow(-1px 0 0 #e0e0e0); */
}
</style>
