<template>
  <!-- 画笔蒙层 -->
  <div class="canvas-container-main" ref="canvasContainer" v-show="isTransparentBoardShown">
    <canvas ref="transparentCanvasRef" id="transparent-canvas"></canvas>
  </div>
</template>

<script>
import { fabric } from 'fabric-with-erasing'
import { debounce } from 'lodash'

export default {
  name: 'DrawingBoard',
  props: {
    isTransparentBoardShown: {
      type: Boolean,
      default: false
    },
    transparentDrawingSettings: {
      type: Object,
      default: () => ({})
    },
    parentRefs: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isActivated: true, // 组件是否激活

      // ppt笔迹
      pptHandwriting: {
        curryPage: 1, // 页码
        // 页码对应存储的笔迹数据
        handwritingMap: {}
      }
    }
  },
  computed: {
    // 上课的呈现方式 courseware :课件 | answerSheet:答题卡 | a4Canvas:4A白板
    teachingType() {
      return this.$store.state.answer.teachingModule.teachingType
    },
    homeWorkType() {
      return this.$store.state.answer.teachingModule.homeWorkType
    },
    // 画板信息
    artboardInfo() {
      return this.$store.state.answer.artboardInfo
    }
  },
  methods: {
    initCanvas() {
      // 获取容器尺寸
      const container = this.$refs.canvasContainer

      const width = container.offsetWidth
      const height = container.offsetHeight

      // 销毁已存在的canvas实例
      if (this.transparentCanvas) {
        this.transparentCanvas.dispose()
      }

      try {
        // 创建新的canvas实例
        this.transparentCanvas = new fabric.Canvas(this.$refs.transparentCanvasRef, {
          width,
          height,
          backgroundColor: 'transparent',
          isDrawingMode: false,
          selection: true,
          preserveObjectStacking: true
        })

        // 初始化完成，通知父组件
        this.$emit('onInited', this.transparentCanvas)

        this.transparentCanvas.upperCanvasEl.tabIndex = -1 // 不允许聚焦
        // 设置画布事件监听
        this.transparentCanvas.on('mouse:down', opt => {
          const sidebarBottomRef = this.parentRefs.sidebarBottomRef?.$refs
          if (!sidebarBottomRef) {
            return
          }
          sidebarBottomRef.penToolRef[0]?.setVisible?.()
          const ref = sidebarBottomRef.eraserPopover
          if (ref) {
            ref[0]?.setVisible?.()
          }
        })

        this.transparentCanvas.on('mouse:up', () => {
          if (this.homeWorkType === 'ppt') {
            // 让wps获取焦点
            document.querySelector('.web-office-iframe')?.focus?.()
          }

          //
          this.saveCanvasState() // 保存画布状态
        })

        // 设置初始画笔
        this.transparentCanvas.freeDrawingBrush = new fabric.PencilBrush(this.transparentCanvas)
        this.transparentCanvas.freeDrawingBrush.color = this.transparentDrawingSettings.penColor || '#E84749'
        this.transparentCanvas.freeDrawingBrush.width = 2

        // 如果有待处理的工具设置，立即应用
        if (this.transparentDrawingSettings.tool === 'pen') {
          this.$emit('handleToolChange', this.drawingSettings)
        }

        this.restoreCanvasState()
      } catch (error) {
        console.error('Error initializing canvas:', error)
      }
    },
    async resizeCanvas() {
      const { width, height } = this.artboardInfo

      const container = this.$refs.canvasContainer
      if (!container) {
        return
      }

      // 重新设置宽高
      window.jQuery(container).css({
        width: !isNaN(Number(width)) ? `${width}px` : width,
        height: !isNaN(Number(height)) ? `${height}px` : height
      })

      // 等待dom更新完成
      await this.$nextTick()

      if (!this.isTransparentBoardShown) {
        // 画板未展示，不处理画布大小变化
        return
      }

      // 过滤异常情况
      if (!container.offsetWidth || !container.offsetHeight) {
        return
      }

      const canvas = this.transparentCanvas
      const oldWidth = canvas.width
      const oldHeight = canvas.height

      // 计算缩放比例
      const scaleX = container.offsetWidth / oldWidth
      const scaleY = container.offsetHeight / oldHeight

      // 调整画布尺寸
      canvas.setDimensions({
        width: container.offsetWidth,
        height: container.offsetHeight
      })

      // 缩放所有对象
      canvas.getObjects().forEach(obj => {
        obj.scaleX *= scaleX
        obj.scaleY *= scaleY
        obj.left *= scaleX
        obj.top *= scaleY
        obj.setCoords() // 更新坐标
      })

      canvas.renderAll()
    },
    reCalcCanvasRect() {
      if (!this.isActivated) {
        return
      }
      // 作业
      if (
        ['a4Canvas', 'answerSheet', 'inClassPractice', 'interactiveExerciseBook', 'inLinePaper']
          .includes(this.teachingType)
      ) {
        this.parentRefs[({
          a4Canvas: 'a4CanvasRef',
          answerSheet: 'answerSheetRef',
          inClassPractice: 'inClassPracticeRef',
          interactiveExerciseBook: 'interactiveExerciseBookRef',
          inLinePaper: 'inLinePaperRef'
        })[this.teachingType]]?.getSize?.().then(size => {
          this.$store.commit('answer/SET_ART_BOARD_INFORMATION', [
            { k: 'width', value: size.width ?? '100%' },
            { k: 'height', value: size.height ?? '100%' }
          ])
        })
        return
      }

      // 课件
      if (this.teachingType === 'courseware') {
        this.parentRefs.coursewareRef?.getSize?.().then(size => {
          this.$store.commit('answer/SET_ART_BOARD_INFORMATION', [
            { k: 'width', value: size.width ?? '100%' },
            { k: 'height', value: size.height ?? '100%' }
          ])
        })
      }

      // 兜底,未匹配到类型，默认100%
      if (this.teachingType !== 'courseware') {
        this.$store.commit('answer/SET_ART_BOARD_INFORMATION', [
          { k: 'width', value: '100%' },
          { k: 'height', value: '100%' }
        ])
      }
    },
    clearAndHideCanvas() {
      if (this.transparentCanvas) {
        this.transparentCanvas.clear()
        this.previousCanvasState = {}
      }
      this.$emit('close')
    },
    saveCanvasState() {
      // 如果是ppt播放界面，保存ppt画笔的状态
      if (
        (
          this.teachingType === 'courseware' &&
          this.homeWorkType === 'ppt' &&
          this.pptHandwriting.curryPage &&
          // 非互动题状态
          !this.$store.state.answer.questionInfo.studentItem?.StudentId
        ) ||
        // A4 和 互动练习簿
        (['a4Canvas', 'interactiveExerciseBook'].includes(this.teachingType))
      ) {
        // 根据当前页码保存对应笔迹
        this.pptHandwriting.handwritingMap[this.pptHandwriting.pptFileId][this.pptHandwriting.curryPage] = {
          width: this.transparentCanvas.width,
          height: this.transparentCanvas.height,
          objects: this.transparentCanvas.toJSON()
        }
        return
      }

      this.previousCanvasState = {
        width: this.transparentCanvas.width,
        height: this.transparentCanvas.height,
        backgroundColor: this.transparentCanvas.backgroundColor,
        objects: this.transparentCanvas.getObjects().map(obj => obj.toJSON())
      }
    },

    /**
     * ppt 切换页码
     * @param curryPage
     * @param init
     * @param pptFileId
     */
    curryPageChange({ curryPage, init, pptFileId }) {
      let target = this.pptHandwriting.handwritingMap[pptFileId]
      if (!target) {
        this.$set(this.pptHandwriting.handwritingMap, pptFileId, {})
        target = this.pptHandwriting.handwritingMap[pptFileId]
      }
      /**
       * 1、保存当前页码的笔迹数据
       * 2、清空fabric
       * 3、获取当前页码笔迹数据，并更新fabric
       */
      if (!init) {
        target[this.pptHandwriting.curryPage] = {
          width: this.transparentCanvas.width,
          height: this.transparentCanvas.height,
          objects: this.transparentCanvas.toJSON()
        }
        this.transparentCanvas.clear()
      }

      // 取出之前的
      if (target[curryPage]) {
        this.transparentCanvas.loadFromJSON(target[curryPage].objects, () => {
          const { width, height } = target[curryPage]
          const scaleX = this.transparentCanvas.width / width
          const scaleY = this.transparentCanvas.height / height

          this.transparentCanvas.getObjects().forEach(obj => {
            obj.scaleX *= scaleX
            obj.scaleY *= scaleY
            obj.left *= scaleX
            obj.top *= scaleY
            obj.setCoords() // 更新坐标
          })

          this.transparentCanvas.renderAll()
        })
      }
      this.pptHandwriting.curryPage = curryPage
      this.pptHandwriting.pptFileId = pptFileId // 当前的pptFileId
    },
    restoreCanvasState() {
      // 教学非互动题状态
      if (
        (this.homeWorkType === 'ppt' && !this.$store.state.answer.questionInfo.studentItem?.StudentId) ||
        // A4 和 互动练习簿
        (['a4Canvas', 'interactiveExerciseBook'].includes(this.teachingType))
      ) {
        // 取出之前的ppt内容
        const target = this.pptHandwriting.handwritingMap?.[this.pptHandwriting.pptFileId]?.[this.pptHandwriting.curryPage]
        if (target) {
          this.transparentCanvas.loadFromJSON(target.objects, () => {
            const { width, height } = target
            const scaleX = this.transparentCanvas.width / width
            const scaleY = this.transparentCanvas.height / height

            this.transparentCanvas.getObjects().forEach(obj => {
              obj.scaleX *= scaleX
              obj.scaleY *= scaleY
              obj.left *= scaleX
              obj.top *= scaleY
              obj.setCoords() // 更新坐标
            })

            this.transparentCanvas.renderAll()
          })
        }
        return
      }

      if (this.previousCanvasState && this.previousCanvasState.objects) {
        this.transparentCanvas.clear()
        this.transparentCanvas.setBackgroundColor(this.previousCanvasState.backgroundColor, () => {
          this.transparentCanvas.loadFromJSON({ objects: this.previousCanvasState.objects }, () => {
            const { width, height } = this.previousCanvasState
            const scaleX = this.transparentCanvas.width / width
            const scaleY = this.transparentCanvas.height / height

            this.transparentCanvas.getObjects().forEach(obj => {
              obj.scaleX *= scaleX
              obj.scaleY *= scaleY
              obj.left *= scaleX
              obj.top *= scaleY
              obj.setCoords() // 更新坐标
            })

            this.transparentCanvas.renderAll()
          })
        })
      }
    },

    clearCanvas() {
      if (!this.transparentCanvas) {
        return
      }
      this.transparentCanvas.clear()
      this.transparentCanvas.setBackgroundColor(
        'transparent',
        this.transparentCanvas.renderAll.bind(this.transparentCanvas)
      )
      this.saveCanvasState()
    },

    updateDrawingSettings(drawingSettings) {
      this.drawingSettings = drawingSettings
    }
  },
  watch: {
    teachingType: {
      handler() {
        // 清空画板
        if (this.transparentCanvas) {
          this.transparentCanvas.clear()
          this.previousCanvasState = {}
        }
        this.$nextTick(() => {
          this.reCalcCanvasRect()
        })
      },
      deep: true,
      immediate: true
    },
    artboardInfo: {
      handler(val) {
        this.resizeCanvas()
      },
      deep: true
    },

    isTransparentBoardShown(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initCanvas()
          // 重新设置宽高
          this.reCalcCanvasRect()
        })
      }
    }
  },
  mounted() {
    if (fabric) {
      this.initCanvas()
    } else {
      console.error('Fabric.js not loaded')
    }

    setTimeout(() => {
      // 监听尺寸变化，重新设置画布大小
      if (window.ResizeObserver) {
        const ob = new ResizeObserver(debounce(entries => {
          entries.forEach(entry => {
            if (!entry.contentRect) {
              return
            }
            this.$nextTick(() => {
              this.reCalcCanvasRect()
            })
          })
        }, 100))
        this.__ob = ob
        ob.observe(this.parentRefs.leftSpliceRef.$el)
      } else {
        this.resizeFn = debounce(() => this.reCalcCanvasRect(), 100)
        window.addEventListener('resize', this.resizeFn)
      }
    })
  },
  activated() {
    this.isActivated = true
  },
  deactivated() {
    this.isActivated = false
  },
  beforeDestroy() {
    this.__ob && this.__ob.disconnect()
    this.resizeFn && window.removeEventListener('resize', this.resizeFn)
  }
}
</script>

<style lang="scss" scoped>

</style>