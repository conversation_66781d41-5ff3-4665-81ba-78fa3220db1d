<template>
  <div class="homePage">
    <div
      class="margin_r_5 back_white h-100 flex_item_shrink "
      style="transition: all ease 0.3s;"
      :class="{ openInDialogBorder: openInDialog }"
      :style="{ width: leftWidth + '%' }"
    >
      <left-screening ref="leftScreening" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 16 ? 2.3 : 16" @getAllList="getLeftScreening" @verification="verification"></left-screening>
    </div>
    <div class="cont">
      <div class="top">
        <div class="_left">
          <div v-for="item in typeOfPaper" :key="item.value" @click="dzbBtn(item.value)">
            <el-button type="text" :style="{color: item.value === form.IsDZB ? '#7BB999' : '#595959'}">{{ item.label }}</el-button>
            <p v-show="item.value === form.IsDZB"></p>
          </div>
        </div>
        <div class="_right">
          <el-button type="primary" class="add-homework-btn" @click="goSetExercises">
            新增作业
          </el-button>
          <span class="homework-count">共<span style="color: #7fb99a;">{{total}}</span>个作业</span>
          <div class="search-container">
            <el-input
              v-model="form.PaperTitle"
              placeholder="请输入作业名称"
              class="search-input"
              @keyup.enter="handleSearch">
              <i slot="suffix" class="el-icon-search search-icon" @click="getPaperList"></i>
            </el-input>
          </div>
          <span class="more-filter-text" @click="showMore = !showMore">
            更多筛选 <i class="el-icon-arrow-down"></i>
          </span>
          <!-- <div class="btn" @click="toTimePublishList">修改推送时间</div> -->
          <!-- <el-dropdown>
            <div class="btn">更多 <i class="el-icon-caret-bottom"></i></div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="IsTY == '1' ? bigSel(1) : ''" @click="IsTY == '1' ? bigSel(1) : ''"
              >练习列表</el-dropdown-item
              >
              <el-dropdown-item
                v-if="SelectAreaId == '1' && classData.length > 0"
                @click.native="Review_progress"
              >批阅进度</el-dropdown-item>
              <el-dropdown-item @click.native="Questions">调查问卷</el-dropdown-item>
              <el-dropdown-item @click.native="helpSeekingProgress">求助进度</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
      </div>
      <div v-show="showMore" class="searchList">
        <div>练习类型：
          <el-button
            type="text"
            v-for="item in paperTypeList"
            :class="{active: item.value == form.PaperType}"
            :key="item.value"
            @click="typesOf(item.value)">
            {{ item.label }}
          </el-button>
        </div>
        <div>班级选择：
          <el-button
            type="text"
            :class="{active: form.ClassId == ''}"
            @click="classClick('')">全部</el-button>
          <el-button
            type="text"
            v-for="item in classData"
            :class="{active: item.ClassId == form.ClassId}"
            :key="item.ClassId"
            @click="classClick(item.ClassId)">
            {{ item.ClassName }}
          </el-button>
        </div>
        <div>布置状态：
          <el-button type="text"
          v-for="item in publishStatus"
          :class="{active: item.value == form.ReleaseStates}"
          :key="item.value"
          @click="publishBtn(item.value)">
          {{ item.label }}
          </el-button>
        </div>
      </div>
      <div class="ctnInput">
        <div>
          <div class="sort-view">
            <div class="sortDiv" v-for="item in selectList">
              <div class="txt" :style="{color: [item.first,item.last].includes(form.SortById) ? '#7fb99a' : '#999'}" @click="sortBtn(item.first,item.last)">{{item.label}}</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: form.SortById == item.last ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: form.SortById == item.first ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <new-list
          v-for="item in paperList"
          :key="item.PaId"
          :paperItem="item"
          @publishPaper="AssignmentLayout"
          @updataList="getPaperList">
        </new-list>
      </div>
      <div class="page">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page.sync="form.PageNo"
          layout="total, prev, pager, next"
          :total="total">
        </el-pagination>
      </div>
    </div>
<!--  批阅进度弹窗  -->
    <el-dialog title="当前批阅进度" class="Dialog_Second" :visible.sync="Review_Visible" width="70%">
      <div class="Class_btn">
        <p
          v-for="item in classData"
          :key="item.ClassId"
          :style="{ color: form.ClassId == item.ClassId ? '#68bb97' : '' }"
          @click="checkReviewClassBtn(item.ClassId)"
        >
          {{ item.ClassName }}
        </p>
      </div>
      <el-table
        :data="Progress_List"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
        style="width: 100%"
      >
        <el-table-column prop="Unit" label="单元"> </el-table-column>
        <el-table-column prop="Section" label="小节"> </el-table-column>
        <el-table-column prop="PaperTitle" label="练习名称">
          <template slot-scope="scope">
            {{ scope.row.PaperTitle }}
            <el-tag v-if="scope.row.IsSubjective" size="small">主观题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ReviewItemCount" label="含有批阅题目数"> </el-table-column>
        <el-table-column label="待批阅题目次数">
          <template slot-scope="scope">
            {{ scope.row.SubmitCount - scope.row.ReviewCount }}道 / {{ scope.row.SubmitCount }}道
          </template>
        </el-table-column>
        <el-table-column label="待批阅学生数">
          <template slot-scope="scope"> {{ scope.row.WaitReviewStudent }}人 / {{ scope.row.TotalCount }}人 </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="Viwe_Progress(scope.row)">批阅</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--  预览  -->
    <preview-paper ref="previewPaper"></preview-paper>
    <!--  布置作业弹窗  -->
    <el-dialog
      title="作业布置"
      :visible.sync="AssignmentLayoutShow"
      width="30%"
      :before-close="handleClose">
      <publish-set ref="PublishSet" :paperId="PaperId" :params="publishParams"></publish-set>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <!--  市管理端作业发布  -->
    <dialog-com ref="dialogCom" title="作业发布" :time-show="true" :close-on-click-modal="true" @authorization="getPaperList"></dialog-com>
  </div>
</template>

<script>
import newList from '@/views/Teacher/My_Task/components/newHomepage/newList'
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper'
import addAJobPopup from '@/views/threeAssistants/components/JobDesign/AddAJobPopup'
import PublishSet from '@/views/threeAssistants/JobDesign/PublishSet.vue'
import dialogCom from '@/views/threeAssistants/components/authorization/dialogCom'
import { Session } from '@/utils/storage'

export default {
  name: 'newHomepage',
  components: {
    leftScreening,
    newList,
    previewPaper,
    addAJobPopup,
    dialogCom,
    PublishSet,
  },
  data() {
    return {
      showMore: false, // 更多筛选
      openInDialog: false,
      Review_Visible: false,
      AssignmentLayoutShow: false,
      leftWidth: 16, // 动态控制布局
      classData: [], // 班级数据
      paperList: [],
      PaperId: '',
      typeOfPaper: this.$store.state.LoginSource == 1 ? [
        { value: 1, label: '纸笔作业' },
        { value: 0, label: '电子作业' },
      ] : [{ value: 0, label: '电子作业' }],
      publishStatus: [ // 发布状态
        { value: 0, label: '全部' },
        { value: 1, label: '已布置' },
        { value: 2, label: '未布置' },
        { value: 3, label: '作废' },
      ],
      paperTypeList:[
        { value: 0, label: '全部' },
        { value: 1, label: '市级作业' },
        { value: 2, label: '区级作业' },
        { value: 3, label: '校本作业' },
        { value: 4, label: '我的作业' },
      ],
      publishParams: null,
      form: {
        ChapterIds: [],
        PaperType: 0, // 试卷类型 0全部：1市：2区：3校：4班
        paperTag: 0, // 练习册类型
        ReviewStatus: 0, // 0全部，1-批阅中 2-未批阅 3-已批阅 4-未提交
        ReleaseStates: 0, // 0全部，1-已发布 2-未发布
        PracticeStates: 0, // 0全部，1作答中，2已截至，3-待批阅，4-已完成
        SortById: 0, // 排序
        ClassId: '', // 班级Id
        UserId: localStorage.getItem('UserId'), // 用户Id
        PageNo: 1, // 页码
        PageIndex: 10, // 条数
        IsDZB: this.$store.state.LoginSource == 1 ? 1 : 0, // 是否点阵笔
        PaperTitle: '', // 试卷标题
        Platform: 1,// 1专科专练，2上海微校
        Term: 1, // 1上学期，2下学期
        Year: ''
      },
      total: 0,
      Progress_List: [],
      IsTY: 1,
      SelectAreaId: '',
      // 记录左侧筛值
      // loggingValues: {},
      grade: 1
    }
  },
  computed: {
    isVip() {
      return Number(this.$store.state.user.userInfo.IsVip) == 1
    },
    selectList() {
      return this.form.IsDZB == 1 ? [
        { first: 10, last: 0, label: '更新时间' },
        { first: 14, last: 4, label: '正确率' },
        { first: 11, last: 1, label: '已参与学生数' },
      ] : [
        { first: 10, last: 0, label: '更新时间' },
        { first: 11, last: 1, label: '已参与学生数' },
        { first: 12, last: 2, label: '已批改学生数' },
        { first: 13, last: 3, label: '已订正学生数' },
      ]
    }
  },
  created() {
    this.IsTY = localStorage.getItem('IsTY')
    this.SelectAreaId = localStorage.getItem('SelectAreaId')
  },
  // mounted() {
  //   this.getLeftScreening()
  // },
  activated() {
    const { Ids } = this.$store.state.chapterStorage.ChapterObj
    this.form.ChapterIds = Ids
    // this.form.ChapterIds = this.$refs.leftScreening.defaultCheckedKeys
    // this.getPaperList()
    this.getLeftScreening()
  },
  methods: {
    // 预览
    previewPaper(id) {
      this.$refs.previewPaper.init(id)
    },
    getLeftScreening(opt) {
      this.reset()
      // this.loggingValues = opt
      // const { yearActive, semesterActive, gradeActive, chapterList } = opt
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      this.form.Year = Year
      this.form.Term = Term
      this.form.ChapterIds = Ids
      this.form.PageNo = 1
      this.form.ClassId = ''
      this.grade = GradeId
      // this.getPaperList()
      // this.GetTeachClass(yearActive, gradeActive)
      this.GetTeachClass()
    },
    verification(val) {
      /* 根据是否点阵笔处理历年数据付费 */
      if(this.form.IsDZB !== 1){
        if(this.isVip){
          this.$store.commit('user/SET_YEAR_NUMS',val*1)
        } else {
          if(val == Session.get('yearDefault')) return
          this.$alert('查看历年数据为付费功能，如若开启请联系：周先生 021-54486816', '温馨提示', {
            confirmButtonText: '确定',
            callback: action => {
              this.form.IsDZB = 1
              this.$refs.leftScreening.getYear()
            }
          });
        }
      }
    },
    // 练习类型事件
    typesOf(val) {
      this.form.PageNo = 1
      this.form.PaperType = val
      if (val == 2) { this.publishParams = ['ClassId', 'IsImmediatelyPublish', 'ShareSet'] }
      if (val == 3) { this.publishParams = ['ClassId', 'IsImmediatelyPublish'] }
      this.getPaperList()
    },
    // 班级点击事件
    classClick(val){
      this.form.PageNo = 1
      this.form.ClassId = val
      this.getPaperList()
    },
    // 发布状态
    publishBtn(state){
      this.form.PageNo = 1
      this.form.ReleaseStates = state
      this.getPaperList()
    },
    // 排序
    sortBtn(e, r) {
      this.form.PageNo = 1
      if(this.form.SortById == e){
        this.form.SortById = r
      } else {
        this.form.SortById = e
      }
      this.getPaperList()
    },
    // 纸笔作业/电子作业切换
    dzbBtn(val){
      if(val == 0){
        /* 根据是否点阵笔处理历年数据付费 */
          if(this.isVip){
            this.$store.commit('user/SET_YEAR_NUMS',this.form.Year*1)
            this.nextQingQiu(val)
          } else {
            if(this.form.Year == Session.get('yearDefault')) {
              this.nextQingQiu(val)
            } else {
              this.$alert('查看历年数据为付费功能，如若开启请联系：周先生 021-54486816', '温馨提示', {
                confirmButtonText: '确定',
                callback: action => {
                  this.form.PageNo = 1
                  this.$refs.leftScreening.getYear()
                }
              });
            }
          }
      } else {
        this.nextQingQiu(val)
      }
      // this.form.PageNo = 1
      // this.form.IsDZB = val
      // this.getPaperList()
    },
    // 往下执行
    nextQingQiu(val){
      this.form.PageNo = 1
      this.form.IsDZB = val
      this.getPaperList()
    } ,
    // 分页
    handleCurrentChange(val) {
      this.form.PageNo = val
      this.getPaperList()
    },
    // 新增作业
    goSetExercises() {
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj

      const passingParameters = {
        PaperId: '',
        Year: Year,
        Semester: Term,
        GradeId: GradeId,
        unit: Ids.length === 1 ? ParentId[0] : '',
        ChapterId: Ids.length === 1 ? Ids[0] : '',
        TextbookId: '',
        title: '',
      }
      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: passingParameters,
      });
    },
    // 大状态切换
    bigSel(id) {
      this.bigCol = 2
      this.$router.push({ path: '/Teacher/My_Task/ExaminationPaperList/ExaminationPaperList' })
    },
    // 调查问卷
    Questions() {
      this.$router.push('/Questionnaires/questionList')
    },
    // 求助进度
    helpSeekingProgress(){
      const { Year, Term } = this.$store.state.chapterStorage.ChapterObj
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/helpSeekingProgress',
        query:{
          grade: Term,
          year: Year
        }
      })
    },
    // 跳转时间发布列表
    toTimePublishList() {
      this.$router.push({
        path: '/Teacher/My_Task/TimePublishList',
        query: {
          classId: this.form.ClassId,
          // status: this.racticeblue,
          status: 1, // 发布状态
        },
      })
    },
    // 批阅
    Viwe_Progress(item) {
      if (item.IsSubjective) {
        this.$router.push({
          path: '/Teacher/My_Task/subjectiveReview',
          query: {
            paperId: item.PaperId,
            classId: item.ClassId,
            // chapterId: item.ChapterId,
            chapterName: item.Unit,
            chapterNum: item.Section,
          },
        })
        this.Review_Visible = false
      } else {
        const GoPge = this.$router.resolve({
          path: '/Teacher/My_Task/TeacherWorkshops',
          query: {
            classId: this.form.ClassId,
            paperId: item.PaperId,
            PaperTitle: item.PaperTitle,
            Show_BackBtn: 1,
          },
        })
        window.open(GoPge.href, '_blank')
      }
    },
    // 批阅进度更改
    checkReviewClassBtn(val) {
      this.checkReviewClass = val
      this.Review_progress()
    },
    // 批阅进度接口
    async Review_progress() {
      this.Review_Visible = true
      const params = {
        ClassId: this.form.ClassId,
        UserId: localStorage.getItem('UserId'),
      }
      const res = await this.$uwonhttp.post('/Paper/TeacherPaper/GetTeacherPaperReviewStatisticsList', params)
      if (res.data.Success) {
        this.Progress_List = res.data.Data
      }
    },
    // 获取教师班级
    async GetTeachClass(year,grade) {
      const { Year, GradeId } = this.$store.state.chapterStorage.ChapterObj
      const { data:res } = await this.$uwonhttp.get(`/Class/TeacherClassManager/GetTeachClass?UserId=${localStorage.getItem('UserId')}&grade=${GradeId}&year=${Year}`)
      // console.log(res)
      if(res.Success) {
        this.classData = res.Data
        this.form.ClassId = ''
        this.getPaperList()
      } else {
        this.$message.error(res.Msg)
      }
    },
    async getPaperList() {
      const promi = {
        ...this.form,
        GradeId: this.grade,
        UserId: localStorage.getItem('UserId'), // 用户Id
      }
      const {data:res} = await this.$uwonhttp.post('/Paper/TeacherPaper/GetTeacherPaperListPracticeByPaper', promi)
      if(res.Success){
        this.total = res.Data.TotalItems
        this.paperList = res.Data.Items
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 数据重置
    reset(){
      this.total = 0
      this.form.PageNo = 1
      this.paperList = []
    },
    AssignmentLayout(id) {
      if(this.$store.state.Level == 5) {
        this.$refs.dialogCom.setPaperId(id)
        this.$refs.dialogCom.init()
      } else {
        this.PaperId = id
        this.AssignmentLayoutShow = true
        const timer = setTimeout(() => {
          this.$refs.PublishSet.GetTeachClass(id)
          clearTimeout(timer)
        },200)
      }
    },
    handleClose() {
      this.AssignmentLayoutShow = false
    },
    async handleSubmit() {
      const obj = {
          PaperId: this.PaperId,
          PublishTime: '',
          ShareAreaCity: 1,
      }
      // 获取已发布班级
      const date = this.$refs.PublishSet.getClassId()
      // 选中班级
      const result = this.$refs.PublishSet.handleQueryData()
      // 去除已发布班级Id
      const newArr = result.ClassId.filter(item=>!date.includes(item))
      if(newArr.length > 0) {
        result.ClassId = newArr.join(',')
        delete result.IsImmediatelyPublish
        const inputParameters = Object.assign(obj, result)
        const { data } = await this.$uwonhttp.post('/Question/Question/PaperPublishToClass', inputParameters)
        if(data.Success) {
          this.handleClose()
          this.$message.success('作业布置成功')
          this.getPaperList()
        } else {
          this.$message.error(data.Msg)
        }
      } else {
        this.handleClose()
      }
    },
  }
}
</script>

<style lang="less" scoped>
.homePage{
  display: flex;
  height: 100%;
  .cont{
    width: 100%;
  }
  .top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px 12px 20px;
    background: #FFFFFF;
    border-bottom: 1px dashed #ABABAB;
    ._left{
      display: flex;
      >div{
        margin-right: 30px;
      }
      .el-button{
        font-size: 24px;
        padding: 12px 0 8px 0;
      }
      p{
        width: 27px;
        height: 4px;
        background: #7BB999;
        margin: 0 auto;
        border-radius: 4px;
      }
    }
    ._right {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .add-homework-btn {
        background: #7fb99a;
        border-color: #7fb99a;
        border-radius: 5px;
        padding: 8px 20px;
        font-size: 16px;
        
        &:hover {
          background: #6ba085;
          border-color: #6ba085;
        }
      }
      
      .homework-count {
        color: #666;
        font-size: 16px;
        white-space: nowrap;
      }
      
      .search-container {
        position: relative;
        .search-input {
          width: 280px;
          display: flex;
          align-items: center;
          font-size: 16px;
          ::v-deep .el-input__inner {
            border-radius: 5px;
            border: 1px solid #ddd;
            padding-right: 40px;
            &:focus {
              border-color: #7fb99a;
            }
          }
          
          .search-icon {
            cursor: pointer;
            color: #999;
            
            &:hover {
              color: #7fb99a;
            }
          }
          ::v-deep .el-input__suffix{
            height: auto !important;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
        .more-filter-text {
          color: #666;
          font-size: 16px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 4px;
          
          &:hover {
            color: #7fb99a;
          }
          
          .el-icon-arrow-down {
            font-size: 12px;
          }
        }
    }
  }
  .searchList{
    background: #FFFFFF;
    padding: 8px 20px 8px 20px;
    //margin: 0 20px;
    border-bottom: 1px solid #D9D9D9;
    div,.el-button{
      font-size: 18px;
      color: #6E6E6E;
    }
    .el-button{
      padding: 10px 0;
    }
  }
  .ctnInput{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #FFFFFF;
    justify-content: flex-end;
    .el-input{
      width: 30%;
    }
    .sort-view{
      display: flex;
      justify-content: flex-end;
      .sortDiv{
        display: flex;
        align-items: center;
        margin: 0 4px;
        .txt{
          font-size: var(--font16-20);
        }
        .icon{
          display: flex;
          flex-direction: column;
          margin-left: 2px;
          i{
            font-size: 16px;
          }
          i:nth-child(1){
            position: relative;
            bottom: -3px;
          }
          i:nth-child(2){
            position: relative;
            top: -3px;
          }
        }
      }
    }
  }
  .content{
    background: #FFFFFF;
    padding: 0 20px 8px;
    height: 80%;
    overflow-y: scroll;
  }
  .content::-webkit-scrollbar{
    display: none;
  }
  .page{
    display: flex;
    justify-content: flex-end;
    padding: 2px 10px 4px;
    background-color: #FFFFFF;
  }
}
.active{
  color: #7fb99a !important;
}
</style>
