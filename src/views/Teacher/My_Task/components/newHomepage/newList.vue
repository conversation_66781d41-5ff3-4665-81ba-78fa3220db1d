<template>
  <div class="new-list-item">
    <!-- 头部信息 -->
    <div class="list-header">
      <div class="paper-header">
        <div class="header-left">
          <h3 class="paper-title">
            {{ paperItem.PaperTitle }}
          </h3>
          <div class="paper-tags">
            <el-tag v-show="paperItem.PaperType == -2" type="warning">市级作业</el-tag>
            <el-tag v-show="paperItem.PaperType == 0 || (paperItem.PaperType == '-3' && paperItem.PaperTag == -32)" color="#51A8A01A" style="color: #51A8A0;">区级作业</el-tag>
            <el-tag v-show="paperItem.PaperType == 18">校本作业</el-tag>
            <el-tag v-show="paperItem.PaperType == -3 && paperItem.PaperTag == -31">教材练习册</el-tag>
            <el-tag v-show="paperItem.PaperType == 1" type="success">我的作业</el-tag>
          </div>
        </div>
        <el-tag
          class="paper-status"
          :type="paperItem.IsStop === 1 ? 'info' : paperItem.ReleaseStates === 1 ? 'success' : 'warning'"
          size="medium">
          {{ getStatusText(paperItem.ReleaseStates) }}
        </el-tag>
      </div>
      <div class="paper-info">
        <div class="paper-info-left">
          <span>所属章节：{{ paperItem.ChapterName }}</span>
          <span>出卷人：{{ paperItem.TrueName }}</span>
          <span>更新时间：{{ paperItem.CreatedTime }}</span>
        </div>
        <div class="paper-info-right">
          <el-button
            type="primary"
            size="small"
            class="share-btn"
            @click="handleShare">
            同校共享
          </el-button>
        </div>
      </div>
    </div>

    <!-- 班级列表 -->
    <div v-if="paperItem.ReleaseStates === 1&&paperItem.IsStop !== 1" class="class-list">
      <div
        v-for="classInfo in paperItem.DoPaperClassInfo" 
        :key="classInfo.ClassId"
        class="class-item">
        <div class="class-name">{{ classInfo.ClassName }}</div>
        <div class="class-stats">
          <div class="stat-item">
            <div class="stat-label-value">
              <span class="stat-label">正确率</span>
              <span class="stat-value">{{ classInfo.DoAnswerRightRate || '0%' }}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill correct" :style="{width: classInfo.DoAnswerRightRate || '0%'}"></div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-label-value">
              <span class="stat-label">参与率</span>
              <span class="stat-value">{{ classInfo.FinishRate || '0/0' }}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill participate" :style="{width: getParticipateRate(classInfo.FinishRate)}"></div>
            </div>
          </div>
          <div v-if="paperItem.IsDZB !== 1" class="stat-item">
            <div class="stat-label-value">
              <span class="stat-label">批改率</span>
              <span class="stat-value">{{ classInfo.ReviseRate || '0/0' }}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill revise" :style="{width: getReviseRate(classInfo.ReviseRate)}"></div>
            </div>
          </div>
          <div v-if="paperItem.IsDZB !== 1" class="stat-item">
            <div class="stat-label-value">
              <span class="stat-label">订正率</span>
              <span class="stat-value">{{ classInfo.CorrectRate || '0/0' }}</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill correction" :style="{width: getCorrectionRate(classInfo.CorrectRate)}"></div>
            </div>
          </div>
        </div>
        <div class="action-buttons">
          <el-button v-if="paperItem.IsDZB !== 1" size="mini" icon="el-icon-view" @click="handleBatchCorrect(classInfo)">批改</el-button>
          <el-button v-if="paperItem.IsDZB !== 1" size="mini" icon="el-icon-check" @click="handleCorrection(classInfo)">订正</el-button>
          <el-button size="mini" icon="el-icon-chat-line-square" @click="handleComment(classInfo)">讲评</el-button>
          <el-button v-if="paperItem.IsDZB !== 1" size="mini" icon="el-icon-data-analysis" @click="handleAnalysis(classInfo)">分析</el-button>
          <el-popover placement="bottom" trigger="click" width="120">
            <div class="more-actions">
              <el-button 
                type="text" 
                size="mini" 
                @click="handleRevoke(classInfo)">
                撤销发布
              </el-button>
              <el-button 
                type="text" 
                size="mini" 
                @click="handleInvalidate(classInfo)">
                作废
              </el-button>
            </div>
            <el-button size="mini" icon="el-icon-more" slot="reference">更多</el-button>
          </el-popover>
        </div>
      </div>
    </div>
    <!-- 未发布 -->
    <div v-else-if="paperItem.ReleaseStates === 2" class="no-class-list">
      <el-button type="primary" size="small" class="share-btn" @click="handleEdit">
        编辑
      </el-button>
      <el-button type="primary" size="small" class="share-btn" @click="handlePublish">
        布置
      </el-button>
      <el-popover placement="bottom" trigger="click" width="120">
        <div class="more-actions">
          <el-button 
            type="text" 
            size="mini" 
            @click="handleCopy">
            复制
          </el-button>
          <el-button 
            type="text" 
            size="mini" 
            @click="handleDelete">
            删除
          </el-button>
        </div>
        <el-button class="more-btn" size="mini" icon="el-icon-more" slot="reference"></el-button>
      </el-popover>
    </div>
    <!-- 已作废 -->
    <div v-else class="no-class-list">
      <el-button type="primary" size="small" class="share-btn" @click="handleCopy">
        复制
      </el-button>
      <el-button type="primary" size="small" class="share-btn" @click="handleDelete">
        删除
      </el-button>
    </div>
    <!--  同校共享  -->
    <collegialSharing ref="collegialSharing" @getSuccess="$emit('updataList')"></collegialSharing>
  </div>
</template>

<script>
import { Session } from '@/utils/storage'
import collegialSharing from '@/views/threeAssistants/JobDesign/components/collegialSharing.vue'
export default {
  name: 'newList',
  props: {
    paperItem: {
      type: Object,
      required: true
    }
  },
  components: {
    collegialSharing
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        1: '已布置',
        2: '未布置', 
        3: '作废'
      }
      return statusMap[status] || '未知'
    },
    getParticipateRate(finishRate) {
      if (!finishRate) return '0%'
      const [finished, total] = finishRate.split('/')
      return total > 0 ? `${(finished / total * 100).toFixed(1)}%` : '0%'
    },
    getReviseRate(reviseRate) {
      if (!reviseRate) return '0%'
      const [revised, total] = reviseRate.split('/')
      return total > 0 ? `${(revised / total * 100).toFixed(1)}%` : '0%'
    },
    getCorrectionRate(correctRate) {
      if (!correctRate) return '0%'
      const [corrected, total] = correctRate.split('/')
      return total > 0 ? `${(corrected / total * 100).toFixed(1)}%` : '0%'
    },
    // 同校共享
    handleShare() {
      this.paperItem.Id = this.paperItem.PaId
      this.$refs.collegialSharing.init(this.paperItem)
    },
    // 批改
    handleBatchCorrect(classInfo) {
      this.$emit('batchCorrect', { paperItem: this.paperItem, classInfo })
    },
    // 订正
    handleCorrection(classInfo) {
      this.$emit('correction', { paperItem: this.paperItem, classInfo })
    },
    // 讲评
    handleComment(item) {
      if (this.$store.state.limitingJobPermissions == 0) {
        this.popupPrompt()
        return
      }
      const subjectId = localStorage.getItem('UW_SUBJECT') || '2'
      const { PaId, IsDZB, ChapterId, PaperTitle } = this.paperItem
      const { HomeworkType, ClassId, ClassName, PaperTag } = item
      const { Year, GradeId } = this.$store.state.chapterStorage.ChapterObj
      Session.remove('isRefresh')
      Session.remove('questionsInfo')
      Session.remove('sessionInfo')

      const query = {
          PaperTag, // 作文
          PaperTitle,
          ClassName,
          Grade: GradeId,
          firstChapter: ChapterId,
          SourceId: PaId,
          HomeworkType: IsDZB ? (HomeworkType || 1) : 21,
          ClassId,
          State: 0, // 0 未授课；1已授课；2 授课中
          year: Year,
          isKatex: [3,23,37,100].includes(subjectId*1), // 学科ID 如果是true代表当前是英语，不走公式识别
          isCurrentYear: this.$store.state.user.yearDefault == Year ? 1 : 0, // 选中的是否当前学年 1是0否
        }
      const GoPge = this.$router.resolve({
        path: '/teaching-modules',
        query,
      })
      window.open(GoPge.href, '_blank')
    },
    // 分析
    handleAnalysis(classInfo) {
      this.$emit('analysis', { paperItem: this.paperItem, classInfo })
    },
    // 编辑
    handleEdit() {
      const { Year, Term, GradeId } = this.$store.state.chapterStorage.ChapterObj
      const { PaId, PaperTitle, ChapterId } = this.paperItem
      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: {
          paperId: PaId,
          Year: Year,
          Semester: Term,
          GradeId: GradeId,
          title: PaperTitle,
          ChapterId
        },
      });
    },
    // 布置
    handlePublish() {
      this.$emit('publishPaper', this.paperItem.PaId)
    },
    // 复制
    handleCopy() {
      const { Year, Term, GradeId } = this.$store.state.chapterStorage.ChapterObj
      const { PaId, PaperTitle, ChapterId } = this.paperItem
      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: {
          paperId: PaId,
          Year: Year,
          Semester: Term,
          GradeId: GradeId,
          title: PaperTitle,
          ChapterId,
          copy: 1
        },
      });
    },
    // 撤销发布
    handleRevoke() {
      this.$confirm('确定撤销发布该份作业吗，撤销后可对作业重新编辑并进行发布', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.PaperDesignRevocationPublish()
      }).catch(() => {
        return false
      });
    },
    // 作废
    handleInvalidate() {
      this.$confirm('确定作废该份作业吗，作废后作业不可编辑与布置！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.PaperStopPublish()
      }).catch(() => {
        return false
      });
    },
    handleDelete() {
      this.$confirm('确定删除该份作业吗，删除后不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.PaperDelete()
      }).catch(() => {
        return false
      });
    },
    // 撤销发布接口
    async PaperDesignRevocationPublish() {
      const resPone = await this.$uwonhttp.post('/Question/Question/PaperDesignRevocationPublish', {
        paperId: this.paperItem.PaId,
        userId: localStorage.getItem('UserId')
      })
      if (resPone.data.Success) {
        this.$message.success('试卷已撤销！')
        this.$emit('updataList')
      } else {
        this.$message.error(resPone.data.Msg)
      }
    },
    // 作废接口
    async PaperStopPublish() {
      const { data } = await this.$uwonhttp.post('/Question/Question/PaperStopPublish', { paperId: this.paperItem.PaId, userId: localStorage.getItem('UserId') })
      if (data.Success) {
        this.$message.success('该试卷已被作废！')
        this.$emit('updataList')
      } else {
        this.$message.error(data.Msg)
      }
    },
    // 删除接口
    async PaperDelete() {
      const { data } = await this.$uwonhttp.post('/Question/Question/PaperDesignDel', { paperId: this.paperItem.PaId, userId: localStorage.getItem('UserId') })
      if (data.Success) {
        this.$message.success('删除成功！')
        this.$emit('updataList')
      } else {
        this.$message.error(data.Msg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.new-list-item {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;

  .list-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    .paper-header{
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }
    .header-left {
      display: flex;
      flex: 1;
      .paper-title {
        font-size: 22px;
        font-weight: 600;
        color: #333;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          color: #7fb99a;
        }
      }
      .paper-tags {
        margin-left: 8px;
        margin-bottom: 8px;
        .el-tag {
          margin-right: 8px;
        }
      }
      .paper-status{
        font-size: 16px;
      }
    }
    .paper-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      color: #666;
      .paper-info-left {
        display: flex;
        gap: 24px;
      }
      .paper-info-right {
        .share-btn {
          background: #7fb99a;
          border-color: #7fb99a;
          border-radius: 4px;
          padding: 10px 16px;
          font-size: 18px;
          color: #fff;
          &:hover {
            background: #6ba085;
            border-color: #6ba085;
          }
        }
      }
    }
  }

  .class-list {
    .class-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f8f8f8;

      &:last-child {
        border-bottom: none;
      }

      .class-name {
        width: 100px;
        font-weight: 500;
        color: #333;
        flex-shrink: 0;
        font-size: 18px;
      }

      .class-stats {
        flex: 1;
        display: flex;
        gap: 40px;
        margin: 0 20px;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          min-width: 120px;
          .stat-label-value {
            display: flex;
            font-size: 16px;
            color: #999;
            margin-bottom: 4px;
            align-items: center;
            gap: 4px;
            justify-content: center;
          }

          .stat-value {
            font-size: 18px;
            font-weight: 500;
            color: #7fb99a;
            margin-bottom: 6px;
          }

          .progress-bar {
            width: 120px;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              border-radius: 3px;
              transition: width 0.3s ease;

              &.correct {
                background: #7bb999;
              }

              &.participate {
                background: #5fa9f7;
              }

              &.revise {
                background: #f6a649;
              }

              &.correction {
                background: #e74c3c;
              }
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        .el-button {
          padding: 4px 8px;
          font-size: 18px;
          border-radius: 4px;

          &:hover {
            background: #7fb99a;
            border-color: #7fb99a;
            color: white;
          }
        }

        .status-tag {
          margin-left: 8px;
        }
      }
    }
  }
  .no-class-list{
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 16px;
    .share-btn {
      margin-left: 15px;
      background: #7fb99a;
      border-color: #7fb99a;
      border-radius: 4px;
      padding: 10px 16px;
      font-size: 18px;
      color: #fff;
      &:hover {
        background: #6ba085;
        border-color: #6ba085;
      }
    }
    .more-btn{
      margin-left: 15px;
      // background: #7fb99a;
      // border-color: #7fb99a;
      border-radius: 4px;
      padding: 10px 16px;
      font-size: 18px;
      // color: #fff;
    }
  }
}
</style>
<style lang="less">
.more-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  .el-button {
    margin-left: 0 !important;
    text-align: center;
    padding: 4px 0;
    font-size: 16px;
    &:hover {
      color: #7fb99a;
    }
  }
}
</style>
