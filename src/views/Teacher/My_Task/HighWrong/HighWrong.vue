<template>
  <div class="highWrong"
       v-loading="loading"
       element-loading-text="文件正在导出请稍等..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="margin_r_5 back_white" :style="{ width: leftWidth + '%' }">
      <left-screening :pageId="$route.path" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 18 ? 2.3 : 18" @getAllList="getLeftScreening"></left-screening>
    </div>
    <div class="right_Conten" :style="{width: leftWidth == 18 ? '84%' : '100%'}">
      <div>
        <el-button
          v-for="item in btnList"
          :key="item.id"
          :type="active == item.id ? 'primary' : ''"
          @click="btnActive(item.id)">
          {{ item.label }}
        </el-button>
      </div>
      <class-list-form
        ref="classListForm"
        :tabValue="active"
        @getCuoTiList="getCuoTiList"
        @GetItemWord="GetManyStudentWrongItemWord"
        @GetHighFrequencyList="GetHighFrequencyList"></class-list-form>
      <div class="stu_from_list">
        <student-list v-show="active == 'student'" ref="studentList" @gitList="allGetList"></student-list>
        <div style="width: 100%">
          <wrong-question-from ref="wrongQuestionFrom" @setListHeight="setListHeight" @allSetList="allGetList"></wrong-question-from>
          <div class="total_errorBook">
            <div class="_errorBook">
              <el-checkbox v-model="selectAll" @change="setSelectAll">全选</el-checkbox>
              <!--       生成错题作业暂时不做功能暂待，后续题库需求合并后再做开发       -->
              <el-button v-if="active === 'teacher'" type="primary" icon="el-icon-document-delete" @click="upAddAJobPopup">生成错题作业</el-button>
              <!--       生成错题作业暂时不做功能暂待，后续题库需求合并后再做开发       -->
              <el-button v-else type="primary" icon="el-icon-document-delete" @click="getStudentListFil">学生个性错题本</el-button>
<!--              <el-button v-show="active === 'student'" type="primary" icon="el-icon-document-delete" @click="getStudentListFil">学生个性错题本</el-button>-->
            </div>
            <div class="_total">
              <div class="lef">
                共<span>{{errorItemList.length}}</span>道错题
              </div>
              <el-button v-show="active == 'student'" type="text" :icon="IsTypical == 1 ? 'el-icon-star-on' : 'el-icon-star-off'" @click="setIsTypical">只查看典型错题</el-button>
              <div class="sort-view" v-show="active == 'teacher'">
                <div class="sortDiv">
                  <div class="txt" @click="sortBtn(0,1)">平均正确率</div>
                  <div class="icon">
                    <i class="el-icon-caret-top" :style="{color: Sort == 0 ? '#7fb99a' : '#999'}"></i>
                    <i class="el-icon-caret-bottom" :style="{color: Sort == 1 ? '#7fb99a' : '#999'}"></i>
                  </div>
                </div>
                <div class="sortDiv">
                  <div class="txt" @click="sortBtn(2,3)">错误人数</div>
                  <div class="icon">
                    <i class="el-icon-caret-top" :style="{color: Sort == 2 ? '#7fb99a' : '#999'}"></i>
                    <i class="el-icon-caret-bottom" :style="{color: Sort == 3 ? '#7fb99a' : '#999'}"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="allList" :style="{height: setHeight}">
            <div v-if="errorItemList.length">
              <item-arr-list
                v-for="(item,index) in errorItemList"
                :key="item.ItemId"
                :errorItem="item"
                :ind="index"
                :active="active"
                @addTutoring="addTutoring"
                @errorItemRollUpAndUnfold="errorItemRollUpAndUnfold">
              </item-arr-list>
            </div>
            <el-empty v-else description="暂无结果"></el-empty>
          </div>
<!--          <div class="page">-->
<!--            <el-pagination-->
<!--              background-->
<!--              layout="prev, pager, next"-->
<!--              :total="total"-->
<!--              :page-size="10"-->
<!--              :current-page="currentPage"-->
<!--              @current-change="changPage">-->
<!--            </el-pagination>-->
<!--          </div>-->
        </div>
      </div>
    </div>
    <!--  生成错题作业  -->
    <add-a-job-popup ref="addAJobPopup" @setErrorPaperList="setErrorPaperList"></add-a-job-popup>
    <!--  前端单题切片使用canvas元素  -->
    <canvas v-show="false" id="canvas-video" ref="canvasVideo" style="width: 100%"></canvas>
  </div>
</template>

<script>
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import classListForm from '@/views/Teacher/My_Task/HighWrong/components/classListForm'
import wrongQuestionFrom from '@/views/Teacher/My_Task/HighWrong/components/wrongQuestionFrom'
import itemArrList from '@/views/Teacher/My_Task/HighWrong/components/itemArrList'
import studentList from '@/views/Teacher/My_Task/HighWrong/components/studentList'
import documentExport from '@/utils/documentExport'
import ExamFormatCommon from '@/common/ExamFormatCommon'
import AddAJobPopup from '@/views/threeAssistants/components/JobDesign/AddAJobPopup'
// 整卷中单题切片
import CanvasVideoSingle, {PLAY_SPEED_MAP} from '@/views/teaching-modules/utils/canvas-video-single'

export default {
  components:{
    leftScreening,
    classListForm,
    wrongQuestionFrom,
    itemArrList,
    studentList,
    AddAJobPopup
  },
  props:{
    paperId:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      // leftComponentVal: {}, // 左侧组件值
      leftWidth: 18,
      active: 'teacher',
      btnList:[
        { id: 'teacher', label: '教师视角' },
        { id: 'student', label: '学生视角' }
      ],
      // 错题列表数据
      errorItemList: [],
      // 分页
      // total: 0,
      // currentPage: 1,
      // 错题列表内容高
      // setHeight: '66vh',
      setHeight: '69.3vh',
      // 是否全选
      selectAll: false,
      // 文件导出加载
      loading: false,
      // 是否只查看典型错题
      IsTypical: 0,
      // 排序
      Sort: 0,
      // 单题切片实例
      canvasVnode: null, // canvas 实例
      canvasImg: '', // 学生实时base64图片
    }
  },
  activated() {
    this.getLeftScreening()
  },
  methods: {
    // 排序
    sortBtn(e, r) {
      if(this.Sort == e){
        this.Sort = r
      } else {
        this.Sort = e
      }
      this.GetHighFrequencyList()
    },
    // 学生视角查看典型错题
    setIsTypical(){
      this.IsTypical = this.IsTypical == 0 ? 1 : 0
      this.GetStudentHighFrequencyList()
    },
    // 左侧学年/学期/年级/章节组件初始方法
    getLeftScreening(initValue) {
      // this.leftComponentVal = initValue
      // console.log(this.leftComponentVal)
      this.$refs.classListForm.GetTeachClass()
      // 根据学年学期限定时间筛选
      this.$refs.wrongQuestionFrom.timeChangeDis()
      if(this.active === 'student'){
        this.GetStudentHighFrequencyList()
      } else {
        this.$refs.classListForm.GetClassItemSettingInfos('init')
        console.log('加载教师视角')
      }
    },
    // 教师维度/学生维度切换
    btnActive(val) {
      if (val === 'teacher') {
        // this.currentPage = 1
        console.log('教师视角逻辑')
        this.$refs.classListForm.GetClassItemSettingInfos('init')
      } else {
        const { activeClassId } = this.$refs.classListForm
        this.$refs.studentList.getStudentList(activeClassId)
      }
      this.active = val
      this.selectAll = false
      this.setListHeight()
    },
    // 设置列表高度
    setListHeight(){
      const val = this.$refs.wrongQuestionFrom.downUpValue
      // if (this.active === 'student') {
      //   this.setHeight = val === 'down' ? '59.8vh' : '65.8vh';
      // } else {
      //   this.setHeight = val === 'down' ? '60vh' : '66vh';
      // }
      this.setHeight = val === 'down' ? '63.3vh' : '69.3vh';
    },
    // 获取高频错题列表
    getCuoTiList() {
      // 需要区分教师/学生维度
      if (this.active === 'teacher') {
        console.log('高频错题教师请求接口')
        this.GetHighFrequencyList()
      } else {
        const { activeClassId } = this.$refs.classListForm
        this.$refs.studentList.getStudentList(activeClassId)
      }
      console.log('获取高频错题列表')
    },
    // 生成错题作业
    upAddAJobPopup(){
      console.log('错题作业')
      // const { yearActive, semesterActive, gradeActive, chapterList } = this.leftComponentVal
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      // vuex携带左侧章节数据
      const [ unit, ChapterId ] = Ids.length == 1 ? [ParentId[0], Ids[0]] : ['','']
      // const [ unit, ChapterId ] = chapterList.length == 1 ? [chapterList[0].ParentId, chapterList[0].ChapterId] : []
      const passingParameters = {
        Year: Year,
        Semester: Term,
        GradeId: GradeId,
        unit ,
        ChapterId,
        TextbookId: -1,
      }
      // 错题作业初始化弹窗
      this.$refs.addAJobPopup.init(passingParameters)
    },
    // 分页
    // changPage(val){
    //   this.currentPage = val
    //   if (this.active === 'teacher') {
    //     this.GetHighFrequencyList()
    //   } else {
    //     this.GetStudentHighFrequencyList()
    //   }
    // },
    // 是否全选事件
    setSelectAll(val){
      this.errorItemList.forEach(item => item.checked = val)
    },
    allGetList(){
      if(this.active === 'teacher'){
        this.GetHighFrequencyList()
      } else {
        this.GetStudentHighFrequencyList()
      }
    },
    // 错题辅导
    addTutoring(item) {
      const { ItemId, ChapterId, Grade, PaperId, IsDZB } = item
      const { Year } = this.$store.state.chapterStorage.ChapterObj
      this.$router.push({
        path: '/Teacher/My_Coach/AccurateCoach',
        query:{
          classId: this.$refs.classListForm.activeClassId,
          itemId: ItemId,
          chapterId: ChapterId,
          gradeId: Grade,
          PaperId: PaperId,
          year: Year,
          IsDZB,
        }
      })
      // eventBus.$emit('RouterFalse', this.RouterFalse)
    },
    // 教师维度生成作业接口
    async setErrorPaperList(form){
      const { ChapterId, PaperName, Year, GradeId, PaperType, Semester, TextbookId, AuthorName, IsSetScore, TotalScore } = form;
      const newArr = this.errorItemList.filter(item => item.checked).map(item => item.ItemId)
      const data = {
        Year,
        Semester,
        GradeId,
        ChapterId,
        PaperName,
        PaperType,
        TextbookId,
        AuthorName,
        IsSetScore,
        TotalScore,
        ItemIds: newArr, // 错题Id集合
        Source: localStorage.getItem('LoginSource'),
        SubjectId: localStorage.getItem('UW_SUBJECT'),
        UserId: localStorage.getItem('UserId'),
        UserRoleId: localStorage.getItem('role'),
      }
      const { data:res } = await this.$uwonhttp.post('/Paper/TeacherPaper/WrongItemCreatePaper', data)
      if(res.Success){
        const urlObj = {
          paperId: res.Data,
          id: 102,
          ChapterId,
          GradeId,
          Semester,
          Year,
          TextbookId
        }
        await this.$router.push({
          path: '/threeAssistants/JobDesign/NewJob',
          query: urlObj,
        });
      } else {
        this.$message.error(res.Msg)
      }
    },
    //高频率错题列表教师视角
    async GetHighFrequencyList() {
      this.selectAll = false
      // const { yearActive, semesterActive, gradeActive, chapterList } = this.leftComponentVal
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      const { activeStudentId } = this.$refs.studentList
      const ChapterId = Ids
      const PeopleCount = this.$refs.classListForm.numberOfPeople
      const [BegionAccuracy, EndAccuracy] = this.$refs.classListForm.sliderValue
      const { activeClassId } = this.$refs.classListForm
      const {
        Type, // 日期（1本学期，2近30天，3近7天）
        PaperName, // 试卷名称
        TypeId, // 题型Id
        Level, // 难度
        UseScene, // 0全部，1电子作业，2纸笔作业
        ResourceType, // 资源类型（0全部，1市，2区，3校，4班）
        IsCoach, // 是否辅导（0全部，1否，2是）
      } = this.$refs.wrongQuestionFrom.form
      let platform = localStorage.getItem('PL_newP')
      const data = {
        userId: localStorage.getItem('UserId'), // 用户ID
        classId: activeClassId, // 班级ID
        // chapterId: this.chapterId.map((item) => item[1]).join(','),
        chapterId: ChapterId, // 章节
        whereTime: Type === 1 ? 'xq' : Type === 2 ? 'month' : 'week', // 时间
        // pageNo: this.currentPage, // 页码
        paperType: '-1', // 试卷类型
        iscoach: IsCoach === 0 ? '-1' : IsCoach === 1 ? '0' : '1', // 是否辅导
        platform,
        // pageSize: 10, // 条数
        term: Term, // 学期
        // 新增入参
        PaperName, // 试卷名称
        Sort: this.Sort, // 排序
        Isdzb: UseScene === 0 ? 2 : UseScene === 1 ? 0 : 1, // 是否点阵笔
        ItemType: TypeId, // 试题类型
        Level: Level, // 难度
        BegionAccuracy, // 开始正确率
        EndAccuracy, // 结束正确率
        PeopleCount, // 人数
        Year: Year, // 学年
        SubjectId: localStorage.getItem('UW_SUBJECT') // 学科
      }
      const { data:res } = await this.$uwonhttp.post('/HighFrequency/HighFrequency/GetTeacherHighFrequencyList', data)
      // console.log('#####################')
      // console.log(res)
      if(res.Success){
        if(res.Data.Items !== null){
          res.Data.Items.forEach(item => {
            item.ItemTitle = ExamFormatCommon.format_fraction(item.ItemTitle) // 分数格式化调整
            item.ItemTitle = ExamFormatCommon.format_space(item.ItemTitle) // 填空题空格式化
            item.showType = false
            item.checked = false
          })
          this.errorItemList = res.Data.Items
        } else {
          this.errorItemList = []
        }
        // this.total = res.Data.TotalItems
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 高频错题列表学生视角
    async GetStudentHighFrequencyList() {
      this.selectAll = false
      // const { yearActive, semesterActive, gradeActive, chapterList } = this.leftComponentVal
      const { Year, Term, Ids } = this.$store.state.chapterStorage.ChapterObj
      const { activeStudentId } = this.$refs.studentList
      const ChapterId = Ids.join(',')
      const data = {
        ...this.$refs.wrongQuestionFrom.form,
        // PageIndex: this.currentPage, // 页码
        // PageSize: 10, // 条数
        Year: Year, // 学年
        Semester: Term, // 学期
        ChapterId, // 章节Id多个逗号隔开
        SubjectId: localStorage.getItem('UW_SUBJECT'), // 学科Id
        StudentId: activeStudentId, // 学生ID
        IsTypical: this.IsTypical,
      }
      const { data: res } = await this.$uwonhttp.post('/Paper/TeacherPaper/GetStudentHighFrequencyWrongItem', data)
      if(res.Success){
        if(res.Data.WrongItemInfos.length){
          res.Data.WrongItemInfos.forEach(item => {
            item.Title = ExamFormatCommon.format_fraction(item.Title) // 分数格式化调整
            item.Title = ExamFormatCommon.format_space(item.Title) // 填空题空格式化
            item.Title = item.Title.startsWith('https://') ? `<img src="${item.Title}" style="width: 100px;height: 100px;object-fit:cover">` : item.Title
            item.showType = false
            item.checked = false
            // 单个学生点阵笔试卷信息
            item.imageBase64 = ''
          })
          this.errorItemList = res.Data.WrongItemInfos
        } else {
          this.errorItemList = []
        }
        // this.total = res.Data.WrongItemInfos.length
        this.selectAll = false
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 单个学生导出接口
    async getStudentListFil(){
      // const { yearActive, semesterActive } = this.leftComponentVal
      const { Year, Term } = this.$store.state.chapterStorage.ChapterObj
      const { activeStudentId } = this.$refs.studentList
      const newArr = this.errorItemList.filter(item => item.checked).map(item => item.ItemId)
      if(newArr.length <= 0) return this.$message.warning('请选择题目！')
      const data = {
        Year: Year,
        Semester: Term,
        StudentId: activeStudentId,
        ItemIds: newArr
      }
      const {data: res} = await documentExport.post('/Paper/TeacherPaper/GetStudentWrongItemWord',data,'blob')
      this.fileExport(res)
    },
    // 整班导出接口
    async GetManyStudentWrongItemWord(val){
      this.loading = true
      const { activeClassId } = this.$refs.classListForm
      // const data = {
      //   ...val,
      //   SubjectId: localStorage.getItem('UW_SUBJECT'),
      // }
      const data = {
        ...val,
        ClassId: activeClassId,
        SubjectId: localStorage.getItem('UW_SUBJECT'),
      }
      const { data:res } = await documentExport.post('/Paper/TeacherPaper/GetManyStudentWrongItemWord',data, 'blob')
      this.loading = false
      this.fileExport(res)

    },
    // 文件导出方法
    fileExport(res){
      let url = window.URL.createObjectURL(res);
      let eleLink = document.createElement('a');
      eleLink.href = url
      document.body.appendChild(eleLink)
      eleLink.click()
      window.URL.revokeObjectURL(url)
    },
    // 处理主观题点阵笔作答试题切片
    async errorItemRollUpAndUnfold(ind){
      if (this.errorItemList[ind].showType) return this.errorItemList[ind].showType = !this.errorItemList[ind].showType
      const { ItemId, PaperId, TypeId, UseScene } = this.errorItemList[ind]
      if(TypeId == 36 && UseScene == 2){
        const { activeStudentId } = this.$refs.studentList
        const reqData = {
          ItemId,
          PaperId,
          StudentIds: activeStudentId
        }
        // 请求接口获取整卷笔迹和单题裁剪坐标
        const { data:res } = await this.$uwonhttp.post('/MDPen/MDPen/GetMDPaperSectionInfos',reqData)
        if(res.Success){
          this.errorItemList[ind].imageBase64 = await this.createCanvasInit(res.Data[0])
          this.errorItemList[ind].showType = !this.errorItemList[ind].showType
        } else {
          this.$message.error(res.Msg)
        }
      } else {
        this.errorItemList[ind].showType = !this.errorItemList[ind].showType
      }
    },
    // 初始canvas
    async createCanvasInit(item) {
      const { ImgUrl, ItemSection, Points, width, height } = item
      const imageInfo = {
        ImgUrl,
        width,
        height
      }
      this.canvasVnode?.pause()
      if (!this.canvasVnode) {
        this.canvasVnode = new CanvasVideoSingle(
          imageInfo,
          ItemSection[0].ItemPoints,
          [],
          this.$refs.canvasVideo,
          15
        )
      }
      this.canvasImg = await this.canvasVnode.init({  // 获取的整卷笔迹绘制
        DZPenLogList: [],
        ImageInfo: imageInfo,
        cutPoints: [],
        TchPenLogList: [],
        studentPenLog: ItemSection[0].ItemPoints,
        cutDown: true,
        StudentComments: []
      })
      const ImageInfo = {
        ...imageInfo,
        ImgUrl: this.canvasImg
      }
      const data = {
        ImageInfo,
        cutPoints: Points
      }
      const imageDataURL = await this.canvasVnode.singleClipping(data) // 针对整卷切割单题
      // console.log(imageDataURL)
      return imageDataURL
    }
  },
  beforeRouteLeave(to,from ,next){
    this.loading = false
    next()
  }
}
</script>

<style lang="less" scoped>
.highWrong{
  width: 100%;
  display: flex;
  .back_white {
    height: 92vh;
  }
  .right_Conten{
    .stu_from_list{
      display: flex;
      margin-top: 4px;
      .total_errorBook{
        display: flex;
        justify-content: space-between;
        background-color: #FFFFFF;
        padding: 0 14px 6px;
        ._total,._errorBook{
          display: flex;
          align-items: center;
        }
        ._total{
          .lef{
            font-size: 20px;
            margin-right: 14px;
            span{
              color: #E6A23C;
              margin: 0 4px;
            }
          }
          .el-button{
            font-size: 18px;
            padding: 0;
          }
          .sort-view{
            //// width: 400px !important;
            //i{
            //  width: 10px;
            //}
            display: flex;
            justify-content: flex-end;
            .sortDiv{
              display: flex;
              align-items: center;
              margin: 0 4px;
              .txt{
                font-size: var(--font16-20);
              }
              .icon{
                display: flex;
                flex-direction: column;
                margin-left: 2px;
                i{
                  font-size: 16px;
                }
                i:nth-child(1){
                  position: relative;
                  bottom: -3px;
                }
                i:nth-child(2){
                  position: relative;
                  top: -3px;
                }
              }
            }
          }
        }
        ._errorBook{
          .el-button{
            font-size: 16px;
            padding: 8px 14px;
            margin-left: 10px;
          }
        }
      }
      .allList{
        //height: 65vh;
        background-color: #FFFFFF;
        padding: 0 14px;
        overflow-y: scroll;
      }
      .allList::-webkit-scrollbar{
        display: none;
      }
      //.page{
      //  display: flex;
      //  justify-content: flex-end;
      //  padding: 0 14px;
      //  background-color: #FFFFFF;
      //}
    }
  }
}
</style>
