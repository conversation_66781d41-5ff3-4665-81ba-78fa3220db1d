<template>
  <div class="lessonPre">
    <div
      class="margin_r_5 back_white h-100 flex_item_shrink "
      style="transition: all ease 0.3s"
      :class="{ openInDialogBorder: openInDialog }"
      :style="{ width: leftWidth + '%' }"
    >
      <left-screening ref="leftScreening" :pageId="$route.path" :givelessons="givelessons" :left-width="leftWidth" :popoverDis="givelessons === 'givelessonsIndex'" @changeWidth="leftWidth = leftWidth == 17 ? 2.3 : 17" @getAllList="getLeftScreening"></left-screening>
    </div>
    <div class="right">
      <!-- 三个助手才展示 -->
      <div v-if="$store.state.LoginSource != 1" class="tabs-view">
        <div class="tab-item" @click="changeTab()">题目</div>
        <div class="tab-item active">课件</div>
      </div>
      <div class="tabTop">
        <div class="tab">
          <el-button type="text" v-for="txt in resources" :key="txt.value" :style="{color: form.ResourceLevel == txt.value ? '#7BB999' : ''}" @click="allClick('ResourceLevel',txt.value)">{{ txt.label }}</el-button>
        </div>
        <div class="tabType">
          <p>类型：</p>
          <el-button type="text" v-for="text in resourceTypeList" :key="text.value" :style="{color: form.ResourceType == text.value ? '#7BB999' : ''}" @click="allClick('ResourceType', text.value)">{{ text.label }}</el-button>
        </div>
      </div>
      <div class="ser_lest">
        <div class="search" v-show="!showCheck">
          <div class="importing" @click="$refs.addSource.init()">导入课件</div>
          <div class="sort-view">
            <div class="sortDiv">
              <div class="txt" :style="{color: form.SortType == 0 ? '#7fb99a' : '#999'}" @click="sortBtn(0)">上传时间</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: form.SortType == 0 && form.OrderByType == 0 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: form.SortType == 0 && form.OrderByType == 1 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="sortDiv">
              <div class="txt" :style="{color: form.SortType == 1 ? '#7fb99a' : '#999'}"  @click="sortBtn(1)">使用次数</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: form.SortType == 1 && form.OrderByType == 0 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: form.SortType == 1 && form.OrderByType == 1 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="sortDiv">
              <div class="txt" :style="{color: form.SortType == 2 ? '#7fb99a' : '#999'}"  @click="sortBtn(2)">预览次数</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: form.SortType == 2 && form.OrderByType == 0 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: form.SortType == 2 && form.OrderByType == 1 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="input">
              <el-input placeholder="请输入内容" v-model="form.QueryFileName" class="input-with-select">
                <el-button slot="append" icon="el-icon-search" @click="GetPrepareLessonSourcesV2"></el-button>
              </el-input>
            </div>
          </div>
        </div>
        <div class="_list" :style="{height: listHeight + 'vh'}">
          <list-of-resources
          ref="listOfResources"
          v-for="item in initInfo"
          :key="item.ResourceId"
          :ResourceLevel="form.ResourceLevel"
          :listInfo="item"
          @preview="liLockPreView"
          @delete="delet"
          @download="download"
          @sharing="(ite) => $refs.beiKeSharing.init(ite.ResourceId)"
          @cloning="cloning"
          @editing="editing">
          </list-of-resources>
        </div>
        <div class="page">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="form.PageSize"
            :current-page="form.PageNo"
            @current-change="currentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
<!--  导入资源  -->
    <addSource ref="addSource" :classId="queryInfo.classId" @addSourceFinish="GetPrepareLessonSourcesV2"></addSource>
<!--  预览  -->
    <pptPreview ref="pptPreview" :isprevieDialog.sync="isprevieDialog" :previewItem="previewItem" :isShowFooter="false"></pptPreview>
    <el-dialog
      title=""
      :visible.sync="isImageDialog"
      width="560px"
      @close="isImageDialog=false"
      center
    >
      <div class="flex flex_justify_center"><img style="max-width: 540px;":src="imageUrl" /></div>
    </el-dialog>
    <el-dialog
      title=""
      :visible.sync="isVideoDialog"
      width="560px"
      @close="isVideoDialog=false"
      center
    >
      <video style="max-width: 520px;" :src="videoUrl" controls></video>
    </el-dialog>
    <!--  同校共享  -->
    <bei-ke-sharing ref="beiKeSharing" @Success="GetPrepareLessonSourcesV2"></bei-ke-sharing>
  </div>
</template>

<script>
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import listOfResources from '@/views/Paper/Exam_MicroLesson/components/listArray/listOfResources'
import addSource from '@/views/giveLessons/components/addSource/index.vue'
import pptPreview from '@/views/prepareLessons/ppt/components/editing-courseware/components/preview/index.vue'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper.vue'
import beiKeSharing from '@/views/Paper/Exam_MicroLesson/components/beiKeSharing.vue'

export default {
  name: 'lessonPreparationResources',
  props:{
    listHeight:{
      type: Number,
      default: 66
    },
    givelessons:{
      type: String,
      default: ''
    }
  },
  // 监听路由界面组件展示
  computed:{
    showCheck(){
      return this.$route.path !== '/Paper/Exam_MicroLesson/QuestionBankResourceCenter'
    }
  },
  components:{
    previewPaper,
    leftScreening,
    listOfResources,
    addSource,
    pptPreview,
    beiKeSharing
  },
  data(){
    return {
      openInDialog: false,
      leftWidth: 17, //动态控制布局
      form:{
        Year: 0,
        Term: 0,
        Grade: 0,
        ResourceLevel: 0, // 资源级别：0我的资源；1校本资源
        ResourceType: '', // 资源类型：PDF,PPT,VIDEO,IMAGE
        QueryFileName: '', // 文件名
        SortType: 0, // 排序类型：0上传时间，1使用次数，2预览次数
        OrderByType: 1, // 1倒叙，0正序
        PageNo: 1,
        PageSize: 10, // 需求中没体现标明分页不做写死
        ChapterId: '', // 章节ID
      },
      total: 0,
    // 导入课件
      isDialogSync: false, //添加资源
      queryInfo: {
        classId: '',
        subjectId: localStorage.getItem('UW_SUBJECT'),
        term: '',
        grade: ''
      },
      initInfo: [],
      // 预览组件对象
      isVideoDialog: false,
      videoUrl: '',
      isImageDialog: false,
      imageUrl: '',
      isprevieDialog: false,
      previewItem: {
        Id: '',
        Name: '',
        PageSize: '',
        ShareRangeName: '',
        ShareName: '',
        Source: '',
        type: 0, // 0 草稿，1成果
        isImport: false // 是否展示导入按钮
      },
      // 资源  后端没提供接口前端写死
      resources:[
        {
          value: 1,
          label: '校本资源'
        },
        {
          value: 0,
          label: '我的资源'
        },
      ],
      // 资源类型
      resourceTypeList: [
        {
          value: '',
          label: '全部'
        },
        {
          value: 'PDF',
          label: 'PDF'
        },
        {
          value: 'PPT',
          label: 'PPT'
        },
        {
          value: 'VIDEO',
          label: '视频'
        },
        {
          value: 'IMAGE',
          label: '图片'
        },
        {
          value: 'xlsx',
          label: 'excel'
        }
      ],
      // dialogFormVisible: false,
      // sharing:{
      //   ResourceId: '',
      //   shareType: 0
      // }
    }
  },
  methods:{
    // 左侧章节组件
    getLeftScreening(opt){
      // 组件内部值
      const { yearActive, semesterActive, gradeActive, chapterList } = this.$refs.leftScreening.throwObj
      const chapterLi = chapterList.map(item => item.ChapterId).join(',');
      // vuex携带值
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      this.form.Year = opt ? yearActive : Year
      this.form.Term = opt ? semesterActive : Term
      this.form.Grade = opt ? gradeActive : GradeId
      this.form.ChapterId = opt ? chapterLi : Ids.join(',')
      this.form.PageNo = 1
      this.GetPrepareLessonSourcesV2()
    },
    allClick(key, val){
      this.form[key] = val
      this.GetPrepareLessonSourcesV2()
    },
    // 排序
    sortBtn(e){
      this.form.SortType = e
      if ([0, 1, 2].includes(e)) {
        this.form.OrderByType = this.form.OrderByType === 1 ? 0 : 1;
      }
      this.GetPrepareLessonSourcesV2()
    },
    // 分页
    currentChange(e){
      this.form.PageNo = e
      this.GetPrepareLessonSourcesV2()
    },
    // 备课资源查询列表
    async GetPrepareLessonSourcesV2(){
      const { data:res } = await this.$uwonhttp.post('/MDPen/MDPen/GetPrepareLessonSourcesV2', this.form)
      if(res.Success){
        res.Data.forEach(item => {
          item.checked = false
        })
        this.initInfo = res.Data
        this.total = res.Total
      } else {
        this.$message.error(res.Msg)
      }
      // console.log(res)
    },
    // 删除
    async delet(item){
      const { ResourceId } = item
      const { data:res } = await this.$uwonhttp.get(`MDPen/MDPen/DeletePrepareResourceById?resourceId=${ResourceId}`)
      if(res.Success){
        this.GetPrepareLessonSourcesV2()
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 下载
    download(item){
      const { ResourceUrl } = item
      if(ResourceUrl){
        window.open(ResourceUrl)
      } else {
        this.$message.error('资源不存在')
      }
    },
    // 备课资源内预览
    async liLockPreView(item) {
      const { ResourceId, ResourceName, ResourceType, ResourceUrl } = item
      const { Grade, Term } = this.form
      const { data:res } = await this.$uwonhttp.get(`MDPen/MDPen/ResourceView?resourceId=${ResourceId}`)
      if(res.Success){
        if (['ppt','pdf'].includes(ResourceType)) {
          this.previewItem.Name = ResourceName
          this.previewItem.Grade = Grade
          this.previewItem.TremDesc = Term
          this.previewItem.Id = ResourceId
          this.isprevieDialog = true
        }else if (ResourceType.includes('image')) {
          this.imageUrl = ResourceUrl
          this.isImageDialog = true
        }else {
          this.videoUrl = ResourceUrl
          this.isVideoDialog = true
        }
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 复制
    async cloning(item){
      const { ResourceId } = item
      const {data:res} = await this.$uwonhttp.get(`/MDPen/MDPen/ResourceCopy?resourceId=${ResourceId}`)
      if(res.Success){
        this.$message.success('复制成功')
        this.GetPrepareLessonSourcesV2()
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 编辑
    editing(item){
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/pptEdit',
        query:{
          FileId: item.ResourceId
        }
      })
    },
    // 获取数据给到父级
    returnData(){
      let AddFiles = []
      this.initInfo.forEach(item => {
        if(item.checked){
          AddFiles.push({
            Url: '',
            Name: item.ResourceName,
            ResourceId: item.ResourceId,
            FileType: item.ResourceType,
          })
        }
      })
      return {
        // LessonPeriodId: "",
        TeachingName: "",
        AddFiles,
        Share: 0
      }
    },
    changeTab() {
      this.$router.push({
        path:'/threeAssistants/newJobResources/index'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.tabs-view {
  display: flex;
  align-items: center;
  // margin-bottom: 20px;
  // border-bottom: 1px solid #e6e6e6;
  background: white;
  .tab-item {
    position: relative;
    padding: 12px 24px;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    &:hover {
      color: #7fb99a;
    }
    &.active {
      color: #7fb99a;
      font-weight: 500;
      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: #7fb99a;
        border-radius: 2px;
      }
    }
  }
}
.lessonPre{
  display: flex;
  height: 100%;
  .right{
    width: 100%;
    .tabTop{
      //width: 100%;
      padding: 10px;
      background-color: #FFFFFF;
      border-radius: 2px;
      .tab{
        .el-button{
          padding: 4px 14px 4px 0;
          font-size: 18px;
          font-weight: bold;
          color: #6d7278;
        }
      }
      .tabType{
        display: flex;
        align-items: center;
        p{
          font-size: 18px;
          padding-top: 7px;
        }
        .el-button{
          font-size: 18px;
          color: #6d7278;
          padding: 10px 12px 4px 0;
        }
      }
    }
    .ser_lest{
      margin-top: 10px;
    }
    .search{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0 8px 18px;
      background-color: #ffffff;
      //border-bottom: 1px solid #DCDCDCFF;
      .importing{
        font-size: 15px;
        padding: 4px 10px;
        background-color: #7FB99A;
        color: #FFFFFF;
        border-radius: 3px;
        cursor: pointer;
      }
      .sort-view {
        //width: 50%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .sortDiv {
          display: flex;
          align-items: center;
          margin: 0 4px;

          .txt {
            font-size: var(--font16-20);
          }

          .icon {
            display: flex;
            flex-direction: column;
            margin-left: 2px;

            i {
              font-size: 16px;
            }

            i:nth-child(1) {
              position: relative;
              bottom: -3px;
            }

            i:nth-child(2) {
              position: relative;
              top: -3px;
            }
          }
        }
        .input{
          width: 40%;
        }
      }
    }
    ._list{
      //height: 66vh;
      overflow: auto;
      background-color: #ffffff;
    }
    .page{
      padding: 4px 20px;
      display: flex;
      justify-content: flex-end;
      background: #FFFFFF;
    }
  }
}
</style>