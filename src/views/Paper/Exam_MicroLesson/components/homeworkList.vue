<template>
  <div class="lessonPre">
    <div
      class="margin_r_5 back_white h-100 flex_item_shrink "
      style="transition: all ease 0.3s"
      :class="{ openInDialogBorder: openInDialog }"
      :style="{ width: leftWidth + '%' }"
    >
      <!--      <left-screening ref="leftScreening" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 16 ? 2.3 : 16" @getAllList="getLeftScreening"></left-screening>-->
      <left-screening ref="leftScreening" :pageId="$route.path" givelessons="givelessonsIndex" :left-width="leftWidth" :popoverDis="true" @changeWidth="leftWidth = leftWidth == 17 ? 2.3 : 17" @getAllList="getLeftScreening"></left-screening>
    </div>
    <div class="right">
      <div class="tabTop">
        <div class="tab" v-if="LoginSource">
          <el-button type="text" v-for="tab in isDzbList" :key="tab.value" @click="btnIsDZB(tab.value)" :style="{color: form.IsDZB == tab.value ? '#7BB999' : ''}">
            {{ tab.label }}
          </el-button>
        </div>
        <div class="tabType">
          <p>作业类型：</p>
          <el-button type="text" v-for="btn in TypeOfTestPaper" :key="btn.value" @click="paperTypeBtn(btn.value)" :style="{color: form.PaperType == btn.value ? '#7BB999' : ''}">{{btn.label}}</el-button>
        </div>
      </div>
      <div class="ser_lest">
<!--        <div class="search">-->
<!--          <div class="importing" @click="isDialogSync = true">导入课件</div>-->
<!--          <div class="sort-view">-->
<!--            <div class="sortDiv">-->
<!--              <div class="txt" :style="{color: [1,4].includes(promise.Sort) ? '#7fb99a' : '#999'}" @click="sortBtn(1,4)">创建时间</div>-->
<!--              <div class="icon">-->
<!--                <i class="el-icon-caret-top" :style="{color: promise.Sort == 4 ? '#7fb99a' : '#999'}"></i>-->
<!--                <i class="el-icon-caret-bottom" :style="{color: promise.Sort == 1 ? '#7fb99a' : '#999'}"></i>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="sortDiv">-->
<!--              <div class="txt" :style="{color: [2,5].includes(promise.Sort) ? '#7fb99a' : '#999'}"  @click="sortBtn(2,5)">布置班级数</div>-->
<!--              <div class="icon">-->
<!--                <i class="el-icon-caret-top" :style="{color: promise.Sort == 5 ? '#7fb99a' : '#999'}"></i>-->
<!--                <i class="el-icon-caret-bottom" :style="{color: promise.Sort == 2 ? '#7fb99a' : '#999'}"></i>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="sortDiv">-->
<!--              <div class="txt" :style="{color: [3,6].includes(promise.Sort) ? '#7fb99a' : '#999'}"  @click="sortBtn(3,6)">已做学生数</div>-->
<!--              <div class="icon">-->
<!--                <i class="el-icon-caret-top" :style="{color: promise.Sort == 6 ? '#7fb99a' : '#999'}"></i>-->
<!--                <i class="el-icon-caret-bottom" :style="{color: promise.Sort == 3 ? '#7fb99a' : '#999'}"></i>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="input">-->
<!--              <el-input placeholder="请输入内容" v-model="promise.PaperName" class="input-with-select">-->
<!--                <el-button slot="append" icon="el-icon-search" @click="GetPaperDesignInfo"></el-button>-->
<!--              </el-input>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
        <div class="input">
          <el-input placeholder="请输入内容" v-model="form.PaperTitle" class="input-with-select">
            <el-button slot="append" icon="el-icon-search" @click="GetTeacherPaperListPractice"></el-button>
          </el-input>
        </div>
        <div class="_list">
          <list-of-resources ref="listOfResources" v-for="(els,ind) in listData" :key="ind" :listInfo="els" stateType="zuoYe" @preview="preview"></list-of-resources>
        </div>
      </div>
    </div>
    <!--  预览  -->
    <preview-paper ref="previewPaper"></preview-paper>
  </div>
</template>

<script>
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import listOfResources from '@/views/Paper/Exam_MicroLesson/components/listArray/listOfResources'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper.vue'

export default {
  name: 'lessonPreparationResources',
  components:{
    previewPaper,
    leftScreening,
    listOfResources,
  },
  props:{
    classId:{
      type: String,
      default: ''
    }
  },
  computed:{
    // 区分平台 1：专科专练 2：三个助手
    LoginSource(){
      return localStorage.getItem('LoginSource') === '1'
    }
  },
  data(){
    return {
      openInDialog: false,
      leftWidth: 17, //动态控制布局
      promise:{
        Sort: 1,
      },
      listData: [],
      TypeOfTestPaper:[
        {
          label: '全部',
          value: 0
        },
        {
          label: '市级',
          value: 1
        },
        {
          label: '区级',
          value: 2
        },
        {
          label: '校本',
          value: 3
        },
        {
          label: '我的',
          value: 4
        }
      ],
      isDzbList: [
        {
          label: '纸笔作业',
          value: 1
        },
        {
          label: '电子作业',
          value: 0
        }
      ],
      // 提交信息
      form:{
        ChapterIds: [],
        PaperType: 0, // 试卷类型 0全部：1市：2区：3校：4班
        paperTag: 0, // 练习册类型
        ReviewStatus: 0, // 0全部，1-批阅中 2-未批阅 3-已批阅 4-未提交
        ReleaseStates: 1, // 0全部，1-已发布 2-未发布
        PracticeStates: 0, // 0全部，1作答中，2已截至，3-待批阅，4-已完成
        OrderById: 5, // 正序：0推送时间，1难度，2正确率，3做卷人数，4平均时长。倒序：5推送时间，6难度，7正确率，8做卷人数，9平均时长
        // ClassId: '', // 班级Id
        UserId: localStorage.getItem('UserId'), // 用户Id
        PageNo: 1, // 页码
        PageIndex: 30000, // 条数 /*原型需求文档中没有体现分页不做写死*/
        // IsDZB: 1, // 是否点阵笔
        IsDZB: 0, // 是否点阵笔
        PaperTitle: '', // 试卷标题
        Platform: 1,// 1专科专练，2上海微校
        Term: 1, // 1上学期，2下学期
        Year: ''
      },
    }
  },
  mounted() {
    this.form.IsDZB = this.$store.state.LoginSource === '1' ? 0 : -1
  },
  methods:{
    // init(classId){
    //   // this.form.ClassId = classId
    //   this.getLeftScreening()
    // },
    // 练习类型切换
    paperTypeBtn(val){
      this.form.PaperType = val
      this.GetTeacherPaperListPractice()
    },
    // 是否电子作业
    btnIsDZB(e){
      this.form.IsDZB = e
      this.GetTeacherPaperListPractice()
    },
    // 预览
    preview(item){
      this.$refs.previewPaper.init(item.PaId)
    },
    // 左侧章节组件
    getLeftScreening(opt){
      const { yearActive, semesterActive, gradeActive, chapterList } = this.$refs.leftScreening.throwObj
      const chapterLi = chapterList.map(item => item.ChapterId)
      // vuex携带值
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      this.form.Year = yearActive ? yearActive : Year
      this.form.Term = semesterActive ? semesterActive : Term
      this.form.GradeId = gradeActive ? gradeActive : GradeId
      this.form.ChapterIds = opt ? chapterLi : Ids
      this.GetTeacherPaperListPractice()
    },
    async GetTeacherPaperListPractice(){
     const { data:res } = await this.$uwonhttp.post('/Paper/TeacherPaper/GetTeacherPaperListPractice', { ClassId: this.classId, ...this.form })
      if(res.Success){
         // 向res.Data.Items中循环添加属性checked = false
        res.Data.Items.forEach(item => {
          item.checked = false
        })
        this.listData = res.Data.Items
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取数据
    returnData(){
      let AddFiles = []
      this.listData.forEach(item => {
        if(item.checked){
          AddFiles.push({
            Url: '',
            Name: item.PaperTitle,
            ResourceId: item.PaId,
            FileType: 'doc',
          })
        }
      })
      return {
        // LessonPeriodId: "",
        TeachingName: "",
        AddFiles,
        Share: 0
      }
    }
  }
}
</script>

<style lang="less" scoped>
.lessonPre{
  display: flex;
  height: 100%;
  .right{
    width: 100%;
    .tabTop{
      //width: 100%;
      padding: 10px;
      background-color: #FFFFFF;
      border-radius: 2px;
      .tab{
        .el-button{
          padding: 4px 14px 4px 0;
          font-size: 20px;
          color: #6d7278;
        }
      }
      .tabType{
        display: flex;
        align-items: center;
        p{
          font-size: 18px;
          padding-top: 7px;
        }
        .el-button{
          font-size: 18px;
          color: #6d7278;
          padding: 10px 12px 4px 0;
        }
      }
    }
    .ser_lest{
      margin-top: 10px;
      .input{
        width: 30%;
      }
    }
    //.search{
    //  display: flex;
    //  justify-content: space-between;
    //  align-items: center;
    //  padding: 10px 0 8px 18px;
    //  background-color: #ffffff;
    //  //border-bottom: 1px solid #DCDCDCFF;
    //  .importing{
    //    font-size: 15px;
    //    padding: 4px 10px;
    //    background-color: #7FB99A;
    //    color: #FFFFFF;
    //    border-radius: 3px;
    //    cursor: pointer;
    //  }
    //  .sort-view {
    //    //width: 50%;
    //    display: flex;
    //    align-items: center;
    //    justify-content: flex-end;
    //    .sortDiv {
    //      display: flex;
    //      align-items: center;
    //      margin: 0 4px;
    //
    //      .txt {
    //        font-size: var(--font16-20);
    //      }
    //
    //      .icon {
    //        display: flex;
    //        flex-direction: column;
    //        margin-left: 2px;
    //
    //        i {
    //          font-size: 16px;
    //        }
    //
    //        i:nth-child(1) {
    //          position: relative;
    //          bottom: -3px;
    //        }
    //
    //        i:nth-child(2) {
    //          position: relative;
    //          top: -3px;
    //        }
    //      }
    //    }
    //
    //  }
    //}
    ._list{
      background-color: #ffffff;
      overflow: auto;
      height: 60vh;
    }
  }
}
</style>
<style>
.v-modal{
  z-index: 1000 !important;
}
</style>