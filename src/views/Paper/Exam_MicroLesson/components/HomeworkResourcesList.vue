<template>
  <div class="homeWorkDis">
    <div
      class="margin_r_5 back_white h-100 flex_item_shrink "
      style="transition: all ease 0.3s"
      :class="{ openInDialogBorder: openInDialog }"
      :style="{ width: leftWidth + '%' }"
    >
      <left-screening ref="leftScreening" :pageId="$route.path" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 18 ? 2.3 : 18" @getAllList="getLeftScreening"></left-screening>
    </div>
    <div class="workRight">
      <div class="tabSearch">
        <div class="jobBtn">
          <div class="_list">
            <el-button type="text" v-for="item in jobTabs" :key="item.value" :class="{active: item.value == promise.PaperType}" @click="jobType(item.value)">{{ item.text }}</el-button>
          </div>
          <el-button type="text" icon="el-icon-plus" style="font-size: 16px;color:#7fb99a;" @click="OpenAddAJobPopup">新增作业</el-button>
        </div>
        <div class="fenlei" v-if="promise.PaperType != 2">
          分类：
          <el-button type="text" v-for="item in newClassify" :key="item.value" :class="{active: item.value == promise.Type}" v-show="item.restrictions.includes(promise.PaperType)" @click="classify(item.value)">{{ item.label }}</el-button>
        </div>
        <div class="fenlei">
          状态：
          <el-button type="text" v-for="item in stateList" :key="item.value" :class="{active: item.value == promise.State}" @click="stateCli(item.value)">{{ item.label }}</el-button>
        </div>
      </div>
      <div class="searchSrot">
        <div class="input">
          <el-input placeholder="请输入内容" v-model="promise.PaperName" class="input-with-select">
            <el-button slot="append" icon="el-icon-search" @click="GetPaperDesignInfo"></el-button>
          </el-input>
        </div>
        <div class="sort">
          <div class="divTotal">共<span style="color: #7FB99A;">{{ total }}</span>条作业</div>
          <span style="margin: 0 12px;">|</span>
          <div class="sort-view">
            <div class="sortDiv">
              <div class="txt" :style="{color: [1,4].includes(promise.Sort) ? '#7fb99a' : '#999'}" @click="sortBtn(1,4)">创建时间</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: promise.Sort == 4 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: promise.Sort == 1 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="sortDiv">
              <div class="txt" :style="{color: [2,5].includes(promise.Sort) ? '#7fb99a' : '#999'}"  @click="sortBtn(2,5)">布置班级数</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: promise.Sort == 5 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: promise.Sort == 2 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
            <div class="sortDiv">
              <div class="txt" :style="{color: [3,6].includes(promise.Sort) ? '#7fb99a' : '#999'}"  @click="sortBtn(3,6)">已做学生数</div>
              <div class="icon">
                <i class="el-icon-caret-top" :style="{color: promise.Sort == 6 ? '#7fb99a' : '#999'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: promise.Sort == 3 ? '#7fb99a' : '#999'}"></i>
              </div>
            </div>
        </div>
      </div>
      </div>
      <div class="arrList" :style="{height: promise.PaperType != 2 ? '69%' : '74%' }">
        <home-work-array-list
        v-for="item in arrList"
        :key="item.Id"
        :paperInfo="item"
        :paperType="promise.PaperType"
        @allSuccess="GetPaperDesignInfo"
        @OpenAddAJobPopup="OpenAddAJobPopup"
        @previewPaper="previewPaper"
        @editPaperInfo="editPaperInfo"
        @goHelpSeekingProgress="goHelpSeekingProgress"
        @pageChange="pageChange"
        ></home-work-array-list>
        <!--    分页    -->
      </div>
      <div class="pageFenye">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :page-size="pageSize"
          :current-page="promise.PageIndex"
          :total="total"
          @current-change="currentChange">
        </el-pagination>
      </div>
  </div>
  <!--  预览  -->
    <preview-paper ref="previewPaper" @addPaperItem="addPaperItem" @removePreviewItem="removePreviewItem" :btn-show="true"></preview-paper>
  <!-- 复制组件 -->
    <add-a-job-popup ref="addAJobPopup"></add-a-job-popup>
  <!-- 试题栏 -->
    <item-list
    ref="itemList"
    @preview="() => {$refs.previewPaper.GetQuestionItemBasketPreviewInfo()}"
    @generateAJob="generatingJobs"></item-list>
  </div>
</template>

<script>
import leftScreening from '@/views/threeAssistants/components/leftScreening'
import HomeWorkArrayList from '@/views/Paper/Exam_MicroLesson/components/HomeWorkArrayList'
import addAJobPopup from '@/views/threeAssistants/components/JobDesign/AddAJobPopup'
import previewPaper from '@/views/Paper/Exam_MicroLesson/previewPaper'
import itemList from '@/views/threeAssistants/newJobResources/components/itemList/index.vue'
export default {
  name: 'HomeworkResourcesList',
  components:{
    leftScreening,
    HomeWorkArrayList,
    addAJobPopup,
    previewPaper,
    itemList
  },
  data(){
    return {
      openInDialog: false,
      leftWidth: 18, //动态控制布局
      jobTabs: [], // 作业筛选
      total: 0,
      arrList: [], // list数据
      newClassify: [ // 分类
        { label: '全部', value: 0, restrictions: ['2', '3', '4', '5'] },
        { label: '校建', value: 3, restrictions: ['3'] },
        { label: '教师共享', value: 6, restrictions: ['3'] },
        { label: '我的共享', value: 4, restrictions: ['3'] },
        { label: '市建', value: 9, restrictions: ['5'] },
        { label: '市共享', value: 10, restrictions: ['5'] },
        { label: '区建', value: 11, restrictions: ['4'] },
        { label: '区共享', value: 12, restrictions: ['4'] },
        { label: '教材资源', value: 13, restrictions: ['3'] },
        { label: '已布置', value: 1, restrictions: ['2'] },
        { label: '未布置', value: 2, restrictions: ['2'] },
        { label: '作废', value: 14, restrictions: ['2'] },
      ],
      // 状态
      stateList: [
        { label: '全部', value: 0 },
        { label: '已布置', value: 1 },
        { label: '未布置', value: 2 },
        { label: '作废', value: 3 },
      ],
      pageSize: 10,
      promise: {
        Sort: 1,
        PaperType: '2', // 作业类型
        Type: 0, // 分类
        PageIndex: 1, // 页码
        PaperName: '',
        Year: 0, // 学年
        Semester: 1, // 学期上下
        GradeId: 1, // 年级
        ChapterId: '', // 章节ID
        State: 0, // 状态
      },
      // newChapterId: [],
      previewId: '', // 触发预览的试卷I的

    }
  },
  created() {
    this.GetPaperShowMenu()
  },
  watch:{
    $route(to){
      if(to.path === '/Paper/Exam_MicroLesson/QuestionBankResourceCenter'&& to.query.activeType === 'ZuoYeResources'){
        this.promise.PageIndex = 1
        this.GetPaperDesignInfo()
        this.GetQuestionItemBasketInfo()
      }
    }
  },
  methods:{
    // 更新试题栏数据
    GetQuestionItemBasketInfo(){
      this.$refs.itemList.GetQuestionItemBasketInfo()
    },
    // 试卷预览的添加
    addPaperItem(Id){
      this.$refs.itemList.SaveQuestionItemBasket(Id)
      this.getPaperItemBasketPreviewInfo('试卷信息')
    },
    // 生成作业开弹窗
    generatingJobs(checkedIds) {
      // const { Year, Semester, GradeId } = this.commonData
      // const [ unit, ChapterId ] = this.chapterIds
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj // vuex携带左侧章节数据
      const [ unit, ChapterId ] = Ids.length == 1 ? [ParentId[0], Ids[0]] : ['','']
      const passingParameters = {
        Year: Year,
        Semester: Term,
        GradeId: GradeId,
        unit ,
        ChapterId,
        TextbookId: -1,
      }
      // 题库ID数组
      // this.$refs.addAJobPopup.setBankIds(this.checkedIds)
      this.$refs.addAJobPopup.setBankIds(checkedIds)
      this.$refs.addAJobPopup.init(passingParameters, '试题栏')
    },
    // 预览页删除题目栏数据
    removePreviewItem({btnText, itemId}){
      this.$refs.itemList.DelQuestionItemBasket({type: 'item', Id: itemId})
      this.getPaperItemBasketPreviewInfo(btnText)
    },
    // 添加移出重新执行接口数据
    getPaperItemBasketPreviewInfo(btnText){
      const time = setTimeout(() => {
        if(btnText === '试卷信息'){
          this.$refs.previewPaper.HomeworkItemInfoList(this.previewId)
        } else {
          this.$refs.previewPaper.GetQuestionItemBasketPreviewInfo()
        }
        clearTimeout(time)
      },500)
    },
    // 计算数据总条数前进一页
    pageChange(){
      const page = (this.total + this.pageSize - 1)/this.pageSize
      if(this.promise.PageIndex == page && Number.isInteger(page)){
        this.promise.PageIndex = page - 1
      }
      this.GetPaperDesignInfo()
    },
    // 左侧章节组件
    getLeftScreening(opt){
      // const { yearActive, semesterActive, gradeActive, chapterList } = opt
      const { Year, Term, GradeId, ParentId, Ids } = this.$store.state.chapterStorage.ChapterObj
      // this.newChapterId = chapterList
      this.promise.PageIndex = 1
      this.promise.Year = Year
      this.promise.Semester = Term
      this.promise.GradeId = GradeId
      this.promise.ChapterId = Ids.join(',')
      this.GetPaperDesignInfo()
    },
    // 试卷类型
    jobType(val){
      this.promise.PageIndex = 1
      this.promise.PaperType = val
      this.promise.Type = 0
      this.promise.State = 0
      this.GetPaperDesignInfo()
    },
    // 分类
    classify(val){
      if(val == 13 && sessionStorage.getItem('IsJCLXC') == 0){
        this.$alert('您的学校暂未申请开通教材资源，详情请咨询客服人员:021-54486816', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            return false
          }
        });
        return
      } else {
        this.promise.PageIndex = 1
        this.promise.Type = val
        this.GetPaperDesignInfo()
      }
    },
    // 状态
    stateCli(val){
      this.promise.PageIndex = 1
      this.promise.State = val
      this.GetPaperDesignInfo()
    },
    // 排序
    sortBtn(e, r) {
      this.promise.PageIndex = 1
      if(this.promise.Sort == e){
        this.promise.Sort = r
      } else {
        this.promise.Sort = e
      }
      this.GetPaperDesignInfo()
    },
    // 分页
    currentChange(val){
      this.promise.PageIndex = val
      this.GetPaperDesignInfo()
    },
    // 复制窗口
    OpenAddAJobPopup(item) {
      const { Year, Semester, GradeId } = this.promise
      // const passingParameters = {
      //   PaperId: item ? item.Id : '',
      //   Year: Year,
      //   Semester:Semester,
      //   GradeId:GradeId,
        // unit: item ? item.UnitId : unit,
        // ChapterId: item ? item.ChapterId : ChapterId,
        // unit: '',
      //   ChapterId: '',
      //   TextbookId: '',
      //   title: item ? item.Title : '',
      // }
      // this.$refs.addAJobPopup.init(passingParameters)

      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: {
          PaperId: item ? item.Id : '',
          Year: Year,
          Semester: Semester,
          GradeId: GradeId,
          // unit: item ? item.UnitId : unit,
          // ChapterId: item ? item.ChapterId : ChapterId,
          TextbookId: item ? item.TextbookId : '',
          title: item ? item.Title : '',
        },
      });
    },
    // 预览
    previewPaper(id){
      this.previewId = id
      this.$refs.previewPaper.init(id)
    },
    // 编辑
    editPaperInfo(item){
      const { Year, Semester, GradeId } = this.promise
      // console.log(this.promise)
      // return false
      this.$router.push({
        path: '/threeAssistants/JobDesign/NewJob',
        query: {
          paperId: item.Id,
          id: 103,
          ChapterId: item.ChapterId,
          Semester: Semester,
          Year: Year,
          GradeId,
          TextbookId: item.TextbookId
        },
      })
    },
    // 跳转至求助列表
    goHelpSeekingProgress(){
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/helpSeekingProgress',
        query:{
          grade: this.promise.GradeId,
          year: this.promise.Year
        }
      })
    },
    // 获取作业筛选数据接口
    async GetPaperShowMenu(){
      const { data:res } = await this.$uwonhttp.get(`/Question/Question/GetPaperShowMenu?roleId=${localStorage.getItem('role')}`)
      if(res.Success){
        this.jobTabs = res.Data
        // this.promise.PaperType = res.Data[0].value
      }else {
       this.$message.error(res.Msg)
      }
    },
    // 数据list获取
    async GetPaperDesignInfo(){
      const promis = {
          ...this.promise,
          SubjectId: localStorage.getItem('UW_SUBJECT'), // 学科
          UserId: localStorage.getItem('UserId'), // 用户Id
          UserRoleId: localStorage.getItem('role'), // 用户角色Id
          TextbookId: '', // 教材Id
          PageSize: 10,
      }
      const { data:res } = await this.$uwonhttp.post('/Question/Question/GetPaperDesignInfo',promis)
      if(res.Success){
        this.arrList = res.Data.DataInfos
        this.total = res.Data.Total
      } else {
        this.$message.error(res.Msg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.homeWorkDis{
  position: relative;
  display: flex;
  // 试题栏
  /deep/.elPopover{
    position: absolute;
    right: 0;
    top: 10%;
    width: 40px;
    //height: 120px;
    line-height: 20px;
    padding: 16px 12px;
    font-size: 18px;
    background: #7fb99a;
    color: #FFFFFF;
    text-align: center;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    z-index: 999;
    cursor: pointer;
    .spanRed{
      width: 20px;
      border-radius: 100px;
      font-size: 12px;
      background: #ff0000;
      color: #FFFFFF;
      position: absolute;
      top: -4px;
      right: 0px;
    }
    i{
      font-size: 20px;
    }
  }
  .workRight{
    //width: 100%;
    width: 84%;
    .tabSearch{
      background: #FFFFFF;
      margin-bottom: 6px;
      .jobBtn{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid #E5E5E5;
        .el-button{
          padding: 4px 0;
          font-size: 20px;
          color: #6e6e6e;
          font-weight: bold;
        }
      }
      .fenlei{
        padding: 6px 20px;
        font-size: 20px;
        .el-button{
          padding: 4px 0;
          font-size: 18px;
          color: #6e6e6e;
        }
      }
    }
    .searchSrot{
      display: flex;
      justify-content: space-between;
      //padding: 16px 20px;
      padding: 13px 20px 7px;
      background: #FFFFFF;
      .input{
        width: 30%;
      }
      .sort{
        display: flex;
        align-items: center;
        .divTotal{
          font-size: 18px;
          margin-right: 10px;
          span{
            font-size: 22px;
          }
        }
        .sort-view{
          display: flex;
          justify-content: flex-end;
          .sortDiv{
            display: flex;
            align-items: center;
            margin: 0 4px;
            .txt{
              font-size: var(--font16-20);
            }
            .icon{
              display: flex;
              flex-direction: column;
              margin-left: 2px;
              i{
                font-size: 16px;
              }
              i:nth-child(1){
                position: relative;
                bottom: -3px;
              }
              i:nth-child(2){
                position: relative;
                top: -3px;
              }
            }
          }
        }
      }
    }
    .arrList{
      //height: 74%;
      overflow-y: scroll;
      background: #FFFFFF;
    }
    .arrList::-webkit-scrollbar{
      display: none;
    }
    .pageFenye{
      background-color: #FFFFFF;
      display: flex;
      padding: 6px 10px;
      justify-content: flex-end;
    }
  }
}
.active{
  color: #7fb99a !important;
}
</style>