<template>
  <div class="headerTabs" style="height: 54px;">
    <div class="tabs_left">
      <ul>
        <li v-for="item in tabsList" :key="item.ind" :class="{ 'liActive': item.ind === tabsActive }" v-show="item.level.includes(getLevel)" @click="Jump(item.ind)">{{item.label}}</li>
      </ul>
    </div>
    <div class="tabs_right">
      <div class="one" @click="tabsClick">
        <img src="@/assets/aibeike.png" />
      </div>
      <el-divider direction="vertical"></el-divider>
      <div class="ther" @click="lessonClick">
        <img src="@/assets/zhinjx.png" />
      </div>
<!--      <HelpVideos ref="HelpVideos" videoName="教学中心操作指南"></HelpVideos>-->
    </div>
  </div>
</template>

<script>
import HelpVideos from '@/components/HelpVideos/HelpVideos'
export default {
  name: 'headOfHeadNavigation',
  components:{
    HelpVideos
  },
  data() {
    return {
      userId: '',
      teachingMaterial:[],
      chapterAll: [], // 原始数据
      chapterArray: [],
      classArray: [],
      textbookVersion: '',
      term: 3,
      chapter: [],
      classId: '',
      tabsList:[
        {
          label: '首页',
          ind: '9',
          level: [ '2', '5' ]
        },
        {
          label: '作业中心',
          ind: '3',
          level: [ '2', '5' ]
        },
        {
          label: '高频错题',
          ind: '6',
          level: [ '2', '5' ]
        },
        {
          label: '作业资源',
          ind: '4',
          level: [ '2', '5' ]
        }
      ],
      tabsActive: '9'
    }
  },
  computed:{
    getLevel(){
      return sessionStorage.getItem('Level')
    }
  },
  mounted() {
    this.Jump(sessionStorage.getItem('tabsActive') || this.tabsActive)
  },
  methods: {
    tabsClick() {
      this.$message({
        message: '该功能暂未开放',
        type: 'warning'
      })
    },
    lessonClick() {
      this.$router.push({
        path:'/giveLessons/index'
      })
    },
  // 根据状态做跳转
    Jump(ind) {
      if(['3','4','5','6','7','8','9'].includes(ind)) {
        this.tabsActive = ind
        sessionStorage.setItem('tabsActive', ind)
      }
      // 首页
      if(ind === '9') {
        this.$router.push({
          path:'/Home/Introduce'
        })
      }
      // 作业中心
      if(ind === '3') {
        this.$router.push({
          path:'/Teacher/My_Task/newHomepage'
        })
      }
      // 作业资源
      if(ind === '4') {
        this.$router.push({
          path:'/threeAssistants/newJobResources/index'
        })
      }
      // 高频错题
      if(ind === '6') {
        this.$router.push({
          path:'/threeAssistants/highFrequencyErrors',
        })
      }
    },
  }
}
</script>

<style lang="less" scoped>
.headerTabs{
  // height: 68px;
  padding: 0 16px 0 26px;
  background: #FFFFFF;
  display: flex;
  justify-content: space-between;
  .tabs_left,.tabs_right{
    display: flex;
    align-items: center;
  }
  .tabs_left{
    ul{
      height: 54px;
      background: transparent;
      border-radius: 0;
      padding: 0;
      list-style: none;
      display: flex;
      align-items: flex-end;
      font-weight: 400;
      font-size: var(--font16-22);
      color: #666666;
      li{
        width: auto;
        height: auto;
        line-height: 54px;
        text-align: center;
        border-radius: 0;
        cursor: pointer;
        padding: 0 24px;
        margin-right: 32px;
        position: relative;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
        
        &:hover {
          color: #537566;
        }
      }
      .liActive{
        background-color: transparent;
        color: #537566;
        border-bottom: 3px solid #537566;
      }
    }
  }
  .tabs_right{
    .one,.ther{
      display: flex;
      align-items: center;
      font-size: 24px;
      cursor: pointer;
    }
    .one{
      margin-right: 10px;
      img{
        width: 75px;
        height: 26px;
      }
    }
    .ther{
      margin-left: 10px;
      img{
        width: 95px;
        height: 26px;
      }
    }
  }
}
</style>
