<template>
  <a-layout>
    <global-header
      v-show="$store.state.user.isAside"
      :mode="layoutMode"
      :menus="menus"
      :theme="navTheme"
      :collapsed="collapsed"
      :device="device"
      @toggle="toggle"
      @trueShow="trueShow"
    />
<!--  三个助手登录用户看到的  -->
    <head-of-head-navigation v-if="$store.state.LoginSource == 2 && routeList.includes(path)"></head-of-head-navigation>
    <a-layout v-if="$store.state.LoginSource == 1" :style="layoutStyle" style="height: calc(100vh - 49px)" class="ant-layout-has-sider">
<!--      <div>-->
        <template v-if="$store.state.user.isZkzl && $store.state.user.isAside">
          <Aside v-if="$store.state.user.userRole && $store.state.user.isShowAside"> </Aside>
          <side-menu
           v-if="!$store.state.user.userRole"
            mode="inline"
            :menus="menus"
            :theme="navTheme"
            :collapsed="collapsed"
            :collapsible="true"
          >
          </side-menu>
        </template>
        <template v-if="!$store.state.user.isZkzl">
          <side-menu
            mode="inline"
            :menus="menus"
            :theme="navTheme"
            :collapsed="collapsed"
            :collapsible="true"
          >
          </side-menu>
        </template>
<!--      </div>-->
      <a-layout-content :style="layoutPadding" id="layout_main" :class="layoutUseBlack ? 'UseBlack' : ''">
<!--        <multi-tab v-if="showCrumbs === 'Admin' || showCrumbs === '1357896248794812416'"></multi-tab>-->
        <multi-tab v-if="showCrumbs === '系统运维'"></multi-tab>
        <route-view />
      </a-layout-content>
    </a-layout>
    <!--  三个助手登录用户看到的  -->
<!--    <a-layout class="ant-layout-has-sider" style=" returnStyle height: calc(100vh - 117px)" v-else>-->
<!--    <a-layout class="ant-layout-has-sider" :style="returnStyle" v-else>-->
    <a-layout class="ant-layout-has-sider" :style="teachingRou ? { height: '100vh' } : returnStyle" v-else>
      <a-layout-content :style="layoutPadding" id="layout_main" :class="layoutUseBlack ? 'UseBlack' : ''">
<!--        <multi-tab v-if="showCrumbs === 'Admin' || showCrumbs === '1357896248794812416'"></multi-tab>-->
        <multi-tab v-if="showCrumbs === '系统运维'"></multi-tab>
        <route-view />
      </a-layout-content>
    </a-layout>
    <div class="faIcon" v-show="faIconShow" @click="elIConBtn">
      <div>
        <h1>版权声明</h1>
        <p>
          上海市教师教育学院（上海市教育委员会教学研究室）拥有本网站市级教学资源的著作权，其中，参与建设的相关区教育学院、区教师进修学院、区教育发展研究院，与上海市教师教育学院（上海市教育委员会教学研究室）共同共有其参与建设部分的市级教学资源的著作权。
        </p>

        <p>
          未经著作权人的书面授权，任何单位或个人不得以任何方式使用、转载、引用、复制等，并明确禁止对本网站所属的服务器做镜像。著作权人保留追究侵权者法律责任之权利。
        </p>

        <p>
          本网站用户自行上传的资源或发布的内容，纯属提交者个人行为，与本网站立场无关。本网站对于用户自行上传的资源或发布的内容所引发的著作权纠纷或
          其他纠纷，在尽到了法律要求的义务后不承担任何责任。
        </p>

        <p>
          本网站旨在促进基础教育高质量发展，并在有限的教育范围内提供用户教学使用。市级教学资源中部分图片、视频、音乐、文本等作品来源于相关专著、网络资源等，尽管已努力查询并标注来源，但未免有疏漏之处，如存在个别未标注的情形，敬请谅解！本网站教学资源中涉及的资料，如作者或权利人不希望我们在教学资源中使用其作品，请联系本网站，我们会立即将有关内容从网上移除。
        </p>
        <i class="el-icon-close elIconButton" @click="elIConBtn"></i>
      </div>
    </div>
  </a-layout>
</template>

<script>
import { triggerWindowResizeEvent } from '@/utils/util'
import { mapState, mapActions } from 'vuex'
import { mixin, mixinDevice } from '@/utils/mixin'
import config from '@/config/defaultSettings'

import RouteView from './RouteView'
import MultiTab from '@/components/MultiTab'
import SideMenu from '@/components/Menu/SideMenu'
import GlobalHeader from '@/components/GlobalHeader'
import GlobalFooter from '@/components/GlobalFooter'
// import SettingDrawer from '@/components/SettingDrawer'
import { getAddRouter } from '@/utils/routerUtil'
import XuHuiFooter from '@/components/XuHuiFooter/XuHuiFooter'
import Aside from './aside'
import headOfHeadNavigation from '@/layouts/headOfHeadNavigation'
import { Session } from '@/utils/storage'
export default {
  name: 'BasicLayout',
  mixins: [mixin, mixinDevice],
  components: {
    RouteView,
    MultiTab,
    SideMenu,
    GlobalHeader,
    GlobalFooter,
    XuHuiFooter,
    Aside,
    headOfHeadNavigation,
    // SettingDrawer
  },
  data() {
    return {
      production: config.production,
      collapsed: false,
      menus: [],
      showCrumbs: '',
      isTeacher: false,
      isStudent: '',
      showTab: '',
      // 版权声明是否显示
      faIconShow: false,
      // aLayoutContentStyle: {
      //   height: '100%',
      //   margin: '12px 20px 0',
      //   paddingTop: fixedHeader ? '64px' : '0',
      // },
      routeList:Object.freeze([
        '/Home/Introduce',
        '/threeAssistants/studySituationAnalysis',
        '/threeAssistants/highFrequencyErrors',
        '/threeAssistants/workResources',
        '/threeAssistants/newJobResources/index',
        '/giveLessons/index',
        '/Teacher/My_Task/newHomepage',
        '/Paper/Exam_MicroLesson/components/lessonPreparationResources'
      ]),
      teachingRouteList:Object.freeze([
        '/teaching-modules',
        '/teaching-modules/split-screen',
        '/teaching-modules/classReport',
        '/teaching-modules/lessonReport'])
    }
  },
  computed: {
    ...mapState({
      // 动态主路由
      mainMenu: (state) => getAddRouter(),
    }),
    layoutStyle() {
      return this.$store.state.user.isAside
        ? {
            height: 'calc(100vh - 49px)',
          }
        : {
            height: ' 100vh',
          }
    },
    layoutUseBlack() {
      return this.$store.state.UseBlack
    },
    layoutPadding() {
      let obj = {}
      if (this.$store.state.user.isAside && this.$store.state.user.showPadding) obj.padding = '12px'
      else obj.padding = 0
      if (this.$store.state.UseBlack) obj.background = 'rgba(240, 242, 245)'
      else obj.background = 'rgba(240, 242, 245)'
      return obj
    },
    // contentPaddingLeft () {
    //   if (!this.fixSidebar || this.isMobile()) {
    //     return '0'
    //   }
    //   if (this.sidebarOpened) {
    //     return '200px'
    //   }
    //   // if (this.sidebarOpened && this.isStudent === '1') {
    //   //   return '0'
    //   // }
    //   return '80px'
    // }
    // 三个助手作业讲评界面
    teachingRou(){
      return this.teachingRouteList.includes(this.$route.path)
    },
    // 三个助手样式
    returnStyle(){
      // return this.routeList.includes(this.$route.path) ? { height: 'calc(100vh - 117px)' } : { height: 'calc(100vh - 50px)' }
      return this.routeList.includes(this.$route.path) ? { height: 'calc(100vh - 103px)' } : { height: 'calc(100vh - 50px)' }

    },
    path(){
      return this.$route.path
    }
  },
  watch: {
    sidebarOpened(val) {
      this.collapsed = !val
    },
  },
  created() {
    this.showTab = localStorage.getItem('role')
    this.isStudent = localStorage.getItem('isStudent')
    this.isTeacher = localStorage.getItem('isTeacher')
    this.menus = this.mainMenu.find((item) => item.path === '/').children
    this.collapsed = !this.sidebarOpened
    // this.showCrumbs = localStorage.getItem('UserId')
    this.showCrumbs = Session.get('userInfo').RoleNames
    // // 是否不再自动提示版权声明
    // if (localStorage.getItem('copyrightNotice')) {
    //   this.faIconShow = false
    // } else {
    //   if (localStorage.getItem('IsTY_GetOperatorInfo') == 'true') {
    //     this.faIconShow = true
    //   } else {
    //     return false
    //   }
    // }
  },
  mounted() {
    const userAgent = navigator.userAgent
    if (userAgent.indexOf('Edge') > -1) {
      this.$nextTick(() => {
        this.collapsed = !this.collapsed
        setTimeout(() => {
          this.collapsed = !this.collapsed
        }, 16)
      })
    }
  },
  methods: {
    // 获取用户信息
    // getUserInfo () {
    //   this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.mobilephone }).then(res => {
    //     this.isTeacher = res.Data.Roles.some(item => {
    //       return item.RoleName === '教师'
    //     })
    //   })
    // },
    ...mapActions(['setSidebar']),
    toggle() {
      this.collapsed = !this.collapsed
      this.setSidebar(!this.collapsed)
      triggerWindowResizeEvent()
    },
    paddingCalc() {
      let left = ''
      if (this.sidebarOpened) {
        left = this.isDesktop() ? '200px' : '80px'
      } else {
        left = (this.isMobile() && '0') || (this.fixSidebar && '80px') || '0'
      }
      return left
    },
    menuSelect() {
      if (!this.isDesktop()) {
        this.collapsed = false
      }
    },
    drawerClose() {
      this.collapsed = false
    },
    // 关闭版权声明
    elIConBtn() {
      this.faIconShow = false
    },
    // 显示版权声明
    trueShow(val) {
      this.faIconShow = val
    },
  },
}
</script>

<style lang="less">
@import url('../components/global.less');
.faIcon {
  width: 100%;
  height: 100%;
  z-index: 99;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.4);
  div {
    width: 32%;
    padding: 30px 20px;
    border-radius: 6px;
    background-color: #ffffff;
    position: relative;
    h1 {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
    }
    p {
      text-indent: 36px;
      letter-spacing: 2px;
      font-size: 16px;
      margin: 10px;
    }
    .elIconButton {
      font-size: 20px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}
/*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */
.ha {
  padding-left: 200px;
}
.footer-left {
  padding-right: 190px;
}
.ant-layout-footer {
  margin-left: -200px;
}
.page-transition-enter {
  opacity: 0;
}
.ant-layout-content {
  // padding-right: 190px;
  /deep/.content {
    height: 100%;
  }
  /deep/.page-header-index-wide {
    height: 100%;
  }
  // div:nth-of-type(2) {
  //   height: 100%;
  // }
}
.page-transition-leave-active {
  opacity: 0;
}

.page-transition-enter .page-transition-container,
.page-transition-leave-active .page-transition-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

:global(.ant-layout-footer) {
  background-color: rgba(240, 242, 245) !important;
}
.UseBlack {
  background: black;
}
</style>
