<template>
  <div class="aside-container">
    <ul class="menu-wraaper" style="width: 60px;">
      <el-scrollbar style="height: 100%;" class="_scrollbar">
        <li v-for="(item,index) in menuList" @click="nextPath(item)" :key="index" class="menu-item" style="min-height: 50px;padding-top: 15px;">
          <i class="iconfont" :class="item.icon" style="font-size: 22px; line-height: 22px;
          margin-bottom: 3px;"></i>
          <span style=" font-size: 12px;font-weight: 500;">{{ item.name }}</span>
        </li>
      </el-scrollbar>
    </ul>
  </div>
</template>
<script>
export default {
  data () {
    return {
      menuList:[
        {
          name:'首页',
          url:'/Home/Introduce',
          icon:'icon-shouye1'
        },
        {
          name:'备课',
          url:'/prepareLessons/ppt/index',
          icon:'icon-beike'
        },
        {
          name:'教学',
          url:'/giveLessons/index',
          icon:'icon-jiaoxue'
        },
        {
          name:'作业',
          url:'/Teacher/My_Task/newHomepage',
          icon:'icon-zuoye'
        },
        {
          name:'错题',
          url:'/Teacher/My_Task/HighWrong/HighWrong',
          icon:'icon-cuoti'
        },
        {
          name:'学情',
          // url:'/Teacher/TeachingEvaluation/StudyAnalysis',
          url:'/analysis-module',
          icon:'icon-fenxi'
        },
        {
          name:'资源',
          // url:'/Paper/Exam_MicroLesson/MicroVideo',
          url:'/Paper/Exam_MicroLesson/QuestionBankResourceCenter',
          icon:'icon-ziyuan-xianxing'
        },
        {
          name:'管理',
          url:'/Teacher/My_Class/StudentManage',
          icon:'icon-shezhi'
        },
       
      ]
    }
  },
  computed:{
    schoolType(){
      return this.$store.state.user.schoolType
    }
  },
  watch:{
    schoolType:{
      handler(newVal,oldVal){
        const index = this.menuList.findIndex(item=>item.name == '学情')
        this.menuList[index].url = newVal == 2 ? '/analysis-module' :'/Teacher/TeachingEvaluation/StudyAnalysis'
      },
      immediate:true
    }
  },
  methods:{
    nextPath(item){
      const {url} = item
      if(url){
        this.$router.replace({
          path:url
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.el-scrollbar_wrap{
  overflow-x: hidden;
}
.aside-container{
  height: 100%;
  background-color: #1B2339;
  .menu-wraaper{
    width: 60px;
    height: 100%;
    .menu-item{
      width: 100%;
      min-height: 50px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 20px;
      cursor: pointer;
      &:hover{
        .iconfont,span{
          color: #6BCDA2;
        }
      }
      .iconfont{
        font-size: 25px;
        margin-bottom: 5px;
        color: #A3A7AF;
        line-height: 25px;
      }
      span{
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #A3A7AF;
      }
    }
  }
}
</style>
<!-- <script lang="less">
.layout.ant-layout{
  &.ant-layout-has-sider {
    flex-direction: row;
  }
}
</script> -->