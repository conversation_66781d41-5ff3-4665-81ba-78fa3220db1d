<template>
  <transition name="showHeader">
    <div v-if="visible" class="header-animat">
      <a-layout-header
        v-if="visible"
        style="padding: 0;height: 49px;"
        :class="[
          fixedHeader && 'ant-header-fixedHeader',
          sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed',
        ]"
        >
        <div v-if="mode === 'sidemenu'" class="header displayb" style="height: 49px;padding: 0 5px;">
          <div class="flex flex_align_center">
            <!-- <a-icon v-if="device==='mobile'" class="trigger" :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click="toggle"/> -->
            <!-- <a-icon v-else class="trigger" :type="collapsed ? 'menu-unfold' : 'menu-fold'" @click="toggle"/> -->
            <img v-if="showxhLogo" class="title-img" src="@/assets/学院logo改1.png" alt="" />
            <img v-if="showhpLogo" class="title-img" src="@/assets/hplogo.png" alt="" />
            <img v-if="showhptogo" src="@/assets/logo/pt.png" alt="" />
            <img v-if="showhSjLogo" src="@/assets/logo/sj-sx.png" alt="" />
            <img v-if="showhCnLogo" class="cn-img" src="@/assets/logo/cning.png" alt="" />
            <img v-if="showhBsLogo" class="bs-img" src="@/assets/logo/bs.png" alt="" />

            <img class="m-l" style="width: 21px" v-if="showhYplogo" src="@/assets/logo/yp-logo.png" alt="" />
            <!-- 页面标题 -->
            <span class="index-title">
              <webName :realmName="realmName" :id="1"></webName>&nbsp;<span v-show="this.showYpJy !== 1" class="spot"></span>&nbsp;
              <!-- <span v-show="this.showYpJy !== 1">打造一流教育</span> -->
              <span class="m-r" v-show="this.showYpJy === 1">精准练习 提质增效</span>
            </span>
            <!-- 三个助手 -->
            <div class="threeAssistant" v-if="$store.state.LoginSource != 1">
              <div class="one margin_lr_10" @click="tabsClick">
                <img src="@/assets/beike.png" />
                备课助手
              </div>
              <div class="ther" @click="tabsClick">
                <img src="@/assets/jiaoxue.png" />
                教学助手
              </div>
            </div>
          </div>
          <div class="iconBtn" v-if="$store.state.LoginSource == 1">  <!-- 专科专练header -->
<!--            {{role}}-->
            <ManageMenu v-if="this.role === 4"></ManageMenu>
            <user-menu v-if="this.role === 2 || this.role === 9999 || this.role === 9 || this.role === 8"></user-menu>
            <StudentMenu v-if="this.isStudent === '1'"></StudentMenu>
            <!-- <user-menu v-if="admin"></user-menu> -->
            <!--    role:（20教研组长）        -->
            <school-head v-if="this.role === 3 || role === 20"></school-head>
            <cityLeader v-if="this.role === 5"></cityLeader>
            <div v-show="isTy == 'true'" @click="imgBtn">
              <img src="@/assets/student/faIcon.png"/>
            </div>
          </div>
          <div class="iconBtn" v-else> <!-- 三个助手header -->
            <user-menu></user-menu>
            <div v-show="isTy == 'true'" @click="imgBtn">
              <img src="@/assets/student/faIcon.png"/>
            </div>
          </div>
          <!-- <StudentMenu else></StudentMenu> -->
        </div>
        <div v-else :class="['top-nav-header-index', theme]">
          <div class="header-index-wide">
            <div class="header-index-left">
              <logo class="top-nav-header" :show-title="device !== 'mobile'" />
              <s-menu v-if="device !== 'mobile'" mode="horizontal" :menu="menus" :theme="theme" />
              <a-icon v-else class="trigger" :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click="toggle" />
            </div>
            <user-menu class="header-index-right"></user-menu>
          </div>
        </div>
      </a-layout-header>
    </div>
  </transition>
</template>

<script>
import schoolHead from '../tools/schoolHeader'
import UserMenu from '../tools/UserMenu'
import StudentMenu from '../tools/StudentMenu'
import ManageMenu from '../tools/ManageMenu'
import SMenu from '../Menu/'
import Logo from '../tools/Logo'
import { mixin } from '@/utils/mixin'
import webName from '@/components/WebsiteName/WebsiteName'
import cityLeader from '../tools/cityLeadersMenu'
export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    schoolHead,
    Logo,
    StudentMenu,
    webName,
    ManageMenu,
    cityLeader
  },
  mixins: [mixin],
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'sidemenu'
    },
    menus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  created () {
    this.mobilephone = localStorage.getItem('mobilePhone')
    this.isStudent = localStorage.getItem('isStudent')
    this.isTy = localStorage.getItem('IsTY_GetOperatorInfo')
    // this.isTeacher = localStorage.getItem('isTeacher')
    // this.isSchoolHeader = localStorage.getItem('isSchoolHeader')
    this.getUserInfo()
    this.realmName = window.location.href.split('/')[2]
    if (this.realmName === this.Facturl.$hrefName) {
      this.showxhLogo = true
    } else if (this.realmName === this.Facturl.$hrefHpName) {
      this.showhpLogo = true
    } else if (this.realmName === this.Facturl.$hrefPtName) {
      this.showhptogo = true
    } else if (this.realmName === this.Facturl.$hrefSjName) {
      this.showhSjLogo = true
    } else if (this.realmName === this.Facturl.$hreYpaName) {
      this.showhYplogo = true
      this.showYpJy = 1
    } else if (this.realmName === this.Facturl.$hrefCnName) {
      this.showhCnLogo = true
    } else if (this.realmName === this.Facturl.$hrefBsName) {
      this.showhBsLogo = true
    } else {
      this.showYpJy = 0
      this.showhpLogo = false
    }
  },
  data () {
    return {
      showYpJy: 0,
      showhYplogo: false,
      showhSjLogo: false,
      showhptogo: false,
      showhpLogo: false,
      showxhLogo: false,
      showhCnLogo: false,
      showhBsLogo: false,
      realmName: '',
      visible: true,
      oldScrollTop: 0,
      isStudent: '',
      isTeacher: false,
      mobilePhone: '',
      isSchoolHeader: false,
      role: '',
      admin: false,
      isTy: '' // 判断当前用户是否为市转型学校
    }
  },
  mounted () {
    document.addEventListener('scroll', this.handleScroll, { passive: true })
  },
  methods: {
    tabsClick() {
      this.$uwonhttp.post('/Period_Subject/Period_Subject/GetSubjectListByUser', {
        UserId: localStorage.getItem('UserId')
      }).then(res => {
        if(res.data.Success) {
          const jx = res.data.Data.find((item) => item.Id == localStorage.getItem('UW_SUBJECT')).JX
          const url = `https://dolearning.net/go/prepare?eden_token=${sessionStorage.getItem('sourceToken')}&subject=${jx}&stage=${localStorage.getItem('STAGE')}`
          window.open(url, '_blank')
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    },
    // 获取用户信息
    getUserInfo () {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.mobilephone }).then(res => {
        // console.log('###########################*************************')
        import(`@/style/${res.Data.LoginSource==1 ? 'common': 'newCommon'}.less`).then(()=>{
          console.log('less执行成功')
        })
        const identityId = localStorage.getItem('role')
        const roles = res.Data.Roles
        if (!res.Data.Roles) {
          this.isStudent = '1'
        } else {
          roles.forEach(value => {
            if (value.Id === identityId) {
              this.role = value.Level
            }
          })
        }
        if (res.Data.User.UserName === 'Admin') {
          this.admin = true
        }
      })
    },
    handleScroll () {
      if (!this.autoHideHeader) {
        return
      }
      const scrollTop = document.body.scrollTop + document.documentElement.scrollTop
      if (!this.ticking) {
        this.ticking = true
        requestAnimationFrame(() => {
          if (this.oldScrollTop > scrollTop) {
            this.visible = true
          } else if (scrollTop > 300 && this.visible) {
            this.visible = false
          } else if (scrollTop < 300 && !this.visible) {
            this.visible = true
          }
          this.oldScrollTop = scrollTop
          this.ticking = false
        })
      }
    },
    toggle () {
      this.$emit('toggle')
    },
    // 向父组件传值显示法律声明
    imgBtn () {
      this.$emit('trueShow', true)
    }
  },
  beforeDestroy () {
    document.body.removeEventListener('scroll', this.handleScroll, true)
  }
}
</script>

<style lang="less">
.m-r {
  margin-left: 30px;
}
@import '../index.less';
// 添加法律声明调整样式
.displayb{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .iconBtn{
    display: flex;
    align-items: center;
    img{
      width: 36px;
      margin-left: 10px;
    }
  }
}
.title-img {
  margin-top: -5px;
  margin-left: 15px;
  vertical-align: middle;
}
.index-title {
  font-size: 26px;
  margin-left: 5px;
}
.m-l {
  margin-left: 15px;
  vertical-align: middle;
}
.spot {
  display: none;
  // display: inline-block;
  position: relative;
  top: -3px;
  width: 4px;
  height: 4px;
  border-radius: 4px;
  background-color: #fff;
}
.header-animat {
  position: relative;
  z-index: @ant-global-header-zindex;
}
.showHeader-enter-active {
  transition: all 0.25s ease;
}
.showHeader-leave-active {
  transition: all 0.5s ease;
}
.showHeader-enter,
.showHeader-leave-to {
  opacity: 0;
}
// 右侧头部基础样式

.ant-layout .header {
  // margin-left: -200px;
  // padding-left: 50px !important;
  background-color: #537566 !important;
  color: #fff !important;
}
.sider .logo {
  background-color: #537566 !important;
}
.cn-img {
  width: 55px;
  height: 50px;
  position: relative;
  bottom: 5px;
}
.bs-img {
  width: 50px;
  bottom: 5px;
  height: 52px;
  position: relative;
}
.threeAssistant{
  display: flex;
  align-items: center;
}
</style>
