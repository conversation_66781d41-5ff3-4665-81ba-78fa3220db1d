<template>
  <span class="font-class" :class="{ 'f-s': showYpJy === 1 }">{{ webName }}</span>
</template>
<script>
export default {
  name: 'WebsiteName',
  props: {
    realmName: {
      type: String,
      required: true,
    },
    id: {
      type: Number,
      required: false,
    },
  },
  created() {
    this.realmName = window.location.href.split('/')[2]

    if (this.id === 1) {
      if (this.realmName === this.Facturl.$hrefName) {
        this.webName = '专课专练在线作业分析系统' //徐汇数学网校
      } else if (this.realmName === this.Facturl.$hrefHpName) {
        this.webName = '黄浦专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefPtName) {
        this.webName = '普陀专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefPdName) {
        this.webName = '浦东专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefCnName) {
        this.webName = '长宁专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefjaName) {
        this.webName = '静安专课专练网校'
      } else if (this.realmName === this.Facturl.$hreHkaName) {
        this.webName = '虹口专课专练网校'
      } else if (this.realmName === this.Facturl.$hreYpaName) {
        this.webName = '上海市杨浦区教育学院'
      } else if (this.realmName === this.Facturl.$hreMhaName) {
        this.webName = '闵行专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefBsName) {
        this.webName = '宝山专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefJdName) {
        this.webName = '嘉定专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefJsName) {
        this.webName = '金山专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefSjName) {
        this.webName = '松江专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefQpName) {
        this.webName = '青浦专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefFxName) {
        this.webName = '奉贤专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefCmName) {
        this.webName = '崇明专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefStName) {
        // this.webName = '上海市中小学数字教学服务平台(试用版)-作业辅导助手'
        this.webName = '上海市中小学数字教学系统（试用版）'
      } else if (this.realmName === this.Facturl.$hrefZyName) {
        // this.webName = '上海市中小学数字教学服务平台(试用版)-作业辅导助手'
        this.webName = '上海市中小学数字教学系统（试用版）'
      } else {
        this.webName = '专课专练在线作业分析系统'
      }
    } else {
      if (this.realmName === this.Facturl.$hrefName) {
        this.webName = '徐汇专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefHpName) {
        this.webName = '黄浦专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefPtName) {
        this.webName = '普陀专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefPdName) {
        this.webName = '浦东专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefCnName) {
        this.webName = '长宁专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefjaName) {
        this.webName = '静安专课专练网校'
      } else if (this.realmName === this.Facturl.$hreHkaName) {
        this.webName = '虹口专课专练网校'
      } else if (this.realmName === this.Facturl.$hreYpaName) {
        this.webName = '上海市杨浦区教育学院'
      } else if (this.realmName === this.Facturl.$hreMhaName) {
        this.webName = '闵行专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefBsName) {
        this.webName = '宝山专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefJdName) {
        this.webName = '嘉定专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefJsName) {
        this.webName = '金山专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefSjName) {
        this.webName = '松江专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefQpName) {
        this.webName = '青浦专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefFxName) {
        this.webName = '奉贤专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefCmName) {
        this.webName = '崇明专课专练网校'
      } else if (this.realmName === this.Facturl.$hrefStName) {
        // this.webName = '上海市中小学数字教学服务平台(试用版)-作业辅导助手'
        this.webName = '上海市中小学数字教学系统（试用版）'
      } else if (this.realmName === this.Facturl.$hrefZyName) {
        // this.webName = '上海市中小学数字教学服务平台(试用版)-作业辅导助手'
        this.webName = '上海市中小学数字教学系统（试用版）'
      } else {
        this.webName = '专课专练在线作业分析系统'
      }
    }
    if (this.realmName === this.Facturl.$hreYpaName) {
      this.showYpJy = 1
    } else {
      this.showYpJy = 0
    }
    // this.webName = '徐汇数学网校'
  },
  data() {
    return {
      webName: '徐汇专课专练网校',
      showYpJy: 0,
    }
  },
}
</script>

<style lang="less" scoped>
.f-s {
  font-size: 10px;
}
</style>
