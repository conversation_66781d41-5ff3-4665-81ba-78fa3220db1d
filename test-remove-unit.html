<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除节点功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 600px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .chapter-node {
            margin-left: 20px;
            margin-bottom: 10px;
        }
        .node-row {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
            background: #f6faf7;
            border: 1px solid #e0e0e0;
        }
        .node-row:hover {
            background: #eaf7f0;
        }
        .expand-arrow {
            cursor: pointer;
            margin-right: 8px;
            width: 16px;
            color: #666;
        }
        .node-name {
            flex: 1;
            margin-left: 8px;
            font-weight: 500;
        }
        .node-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .action-btn {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        .action-btn:hover {
            background: #f0f0f0;
        }
        .add-btn {
            color: #67c23a;
            border-color: #67c23a;
        }
        .edit-btn {
            color: #409eff;
            border-color: #409eff;
        }
        .delete-btn {
            color: #f56c6c;
            border-color: #f56c6c;
        }
        .children {
            margin-left: 24px;
            margin-top: 8px;
        }
        .level-indicator {
            font-size: 12px;
            color: #999;
            margin-right: 8px;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
        }
        .test-controls button {
            margin-right: 10px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .primary-btn {
            background: #409eff;
            color: white;
        }
        .success-btn {
            background: #67c23a;
            color: white;
        }
        .log-area {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-item {
            margin-bottom: 5px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h2>删除节点功能测试</h2>
        
        <div class="test-controls">
            <button class="primary-btn" onclick="addTestData()">添加测试数据</button>
            <button class="success-btn" onclick="clearAll()">清空所有</button>
            <button onclick="showStructure()">显示结构</button>
        </div>
        
        <div id="chapterTree">
            <!-- 动态生成的章节树 -->
        </div>
        
        <div class="log-area">
            <h4>操作日志：</h4>
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        let chapterList = [
            {
                id: 1,
                name: '第一章 基础知识',
                expand: true,
                children: [
                    {
                        id: 2,
                        name: '1.1 概念介绍',
                        expand: true,
                        children: [
                            { id: 3, name: '1.1.1 基本概念', expand: false, children: [] },
                            { id: 4, name: '1.1.2 重要定理', expand: false, children: [] }
                        ]
                    },
                    {
                        id: 5,
                        name: '1.2 实践应用',
                        expand: false,
                        children: [
                            { id: 6, name: '1.2.1 案例分析', expand: false, children: [] }
                        ]
                    }
                ]
            },
            {
                id: 7,
                name: '第二章 进阶内容',
                expand: false,
                children: [
                    { id: 8, name: '2.1 高级概念', expand: false, children: [] }
                ]
            }
        ];
        
        let nextId = 9;
        
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function renderTree() {
            const container = document.getElementById('chapterTree');
            container.innerHTML = '';
            
            chapterList.forEach(chapter => {
                container.appendChild(renderNode(chapter, null, 0));
            });
        }
        
        function renderNode(node, parent, level) {
            const nodeDiv = document.createElement('div');
            nodeDiv.className = 'chapter-node';
            
            const nodeRow = document.createElement('div');
            nodeRow.className = 'node-row';
            
            // 展开/收起图标
            const expandIcon = document.createElement('span');
            expandIcon.className = 'expand-arrow';
            if (node.children && node.children.length > 0) {
                expandIcon.innerHTML = node.expand ? '▼' : '▶';
                expandIcon.onclick = () => toggleExpand(node);
            } else {
                expandIcon.innerHTML = '&nbsp;';
            }
            
            // 级别指示器
            const levelIndicator = document.createElement('span');
            levelIndicator.className = 'level-indicator';
            levelIndicator.textContent = `L${level + 1}`;
            
            // 节点名称
            const nodeName = document.createElement('span');
            nodeName.className = 'node-name';
            nodeName.textContent = node.name;
            
            // 操作按钮
            const actions = document.createElement('div');
            actions.className = 'node-actions';
            
            const addBtn = document.createElement('button');
            addBtn.className = 'action-btn add-btn';
            addBtn.textContent = '添加子节点';
            addBtn.onclick = () => addChild(node);
            
            const editBtn = document.createElement('button');
            editBtn.className = 'action-btn edit-btn';
            editBtn.textContent = '编辑';
            editBtn.onclick = () => editNode(node);
            
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'action-btn delete-btn';
            deleteBtn.textContent = '删除';
            deleteBtn.onclick = () => removeUnit(parent, node);
            
            actions.appendChild(addBtn);
            actions.appendChild(editBtn);
            actions.appendChild(deleteBtn);
            
            nodeRow.appendChild(expandIcon);
            nodeRow.appendChild(levelIndicator);
            nodeRow.appendChild(nodeName);
            nodeRow.appendChild(actions);
            
            nodeDiv.appendChild(nodeRow);
            
            // 子节点
            if (node.children && node.children.length > 0 && node.expand) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'children';
                
                node.children.forEach(child => {
                    childrenDiv.appendChild(renderNode(child, node, level + 1));
                });
                
                nodeDiv.appendChild(childrenDiv);
            }
            
            return nodeDiv;
        }
        
        function removeUnit(parent, node) {
            log(`删除节点: parent=${parent?.name || 'null'}, node=${node.name}`);
            
            if (confirm(`确定要删除"${node.name}"吗？`)) {
                if (!parent) {
                    // 删除顶级节点
                    const idx = chapterList.indexOf(node);
                    if (idx > -1) {
                        chapterList.splice(idx, 1);
                        log(`已删除顶级节点"${node.name}"`);
                    } else {
                        log('未找到要删除的顶级节点');
                    }
                } else {
                    // 删除子节点
                    if (!parent.children) {
                        parent.children = [];
                    }
                    const idx = parent.children.indexOf(node);
                    if (idx > -1) {
                        parent.children.splice(idx, 1);
                        log(`已从"${parent.name}"中删除子节点"${node.name}"`);
                    } else {
                        log('未找到要删除的子节点');
                    }
                }
                renderTree();
            } else {
                log('用户取消删除操作');
            }
        }
        
        function toggleExpand(node) {
            node.expand = !node.expand;
            renderTree();
        }
        
        function addChild(parent) {
            const name = prompt('请输入子节点名称:');
            if (name) {
                if (!parent.children) {
                    parent.children = [];
                }
                parent.children.push({
                    id: nextId++,
                    name: name,
                    expand: false,
                    children: []
                });
                parent.expand = true;
                log(`为"${parent.name}"添加了子节点"${name}"`);
                renderTree();
            }
        }
        
        function editNode(node) {
            const newName = prompt('请输入新名称:', node.name);
            if (newName && newName !== node.name) {
                const oldName = node.name;
                node.name = newName;
                log(`节点"${oldName}"重命名为"${newName}"`);
                renderTree();
            }
        }
        
        function addTestData() {
            chapterList = [
                {
                    id: 1,
                    name: '第一章 基础知识',
                    expand: true,
                    children: [
                        {
                            id: 2,
                            name: '1.1 概念介绍',
                            expand: true,
                            children: [
                                { id: 3, name: '1.1.1 基本概念', expand: false, children: [] },
                                { id: 4, name: '1.1.2 重要定理', expand: false, children: [] }
                            ]
                        },
                        {
                            id: 5,
                            name: '1.2 实践应用',
                            expand: false,
                            children: [
                                { id: 6, name: '1.2.1 案例分析', expand: false, children: [] }
                            ]
                        }
                    ]
                },
                {
                    id: 7,
                    name: '第二章 进阶内容',
                    expand: false,
                    children: [
                        { id: 8, name: '2.1 高级概念', expand: false, children: [] }
                    ]
                }
            ];
            log('已添加测试数据');
            renderTree();
        }
        
        function clearAll() {
            chapterList = [];
            log('已清空所有数据');
            renderTree();
        }
        
        function showStructure() {
            console.log('当前章节结构:', JSON.stringify(chapterList, null, 2));
            log('章节结构已输出到控制台');
        }
        
        // 初始化
        renderTree();
        log('页面初始化完成');
    </script>
</body>
</html>
